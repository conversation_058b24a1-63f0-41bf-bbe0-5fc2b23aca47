2025-08-02 13:19:09,594 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:19:09,594 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-02 13:19:09,594 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:19:09,594 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-02 13:19:09,595 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-02 13:19:09,595 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:19:09,595 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-02 13:19:14,644 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-02 13:19:14,808 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.164 s. ]
2025-08-02 13:19:14,808 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-02 13:19:14,896 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0878 s. ]
2025-08-02 13:19:14,968 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-02 13:19:15,140 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 14 s. ]
2025-08-02 13:19:15,431 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.84 s. ]
2025-08-02 13:19:15,524 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:19:15,525 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-02 13:19:15,562 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-08-02 13:19:15,562 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:19:15,562 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-02 13:19:15,568 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-08-02 13:19:15,568 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-08-02 13:19:15,568 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-08-02 13:19:15,568 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-08-02 13:19:15,568 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-02 13:19:15,568 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-08-02 13:19:15,576 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-02 13:19:15,766 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-02 13:19:15,857 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-02 13:19:16,458 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.9 s. ]
2025-08-02 13:19:16,476 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:19:16,476 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-02 13:19:16,811 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-02 13:19:16,833 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.38 s. ]
2025-08-02 13:19:16,907 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:19:16,907 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-02 13:19:16,917 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-02 13:19:16,986 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-02 13:19:17,057 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-02 13:19:17,123 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-02 13:19:17,166 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-02 13:19:17,212 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-02 13:19:17,270 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-02 13:19:17,344 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-02 13:19:17,411 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-02 13:19:17,411 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.58 s. ]
2025-08-02 13:19:17,411 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:19:17,411 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-02 13:19:17,432 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-02 13:19:17,433 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:19:17,433 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-02 13:19:17,587 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-02 13:19:17,595 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-02 13:19:17,730 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.3 s. ]
2025-08-02 13:19:17,730 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:19:17,730 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-02 13:19:17,741 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-02 13:19:17,741 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:19:17,741 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-02 13:19:21,978 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 4.24 s. ]
2025-08-02 13:19:21,979 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:19:21,979 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 12.4 s. ]
2025-08-02 13:19:21,979 [main] INFO  com.polarion.platform.startup - ****************************************************************
