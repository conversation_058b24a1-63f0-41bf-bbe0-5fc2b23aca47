2025-08-02 20:46:32,896 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 20:46:32,896 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-02 20:46:32,896 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 20:46:32,896 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-02 20:46:32,896 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-02 20:46:32,896 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 20:46:32,896 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-02 20:46:37,323 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-02 20:46:37,529 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.206 s. ]
2025-08-02 20:46:37,529 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-02 20:46:37,600 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0704 s. ]
2025-08-02 20:46:37,653 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-02 20:46:37,781 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 16 s. ]
2025-08-02 20:46:38,030 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.14 s. ]
2025-08-02 20:46:38,139 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 20:46:38,139 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-02 20:46:38,175 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.14 s. ]
2025-08-02 20:46:38,175 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 20:46:38,175 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-02 20:46:38,183 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-08-02 20:46:38,183 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-08-02 20:46:38,183 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-08-02 20:46:38,183 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-08-02 20:46:38,183 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-08-02 20:46:38,183 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-08-02 20:46:38,192 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-02 20:46:38,350 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-02 20:46:38,449 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-02 20:46:38,980 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.8 s. ]
2025-08-02 20:46:38,993 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 20:46:38,993 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-02 20:46:39,257 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-02 20:46:39,273 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.29 s. ]
2025-08-02 20:46:39,300 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 20:46:39,300 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-02 20:46:39,305 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-02 20:46:39,357 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-02 20:46:39,409 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-02 20:46:39,458 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-02 20:46:39,484 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-02 20:46:39,512 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-02 20:46:39,552 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-02 20:46:39,594 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-02 20:46:39,623 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-02 20:46:39,623 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.35 s. ]
2025-08-02 20:46:39,623 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 20:46:39,623 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-02 20:46:39,640 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-02 20:46:39,640 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 20:46:39,641 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-02 20:46:39,753 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-02 20:46:39,757 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-02 20:46:40,034 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.39 s. ]
2025-08-02 20:46:40,035 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 20:46:40,035 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-02 20:46:40,043 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-02 20:46:40,043 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 20:46:40,043 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-02 20:46:44,008 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.97 s. ]
2025-08-02 20:46:44,009 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 20:46:44,010 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11.1 s. ]
2025-08-02 20:46:44,010 [main] INFO  com.polarion.platform.startup - ****************************************************************
