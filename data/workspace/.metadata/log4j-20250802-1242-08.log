2025-08-02 12:42:08,968 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using logging context STANDALONE
2025-08-02 12:42:08,970 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Launchers manager started...
2025-08-02 12:42:08,970 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using home directory /opt/polarion/polarion
2025-08-02 12:42:08,970 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using root directory /opt/polarion
2025-08-02 12:42:08,970 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using workspace directory /opt/polarion/data/workspace
2025-08-02 12:42:08,970 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using config directory /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-08-02 12:42:08,970 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading external properties ...
2025-08-02 12:42:08,970 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using external property file /opt/polarion/etc/polarion.properties
2025-08-02 12:42:08,970 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading internal properties ...
2025-08-02 12:42:08,972 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Host: zhangwendeMini2.lan (**************)
2025-08-02 12:42:08,974 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Product: com.polarion.alm
2025-08-02 12:42:08,975 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Version: 3.22.1
2025-08-02 12:42:08,975 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Build: 20220419-1528-22_R1-be3adceb
2025-08-02 12:42:08,975 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/fasnote
2025-08-02 12:42:08,976 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/2404
2025-08-02 12:42:08,976 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Extensions: [exts]
2025-08-02 12:42:08,979 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace location: /opt/polarion/data/workspace
2025-08-02 12:42:08,979 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace lock acquired
2025-08-02 12:42:08,979 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Found applications: [polarion.server, polarion.coordinator, polarion.rt]
2025-08-02 12:42:08,979 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Starting application: polarion.server
2025-08-02 12:42:08,984 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Application extension successfully read
2025-08-02 12:42:08,988 [main] INFO  com.polarion.platform.internal.SystemStatistics - Initializing monitoring, isThreadCpuTimeSupported: true, isThreadContentionMonitoringSupported: true, isThreadAllocatedMemorySupported: true
2025-08-02 12:42:08,988 [main] INFO  com.polarion.platform.internal.SystemStatistics - State before enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-08-02 12:42:08,988 [main] INFO  com.polarion.platform.internal.SystemStatistics - State after enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-08-02 12:42:08,992 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 12:42:08,992 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-02 12:42:08,992 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 12:42:08,992 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-02 12:42:08,993 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-02 12:42:08,993 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:42:08,993 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-02 12:42:08,993 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** Java system properties listing: 
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminPasswd = admin
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminUser = admin
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.auth = false
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.host = 
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.password = **PASSWORD**HIDDEN**
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.port = 25
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.user = 
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - awt.toolkit = sun.lwawt.macosx.LWCToolkit
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - base.url = http://localhost
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - bfh.jobs.workdir = /opt/polarion/data/workspace/polarion-data/jobs
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - BIRDir = /opt/polarion/data/BIR
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - calculated.fields.mode = async
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.activationHelpLink = https://polarion.plm.automation.siemens.com/getlicense
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.server = https://license.polarion.com/licenseGenerator/generator/generate
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.enabled = false
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.url = http://www.gravatar.com/avatar/$emailHash$?d=identicon&s=50
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.application = polarion.server
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.config = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.data = /opt/polarion/data
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.eclipse = /opt/polarion/polarion
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.home = /opt/polarion/polarion
2025-08-02 12:42:09,016 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.licenseDir = /opt/polarion/polarion/license
2025-08-02 12:42:09,017 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.internalPG = polarion:polarion@localhost:5434
2025-08-02 12:42:09,017 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.disabled = true
2025-08-02 12:42:09,017 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.receivers = 
2025-08-02 12:42:09,017 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.sender = 
2025-08-02 12:42:09,017 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.subject.prefix = 
2025-08-02 12:42:09,017 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.persistence.notifications.disabled = true
2025-08-02 12:42:09,017 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.propertyFile = /opt/polarion/etc/polarion.properties
2025-08-02 12:42:09,017 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.root = /opt/polarion
2025-08-02 12:42:09,017 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.workspace = /opt/polarion/data/workspace
2025-08-02 12:42:09,017 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.collaborationNotifications.enabled = true
2025-08-02 12:42:09,017 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.document.listStyle = 1ai
2025-08-02 12:42:09,017 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.loggingContext = STANDALONE
2025-08-02 12:42:09,017 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.preview.thumbnailsDataDir = /opt/polarion/data/workspace/previews-data/thumbnails
2025-08-02 12:42:09,017 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.cors.allowedOrigins = *
2025-08-02 12:42:09,017 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.enabled = true
2025-08-02 12:42:09,017 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.swaggerUi.enabled = true
2025-08-02 12:42:09,017 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.accEndpointUrl = https://acc.collab.sws.siemens.com
2025-08-02 12:42:09,017 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.baseDomain = sws.siemens.com
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.shareEndpointUrl = https://share.sws.siemens.com
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - content.types.user.table = /opt/polarion/polarion/plugins/com.polarion.core.boot_3.22.1/content-types.properties
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlHostname = localhost
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlPort = 8887
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.location = Sandbox/
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.useUserId = true
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.limitedAccessMessage = You may create a project in the Sandbox project group (only). Please fill in the required properties below. For example:<br/><table><tr><td>Location:</td><td>Sandbox/MyFirstProject</td></tr><tr><td>ID:</td><td>MyFirstProject</td></tr></table><br/>Or use the suggested defaults.
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug = false
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.license.validation = true
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.machine.code.generation = true
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.security.validation = true
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.ALM = alm_vmodel
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Pro = alm_vmodel
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.QA = qa_vmodel
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Requirements = req_vmodel
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XBase = alm_vmodel
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XEnterprise = alm_vmodel
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XPro = alm_vmodel
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - derby.system.home = /opt/polarion/data/logs/derby
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.application = com.polarion.core.boot.app
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.commands = -application
com.polarion.core.boot.app
-data
/opt/polarion/data/workspace
-configuration
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
-dev
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
-os
linux
-ws
linux
-arch
arm64
-appId
polarion.server

2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.home.location = file:/opt/polarion/polarion/plugins/
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.p2.data.area = @config.dir/.p2
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.pde.launch = true
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.startTime = *************
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.stateSaveDelayInterval = 30000
2025-08-02 12:42:09,018 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - enableCreateAccountForm = false
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - equinox.init.uuid = true
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - error.report.email = 
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.encoding = UTF-8
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.separator = /
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ftp.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gopherProxySet = false
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gosh.args = --nointeractive
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - htpasswd.path = htpasswd
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - http.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - index.activities = /opt/polarion/data/workspace/polarion-data/index
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.graphicsenv = sun.awt.CGraphicsEnvironment
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.headless = true
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.printerjob = sun.lwawt.macosx.CPrinterJob
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.path = /opt/polarion/polarion/plugins/org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.version = 55.0
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.home = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.io.tmpdir = /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.library.path = /Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.name = OpenJDK Runtime Environment
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.version = 11.0.27+6-LTS
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.security.policy = /opt/polarion/polarion/policy
2025-08-02 12:42:09,019 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.maintenance.version = 3
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.name = Java Platform API Specification
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.vendor = Oracle Corporation
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.version = 11
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor = Microsoft
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url = https://www.microsoft.com
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url.bug = https://github.com/microsoft/openjdk/issues
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.version = Microsoft-11367290
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version = 11.0.27
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version.date = 2025-04-15
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.compressedOopsMode = Zero based
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.info = mixed mode
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.name = OpenJDK 64-Bit Server VM
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.name = Java Virtual Machine Specification
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.vendor = Oracle Corporation
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.version = 11
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.vendor = Microsoft
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.version = 11.0.27+6-LTS
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - javasvn.timeout = 10000
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - jdk.debug = release
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ldap.bind.password = **PASSWORD**HIDDEN**
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.audit.enabled = true
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.auto.scan.enabled = true
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.size = 100
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.ttl = 1800
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.check.interval = 0
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.expired = true
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.local.files = true
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.features = all
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.max.users = 10
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.plugin.id = com.fasnote.alm.plugin.manage
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.mode = true
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.show.machine.code = true
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.machine.binding = true
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.network.validation = true
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.directory = dev-licenses
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.encryption.enabled = false
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.hot.reload.enabled = true
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.log.level = DEBUG
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.machine.binding.enabled = false
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.max.plugins = 1000
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.scan.interval = 60
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.signature.validation.enabled = false
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.validation.timeout = 1000
2025-08-02 12:42:09,020 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - licenseForNewUserAccount = 
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - line.separator = 

2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.contextSelector = org.apache.logging.log4j.core.selector.BasicContextSelector
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.loggerContextFactory = org.apache.logging.log4j.core.impl.Log4jContextFactory
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - logDir = /opt/polarion/data/workspace/.metadata/
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - login = polarion
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - mavenConfigDir = /opt/polarion/polarion/../maven
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - minimalPasswordLength = **PASSWORD**HIDDEN**
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.equinox.simpleconfigurator.configUrl = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/org.eclipse.equinox.simpleconfigurator/bundles.info
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.lyo.oslc4j.strictDatatypes = false
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.executionenvironment = OSGi/Minimum-1.0, OSGi/Minimum-1.1, OSGi/Minimum-1.2, JavaSE/compact1-1.8, JavaSE/compact2-1.8, JavaSE/compact3-1.8, JRE-1.1, J2SE-1.2, J2SE-1.3, J2SE-1.4, J2SE-1.5, JavaSE-1.6, JavaSE-1.7, JavaSE-1.8, JavaSE-9, JavaSE-10, JavaSE-11
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.language = zh
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.name = MacOSX
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.version = 15.5.0
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.processor = aarch64
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.storage = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.capabilities = osgi.ee; osgi.ee="OSGi/Minimum"; version:List<Version>="1.0, 1.1, 1.2", osgi.ee; osgi.ee="JRE"; version:List<Version>="1.0, 1.1", osgi.ee; osgi.ee="JavaSE"; version:List<Version>="1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact1"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact2"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact3"; version:List<Version>="1.8, 9.0, 10.0, 11.0"
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.packages = com.sun.jarsigner, com.sun.java.accessibility.util, com.sun.javadoc, com.sun.jdi, com.sun.jdi.connect, com.sun.jdi.connect.spi, com.sun.jdi.event, com.sun.jdi.request, com.sun.jndi.ldap.spi, com.sun.management, com.sun.net.httpserver, com.sun.net.httpserver.spi, com.sun.nio.file, com.sun.nio.sctp, com.sun.security.auth, com.sun.security.auth.callback, com.sun.security.auth.login, com.sun.security.auth.module, com.sun.security.jgss, com.sun.source.doctree, com.sun.source.tree, com.sun.source.util, com.sun.tools.attach, com.sun.tools.attach.spi, com.sun.tools.javac, com.sun.tools.javadoc, com.sun.tools.jconsole, java.applet, java.awt, java.awt.color, java.awt.datatransfer, java.awt.desktop, java.awt.dnd, java.awt.event, java.awt.font, java.awt.geom, java.awt.im, java.awt.im.spi, java.awt.image, java.awt.image.renderable, java.awt.print, java.beans, java.beans.beancontext, java.io, java.lang, java.lang.annotation, java.lang.instrument, java.lang.invoke, java.lang.management, java.lang.module, java.lang.ref, java.lang.reflect, java.math, java.net, java.net.http, java.net.spi, java.nio, java.nio.channels, java.nio.channels.spi, java.nio.charset, java.nio.charset.spi, java.nio.file, java.nio.file.attribute, java.nio.file.spi, java.rmi, java.rmi.activation, java.rmi.dgc, java.rmi.registry, java.rmi.server, java.security, java.security.acl, java.security.cert, java.security.interfaces, java.security.spec, java.sql, java.text, java.text.spi, java.time, java.time.chrono, java.time.format, java.time.temporal, java.time.zone, java.util, java.util.concurrent, java.util.concurrent.atomic, java.util.concurrent.locks, java.util.function, java.util.jar, java.util.logging, java.util.prefs, java.util.regex, java.util.spi, java.util.stream, java.util.zip, javax.accessibility, javax.annotation.processing, javax.crypto, javax.crypto.interfaces, javax.crypto.spec, javax.imageio, javax.imageio.event, javax.imageio.metadata, javax.imageio.plugins.bmp, javax.imageio.plugins.jpeg, javax.imageio.plugins.tiff, javax.imageio.spi, javax.imageio.stream, javax.lang.model, javax.lang.model.element, javax.lang.model.type, javax.lang.model.util, javax.management, javax.management.loading, javax.management.modelmbean, javax.management.monitor, javax.management.openmbean, javax.management.relation, javax.management.remote, javax.management.remote.rmi, javax.management.timer, javax.naming, javax.naming.directory, javax.naming.event, javax.naming.ldap, javax.naming.spi, javax.net, javax.net.ssl, javax.print, javax.print.attribute, javax.print.attribute.standard, javax.print.event, javax.rmi.ssl, javax.script, javax.security.auth, javax.security.auth.callback, javax.security.auth.kerberos, javax.security.auth.login, javax.security.auth.spi, javax.security.auth.x500, javax.security.cert, javax.security.sasl, javax.smartcardio, javax.sound.midi, javax.sound.midi.spi, javax.sound.sampled, javax.sound.sampled.spi, javax.sql, javax.sql.rowset, javax.sql.rowset.serial, javax.sql.rowset.spi, javax.swing, javax.swing.border, javax.swing.colorchooser, javax.swing.event, javax.swing.filechooser, javax.swing.plaf, javax.swing.plaf.basic, javax.swing.plaf.metal, javax.swing.plaf.multi, javax.swing.plaf.nimbus, javax.swing.plaf.synth, javax.swing.table, javax.swing.text, javax.swing.text.html, javax.swing.text.html.parser, javax.swing.text.rtf, javax.swing.tree, javax.swing.undo, javax.tools, javax.transaction.xa, javax.xml, javax.xml.catalog, javax.xml.crypto, javax.xml.crypto.dom, javax.xml.crypto.dsig, javax.xml.crypto.dsig.dom, javax.xml.crypto.dsig.keyinfo, javax.xml.crypto.dsig.spec, javax.xml.datatype, javax.xml.namespace, javax.xml.parsers, javax.xml.stream, javax.xml.stream.events, javax.xml.stream.util, javax.xml.transform, javax.xml.transform.dom, javax.xml.transform.sax, javax.xml.transform.stax, javax.xml.transform.stream, javax.xml.validation, javax.xml.xpath, jdk.dynalink, jdk.dynalink.beans, jdk.dynalink.linker, jdk.dynalink.linker.support, jdk.dynalink.support, jdk.javadoc.doclet, jdk.jfr, jdk.jfr.consumer, jdk.jshell, jdk.jshell.execution, jdk.jshell.spi, jdk.jshell.tool, jdk.management.jfr, jdk.nashorn.api.scripting, jdk.nashorn.api.tree, jdk.net, jdk.nio, jdk.security.jarsigner, jdk.swing.interop, netscape.javascript, org.ietf.jgss, org.w3c.dom, org.w3c.dom.bootstrap, org.w3c.dom.css, org.w3c.dom.events, org.w3c.dom.html, org.w3c.dom.ls, org.w3c.dom.ranges, org.w3c.dom.stylesheets, org.w3c.dom.traversal, org.w3c.dom.views, org.w3c.dom.xpath, org.xml.sax, org.xml.sax.ext, org.xml.sax.helpers, sun.misc, sun.reflect
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.uuid = 1f7bf055-af3e-4ea6-8ee5-dcd8de613623
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.vendor = Eclipse
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.version = 1.9.0
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.extension = true
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.fragment = true
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.requirebundle = true
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.client.readbuffer.usedirect = true
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.server.readbuffer.usedirect = true
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.arch = aarch64
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.name = Mac OS X
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.version = 15.5
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.arch = arm64
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles = reference:file:/opt/polarion/polarion/plugins/org.eclipse.equinox.simpleconfigurator_1.3.0.v20180502-1828.jar@1:start
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles.defaultStartLevel = 4
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.checkConfiguration = true
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation = true
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation.default = true
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.area = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.cascaded = false
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.dev = file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework = file:/opt/polarion/polarion/plugins/org.eclipse.osgi_3.13.0.v20180409-1500.jar
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.shape = jar
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.useSystemProperties = true
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.install.area = file:/opt/polarion/polarion/plugins/
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.instance.area = file:/opt/polarion/data/workspace/
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.logfile = /opt/polarion/data/workspace/.metadata/.log
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.nl = zh_CN_#Hans
2025-08-02 12:42:09,021 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.os = linux
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.syspath = /opt/polarion/polarion/plugins
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.tracefile = /opt/polarion/data/workspace/.metadata/trace.log
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.ws = linux
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - password = **PASSWORD**HIDDEN**
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - path.separator = :
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfbox.fontcache = /opt/polarion/data/workspace/polarion-data
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfexport.config = /opt/polarion/polarion/configuration/pdfexport.xml
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.id = polarion-shared
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.url = file:///opt/polarion/data/shared-maven-repo
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.maven.location.maven2 = /opt/polarion/polarion/../maven/distribution
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.size = 100
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.with.history = false
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.tx.doc.cache.size = 100
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.alm = https://polarion.plm.automation.siemens.com/products/polarion-alm
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.qa = https://polarion.plm.automation.siemens.com/products/polarion-qa
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/polarion-requirements
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - repo = http://localhost/repo
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - rolesForNewUserAccount = user
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - RRDir = /opt/polarion/data/RR
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - SDKDir = /opt/polarion/polarion/SDK
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - secure.approvals = false
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - shutdownCatchPhrase = shutdown
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - simple.profiler.enabled = false
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - skip.data.preloading = false
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - socksNonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stderr.encoding = UTF-8
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stdout.encoding = UTF-8
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - storeUrl.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/licensing?product=REQUIREMENTS
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.arch.data.model = 64
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.boot.library.path = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/lib
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.endian = little
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.isalist = 
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.io.unicode.encoding = UnicodeBig
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.command = org.eclipse.equinox.launcher.Main -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -configuration file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/ -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.launcher = SUN_STANDARD
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.jnu.encoding = UTF-8
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.management.compiler = HotSpot 64-Bit Tiered Compilers
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.os.patch.level = unknown
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.contact = https://polarion.plm.automation.siemens.com/techsupport/resources
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.license.email = <EMAIL>
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.sales.email = <EMAIL>
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.access.file = /opt/polarion/data/svn/access
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.passwd.file = /opt/polarion/data/svn/passwd
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.http.encoding = UTF-8
2025-08-02 12:42:09,022 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.library.gnome-keyring.enabled = false
2025-08-02 12:42:09,023 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.ajp13-port = 8889
2025-08-02 12:42:09,023 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.request.safeListedHosts = 0.0.0.0
2025-08-02 12:42:09,023 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.country = CN
2025-08-02 12:42:09,023 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.dir = /Applications/Eclipse JEE.app/Contents/MacOS
2025-08-02 12:42:09,023 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.home = /Users/<USER>
2025-08-02 12:42:09,023 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.language = zh
2025-08-02 12:42:09,023 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.name = zhangwentian
2025-08-02 12:42:09,023 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.script = Hans
2025-08-02 12:42:09,023 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.timezone = Asia/Shanghai
2025-08-02 12:42:09,023 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - userAccountVault = /opt/polarion/data/workspace/user-account-vault
2025-08-02 12:42:09,023 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - workDir = /opt/polarion/data/workspace/polarion-data
2025-08-02 12:42:09,023 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** END of Java system properties
2025-08-02 12:42:09,025 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - XML parsers factory: com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl
2025-08-02 12:42:09,025 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Starting Platform...
2025-08-02 12:42:09,033 [main] INFO  PolarionLicensing - Searching for valid license file in /opt/polarion/polarion/license
2025-08-02 12:42:09,035 [main] INFO  PolarionLicensing - Trying to load license file polarion.lic
2025-08-02 12:42:09,036 [main] INFO  PolarionLicensing - The license file contains the following fields:
2025-08-02 12:42:09,036 [main] INFO  PolarionLicensing - *** License fields ***
2025-08-02 12:42:09,036 [main] INFO  PolarionLicensing - VariantsNamedUsers = 3
2025-08-02 12:42:09,036 [main] INFO  PolarionLicensing - almNamedUsers = 3
2025-08-02 12:42:09,036 [main] INFO  PolarionLicensing - dateCreated = 23.07.2025
2025-08-02 12:42:09,036 [main] INFO  PolarionLicensing - expirationDate = 21.08.2025
2025-08-02 12:42:09,036 [main] INFO  PolarionLicensing - hardwareKey = 8AG9-261C-1962
2025-08-02 12:42:09,037 [main] INFO  PolarionLicensing - licenseFormat = 2022
2025-08-02 12:42:09,037 [main] INFO  PolarionLicensing - licenseType = EVAL
2025-08-02 12:42:09,037 [main] INFO  PolarionLicensing - multiInstanceRunningInstances = 3
2025-08-02 12:42:09,037 [main] INFO  PolarionLicensing - userCompany = Polarion Eval
2025-08-02 12:42:09,037 [main] INFO  PolarionLicensing - *** License fields END ***
2025-08-02 12:42:09,051 [main] INFO  PolarionLicensing - Removing allocations by null
2025-08-02 12:42:09,052 [main] INFO  PolarionLicensing - STATS:concurrentVariantsUser,current:0,peak:0,limit:0
2025-08-02 12:42:09,052 [main] INFO  PolarionLicensing - 0 namedReviewerUser assignments (out of 0) loaded: []
2025-08-02 12:42:09,052 [main] INFO  PolarionLicensing - 0 concurrentReviewerUser assignments (out of 0) loaded: []
2025-08-02 12:42:09,052 [main] INFO  PolarionLicensing - STATS:concurrentReviewerUser,current:0,peak:0,limit:0
2025-08-02 12:42:09,053 [main] INFO  PolarionLicensing - 0 namedXBaseUser assignments (out of 0) loaded: []
2025-08-02 12:42:09,053 [main] INFO  PolarionLicensing - 0 concurrentXBaseUser assignments (out of 0) loaded: []
2025-08-02 12:42:09,053 [main] INFO  PolarionLicensing - STATS:concurrentXBaseUser,current:0,peak:0,limit:0
2025-08-02 12:42:09,053 [main] INFO  PolarionLicensing - 0 namedXProUser assignments (out of 0) loaded: []
2025-08-02 12:42:09,053 [main] INFO  PolarionLicensing - 0 concurrentXProUser assignments (out of 0) loaded: []
2025-08-02 12:42:09,053 [main] INFO  PolarionLicensing - STATS:concurrentXProUser,current:0,peak:0,limit:0
2025-08-02 12:42:09,053 [main] INFO  PolarionLicensing - 0 namedXEnterpriseUser assignments (out of 0) loaded: []
2025-08-02 12:42:09,053 [main] INFO  PolarionLicensing - 0 concurrentXEnterpriseUser assignments (out of 0) loaded: []
2025-08-02 12:42:09,053 [main] INFO  PolarionLicensing - STATS:concurrentXEnterpriseUser,current:0,peak:0,limit:0
2025-08-02 12:42:09,053 [main] INFO  PolarionLicensing - 0 namedProUser assignments (out of 0) loaded: []
2025-08-02 12:42:09,054 [main] INFO  PolarionLicensing - 0 concurrentProUser assignments (out of 0) loaded: []
2025-08-02 12:42:09,054 [main] INFO  PolarionLicensing - STATS:concurrentProUser,current:0,peak:0,limit:0
2025-08-02 12:42:09,054 [main] INFO  PolarionLicensing - 0 namedRequirementsUser assignments (out of 0) loaded: []
2025-08-02 12:42:09,054 [main] INFO  PolarionLicensing - 0 concurrentRequirementsUser assignments (out of 0) loaded: []
2025-08-02 12:42:09,054 [main] INFO  PolarionLicensing - STATS:concurrentRequirementsUser,current:0,peak:0,limit:0
2025-08-02 12:42:09,054 [main] INFO  PolarionLicensing - 0 namedQAUser assignments (out of 0) loaded: []
2025-08-02 12:42:09,054 [main] INFO  PolarionLicensing - 0 concurrentQAUser assignments (out of 0) loaded: []
2025-08-02 12:42:09,054 [main] INFO  PolarionLicensing - STATS:concurrentQAUser,current:0,peak:0,limit:0
2025-08-02 12:42:09,054 [main] INFO  PolarionLicensing - 3 namedALMUser assignments (out of 3) loaded: [admin, ou_d6f3139d36fb2978b33a8f870096b9e3, mTest]
2025-08-02 12:42:09,055 [main] INFO  PolarionLicensing - 0 concurrentALMUser assignments (out of 0) loaded: []
2025-08-02 12:42:09,055 [main] INFO  PolarionLicensing - STATS:concurrentALMUser,current:0,peak:0,limit:0
2025-08-02 12:42:09,055 [main] INFO  PolarionLicensing - 
*******************************************************************
 Polarion successfully activated
*******************************************************************
2025-08-02 12:42:09,128 [main] INFO  com.polarion.platform.internal.i18n.LanguageContributor - Localization file /META-INF/messages_en.properties read successfully (7789 messages)
2025-08-02 12:42:09,150 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Processing bundles:
2025-08-02 12:42:09,150 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [0] - org.eclipse.osgi
2025-08-02 12:42:09,150 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [1] - org.eclipse.equinox.simpleconfigurator
2025-08-02 12:42:09,150 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [2] - antlr
2025-08-02 12:42:09,151 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [3] - antlr4
2025-08-02 12:42:09,151 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [4] - antlr4-runtime
2025-08-02 12:42:09,151 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [5] - bcprov
2025-08-02 12:42:09,151 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [6] - com.auth0.java-jwt
2025-08-02 12:42:09,151 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [7] - com.fasnote.alm.auth.feishu
2025-08-02 12:42:09,153 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.fasnote.alm.auth.feishu to HiveMind
2025-08-02 12:42:09,153 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [8] - com.fasnote.alm.checklist
2025-08-02 12:42:09,153 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [9] - com.fasnote.alm.injection
2025-08-02 12:42:09,154 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [10] - com.fasnote.alm.plugin.manage
2025-08-02 12:42:09,154 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [11] - com.fasnote.alm.test
2025-08-02 12:42:09,157 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [12] - com.fasnote.alm.watermark
2025-08-02 12:42:09,157 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [13] - com.fasterxml.classmate
2025-08-02 12:42:09,157 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [14] - com.fasterxml.jackson
2025-08-02 12:42:09,157 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [15] - com.fasterxml.jackson.dataformat.jackson-dataformat-yaml
2025-08-02 12:42:09,157 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [16] - com.fasterxml.jackson.jaxrs
2025-08-02 12:42:09,157 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [17] - com.fasterxml.woodstox
2025-08-02 12:42:09,157 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [18] - com.finething.hesai.ai
2025-08-02 12:42:09,160 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.finething.hesai.ai to HiveMind
2025-08-02 12:42:09,160 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [19] - com.finething.hesai.defect
2025-08-02 12:42:09,160 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.finething.hesai.defect to HiveMind
2025-08-02 12:42:09,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [20] - com.google.gson
2025-08-02 12:42:09,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [21] - com.google.guava
2025-08-02 12:42:09,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [22] - com.google.guava.failureaccess
2025-08-02 12:42:09,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [23] - com.ibm.icu.icu4j
2025-08-02 12:42:09,162 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [24] - com.icl.saxon
2025-08-02 12:42:09,163 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [25] - com.jayway.jsonpath.json-path
2025-08-02 12:42:09,163 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [26] - com.jcraft.jsch
2025-08-02 12:42:09,163 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [27] - com.networknt.json-schema-validator
2025-08-02 12:42:09,163 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [28] - com.nimbusds.content-type
2025-08-02 12:42:09,163 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [29] - com.nimbusds.nimbus-jose-jwt
2025-08-02 12:42:09,163 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [30] - com.opensymphony.quartz
2025-08-02 12:42:09,164 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [31] - com.polarion.alm.ProjectPlanGantt_new
2025-08-02 12:42:09,165 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.ProjectPlanGantt_new to HiveMind
2025-08-02 12:42:09,165 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [32] - com.polarion.alm.builder
2025-08-02 12:42:09,165 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.builder to HiveMind
2025-08-02 12:42:09,165 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [33] - com.polarion.alm.checker
2025-08-02 12:42:09,166 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.checker to HiveMind
2025-08-02 12:42:09,166 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [34] - com.polarion.alm.extension.vcontext
2025-08-02 12:42:09,166 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.extension.vcontext to HiveMind
2025-08-02 12:42:09,166 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [35] - com.polarion.alm.impex
2025-08-02 12:42:09,167 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.impex to HiveMind
2025-08-02 12:42:09,167 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [36] - com.polarion.alm.install
2025-08-02 12:42:09,167 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [37] - com.polarion.alm.oslc
2025-08-02 12:42:09,168 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.oslc to HiveMind
2025-08-02 12:42:09,168 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [38] - com.polarion.alm.projects
2025-08-02 12:42:09,168 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.projects to HiveMind
2025-08-02 12:42:09,169 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [39] - com.polarion.alm.qcentre
2025-08-02 12:42:09,169 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.qcentre to HiveMind
2025-08-02 12:42:09,169 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [40] - com.polarion.alm.tracker
2025-08-02 12:42:09,169 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.tracker to HiveMind
2025-08-02 12:42:09,169 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [41] - com.polarion.alm.ui
2025-08-02 12:42:09,172 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.ui to HiveMind
2025-08-02 12:42:09,172 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [42] - com.polarion.alm.ui.diagrams.mxgraph
2025-08-02 12:42:09,173 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [43] - com.polarion.alm.wiki
2025-08-02 12:42:09,174 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.wiki to HiveMind
2025-08-02 12:42:09,174 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [44] - com.polarion.alm.ws
2025-08-02 12:42:09,174 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [45] - com.polarion.alm.ws.client
2025-08-02 12:42:09,175 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [46] - com.polarion.cluster
2025-08-02 12:42:09,175 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.cluster to HiveMind
2025-08-02 12:42:09,175 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [47] - com.polarion.core.boot
2025-08-02 12:42:09,175 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [48] - com.polarion.core.util
2025-08-02 12:42:09,176 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [49] - com.polarion.fop
2025-08-02 12:42:09,176 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [50] - com.polarion.platform
2025-08-02 12:42:09,176 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform to HiveMind
2025-08-02 12:42:09,176 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [51] - com.polarion.platform.guice
2025-08-02 12:42:09,177 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [52] - com.polarion.platform.hivemind
2025-08-02 12:42:09,177 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.hivemind to HiveMind
2025-08-02 12:42:09,177 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [53] - com.polarion.platform.jobs
2025-08-02 12:42:09,177 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.jobs to HiveMind
2025-08-02 12:42:09,177 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [54] - com.polarion.platform.monitoring
2025-08-02 12:42:09,177 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.monitoring to HiveMind
2025-08-02 12:42:09,177 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [55] - com.polarion.platform.persistence
2025-08-02 12:42:09,178 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.persistence to HiveMind
2025-08-02 12:42:09,178 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [56] - com.polarion.platform.repository
2025-08-02 12:42:09,178 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository to HiveMind
2025-08-02 12:42:09,178 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [57] - com.polarion.platform.repository.driver.svn
2025-08-02 12:42:09,178 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.driver.svn to HiveMind
2025-08-02 12:42:09,178 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [58] - com.polarion.platform.repository.external
2025-08-02 12:42:09,178 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external to HiveMind
2025-08-02 12:42:09,178 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [59] - com.polarion.platform.repository.external.git
2025-08-02 12:42:09,178 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.git to HiveMind
2025-08-02 12:42:09,178 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [60] - com.polarion.platform.repository.external.svn
2025-08-02 12:42:09,178 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.svn to HiveMind
2025-08-02 12:42:09,178 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [61] - com.polarion.platform.sql
2025-08-02 12:42:09,179 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [62] - com.polarion.portal.tomcat
2025-08-02 12:42:09,181 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [63] - com.polarion.psvn.launcher
2025-08-02 12:42:09,182 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.psvn.launcher to HiveMind
2025-08-02 12:42:09,182 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [64] - com.polarion.psvn.translations.en
2025-08-02 12:42:09,182 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [65] - com.polarion.purevariants
2025-08-02 12:42:09,182 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.purevariants to HiveMind
2025-08-02 12:42:09,182 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [66] - com.polarion.qcentre
2025-08-02 12:42:09,183 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [67] - com.polarion.scripting
2025-08-02 12:42:09,189 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.scripting to HiveMind
2025-08-02 12:42:09,189 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [68] - com.polarion.scripting.servlet
2025-08-02 12:42:09,189 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [69] - com.polarion.subterra.base
2025-08-02 12:42:09,189 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [70] - com.polarion.subterra.index
2025-08-02 12:42:09,190 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.index to HiveMind
2025-08-02 12:42:09,190 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [71] - com.polarion.subterra.persistence
2025-08-02 12:42:09,190 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence to HiveMind
2025-08-02 12:42:09,190 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [72] - com.polarion.subterra.persistence.document
2025-08-02 12:42:09,190 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence.document to HiveMind
2025-08-02 12:42:09,190 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [73] - com.polarion.synchronizer
2025-08-02 12:42:09,190 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.synchronizer to HiveMind
2025-08-02 12:42:09,190 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [74] - com.polarion.synchronizer.proxy.feishu
2025-08-02 12:42:09,191 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [75] - com.polarion.synchronizer.proxy.hpalm
2025-08-02 12:42:09,195 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [76] - com.polarion.synchronizer.proxy.jira
2025-08-02 12:42:09,195 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [77] - com.polarion.synchronizer.proxy.polarion
2025-08-02 12:42:09,196 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [78] - com.polarion.synchronizer.proxy.reqif
2025-08-02 12:42:09,204 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [79] - com.polarion.synchronizer.ui
2025-08-02 12:42:09,204 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [80] - com.polarion.usdp.persistence
2025-08-02 12:42:09,205 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.usdp.persistence to HiveMind
2025-08-02 12:42:09,205 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [81] - com.polarion.xray.doc.user
2025-08-02 12:42:09,205 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [82] - com.siemens.des.logger.api
2025-08-02 12:42:09,205 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [83] - com.siemens.plm.bitools.analytics
2025-08-02 12:42:09,205 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [84] - com.siemens.polarion.ct.collectors.git
2025-08-02 12:42:09,205 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.ct.collectors.git to HiveMind
2025-08-02 12:42:09,206 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [85] - com.siemens.polarion.eclipse.configurator
2025-08-02 12:42:09,206 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [86] - com.siemens.polarion.integration.ci
2025-08-02 12:42:09,207 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.integration.ci to HiveMind
2025-08-02 12:42:09,207 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [87] - com.siemens.polarion.previewer
2025-08-02 12:42:09,208 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer to HiveMind
2025-08-02 12:42:09,208 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [88] - com.siemens.polarion.previewer.external
2025-08-02 12:42:09,208 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer.external to HiveMind
2025-08-02 12:42:09,208 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [89] - com.siemens.polarion.rest
2025-08-02 12:42:09,208 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [90] - com.siemens.polarion.rt
2025-08-02 12:42:09,209 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [91] - com.siemens.polarion.rt.api
2025-08-02 12:42:09,209 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [92] - com.siemens.polarion.rt.collectors.git
2025-08-02 12:42:09,210 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [93] - com.siemens.polarion.rt.communication.common
2025-08-02 12:42:09,210 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [94] - com.siemens.polarion.rt.communication.polarion
2025-08-02 12:42:09,210 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.rt.communication.polarion to HiveMind
2025-08-02 12:42:09,210 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [95] - com.siemens.polarion.rt.communication.rt
2025-08-02 12:42:09,210 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [96] - com.siemens.polarion.rt.parsers.c
2025-08-02 12:42:09,211 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [97] - com.siemens.polarion.rt.ui
2025-08-02 12:42:09,211 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [98] - com.siemens.polarion.synchronizer.proxy.tfs
2025-08-02 12:42:09,211 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [99] - com.sun.activation.javax.activation
2025-08-02 12:42:09,212 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [100] - com.sun.istack.commons-runtime
2025-08-02 12:42:09,212 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [101] - com.sun.jna
2025-08-02 12:42:09,212 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [102] - com.sun.jna.platform
2025-08-02 12:42:09,212 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [103] - com.sun.xml.bind.jaxb-impl
2025-08-02 12:42:09,212 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [104] - com.teamlive.hozon.expcounter
2025-08-02 12:42:09,212 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.teamlive.hozon.expcounter to HiveMind
2025-08-02 12:42:09,212 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [105] - com.teamlive.livechecklist
2025-08-02 12:42:09,212 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.teamlive.livechecklist to HiveMind
2025-08-02 12:42:09,212 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [106] - com.trilead.ssh2
2025-08-02 12:42:09,212 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [107] - com.zaxxer.hikariCP
2025-08-02 12:42:09,212 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [108] - des-sdk-core
2025-08-02 12:42:09,213 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [109] - des-sdk-dss
2025-08-02 12:42:09,213 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [110] - io.github.resilience4j.circuitbreaker
2025-08-02 12:42:09,213 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [111] - io.github.resilience4j.core
2025-08-02 12:42:09,213 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [112] - io.github.resilience4j.retry
2025-08-02 12:42:09,213 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [113] - io.swagger
2025-08-02 12:42:09,213 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [114] - io.vavr
2025-08-02 12:42:09,213 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [115] - jakaroma
2025-08-02 12:42:09,213 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [116] - jakarta.validation.validation-api
2025-08-02 12:42:09,213 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [117] - javassist
2025-08-02 12:42:09,213 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [118] - javax.annotation-api
2025-08-02 12:42:09,213 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [119] - javax.cache
2025-08-02 12:42:09,213 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [120] - javax.el
2025-08-02 12:42:09,213 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [121] - javax.inject
2025-08-02 12:42:09,213 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [122] - javax.servlet
2025-08-02 12:42:09,214 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [123] - javax.servlet.jsp
2025-08-02 12:42:09,214 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [124] - javax.transaction
2025-08-02 12:42:09,214 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [125] - jaxb-api
2025-08-02 12:42:09,214 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [126] - jcip-annotations
2025-08-02 12:42:09,214 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [127] - jcl.over.slf4j
2025-08-02 12:42:09,214 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [128] - jul.to.slf4j
2025-08-02 12:42:09,214 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [129] - kuromoji-core
2025-08-02 12:42:09,214 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [130] - kuromoji-ipadic
2025-08-02 12:42:09,214 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [131] - lang-tag
2025-08-02 12:42:09,214 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [132] - net.htmlparser.jericho
2025-08-02 12:42:09,214 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [133] - net.java.dev.jna
2025-08-02 12:42:09,214 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [134] - net.minidev.accessors-smart
2025-08-02 12:42:09,215 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [135] - net.minidev.asm
2025-08-02 12:42:09,215 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [136] - net.minidev.json-smart
2025-08-02 12:42:09,215 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [137] - net.n3.nanoxml
2025-08-02 12:42:09,215 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [138] - net.sourceforge.cssparser
2025-08-02 12:42:09,215 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [139] - nu.xom
2025-08-02 12:42:09,215 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [140] - oauth2-oidc-sdk
2025-08-02 12:42:09,216 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [141] - org.apache.ant
2025-08-02 12:42:09,223 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [142] - org.apache.avro
2025-08-02 12:42:09,223 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [143] - org.apache.axis
2025-08-02 12:42:09,226 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [144] - org.apache.batik
2025-08-02 12:42:09,227 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [145] - org.apache.commons.codec
2025-08-02 12:42:09,227 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [146] - org.apache.commons.collections
2025-08-02 12:42:09,227 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [147] - org.apache.commons.commons-beanutils
2025-08-02 12:42:09,227 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [148] - org.apache.commons.commons-collections4
2025-08-02 12:42:09,227 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [149] - org.apache.commons.commons-compress
2025-08-02 12:42:09,227 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [150] - org.apache.commons.commons-fileupload
2025-08-02 12:42:09,227 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [151] - org.apache.commons.digester
2025-08-02 12:42:09,227 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [152] - org.apache.commons.exec
2025-08-02 12:42:09,227 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [153] - org.apache.commons.io
2025-08-02 12:42:09,227 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [154] - org.apache.commons.lang
2025-08-02 12:42:09,227 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [155] - org.apache.commons.lang3
2025-08-02 12:42:09,228 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [156] - org.apache.commons.logging
2025-08-02 12:42:09,228 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [157] - org.apache.curator
2025-08-02 12:42:09,233 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [158] - org.apache.fop
2025-08-02 12:42:09,233 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [159] - org.apache.hivemind
2025-08-02 12:42:09,234 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle org.apache.hivemind to HiveMind
2025-08-02 12:42:09,234 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [160] - org.apache.httpcomponents.httpclient
2025-08-02 12:42:09,234 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [161] - org.apache.httpcomponents.httpcore
2025-08-02 12:42:09,234 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [162] - org.apache.jasper.glassfish
2025-08-02 12:42:09,234 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [163] - org.apache.kafka.clients
2025-08-02 12:42:09,235 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [164] - org.apache.kafka.streams
2025-08-02 12:42:09,235 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [165] - org.apache.logging.log4j.1.2-api
2025-08-02 12:42:09,235 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [166] - org.apache.logging.log4j.api
2025-08-02 12:42:09,235 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [167] - org.apache.logging.log4j.apiconf
2025-08-02 12:42:09,235 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [168] - org.apache.logging.log4j.core
2025-08-02 12:42:09,235 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [169] - org.apache.logging.log4j.slf4j-impl
2025-08-02 12:42:09,235 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [170] - org.apache.lucene.analyzers-common
2025-08-02 12:42:09,235 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [171] - org.apache.lucene.analyzers-common
2025-08-02 12:42:09,236 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [172] - org.apache.lucene.analyzers-smartcn
2025-08-02 12:42:09,236 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [173] - org.apache.lucene.core
2025-08-02 12:42:09,237 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [174] - org.apache.lucene.core
2025-08-02 12:42:09,238 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [175] - org.apache.lucene.grouping
2025-08-02 12:42:09,238 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [176] - org.apache.lucene.queryparser
2025-08-02 12:42:09,238 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [177] - org.apache.oro
2025-08-02 12:42:09,239 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [178] - org.apache.pdfbox.fontbox
2025-08-02 12:42:09,239 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [179] - org.apache.poi
2025-08-02 12:42:09,248 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [180] - org.apache.tika
2025-08-02 12:42:09,699 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [181] - org.apache.xalan
2025-08-02 12:42:09,699 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [182] - org.apache.xercesImpl
2025-08-02 12:42:09,699 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [183] - org.apache.xml.serializer
2025-08-02 12:42:09,699 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [184] - org.apache.xmlgraphics.commons
2025-08-02 12:42:09,700 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [185] - org.apache.zookeeper
2025-08-02 12:42:09,700 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [186] - org.codehaus.groovy
2025-08-02 12:42:09,701 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [187] - org.codehaus.jettison
2025-08-02 12:42:09,701 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [188] - org.dom4j
2025-08-02 12:42:09,701 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [189] - org.eclipse.core.contenttype
2025-08-02 12:42:09,701 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [190] - org.eclipse.core.expressions
2025-08-02 12:42:09,701 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [191] - org.eclipse.core.filesystem
2025-08-02 12:42:09,701 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [192] - org.eclipse.core.jobs
2025-08-02 12:42:09,701 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [193] - org.eclipse.core.net
2025-08-02 12:42:09,701 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [194] - org.eclipse.core.resources
2025-08-02 12:42:09,701 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [195] - org.eclipse.core.runtime
2025-08-02 12:42:09,701 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [196] - org.eclipse.equinox.app
2025-08-02 12:42:09,701 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [197] - org.eclipse.equinox.common
2025-08-02 12:42:09,701 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [198] - org.eclipse.equinox.event
2025-08-02 12:42:09,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [199] - org.eclipse.equinox.http.registry
2025-08-02 12:42:09,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [200] - org.eclipse.equinox.http.servlet
2025-08-02 12:42:09,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [201] - org.eclipse.equinox.jsp.jasper
2025-08-02 12:42:09,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [202] - org.eclipse.equinox.jsp.jasper.registry
2025-08-02 12:42:09,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [203] - org.eclipse.equinox.launcher
2025-08-02 12:42:09,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [204] - org.eclipse.equinox.preferences
2025-08-02 12:42:09,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [205] - org.eclipse.equinox.registry
2025-08-02 12:42:09,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [206] - org.eclipse.equinox.security
2025-08-02 12:42:09,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [207] - org.eclipse.help
2025-08-02 12:42:09,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [208] - org.eclipse.help.base
2025-08-02 12:42:09,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [209] - org.eclipse.help.webapp
2025-08-02 12:42:09,702 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [210] - org.eclipse.jgit
2025-08-02 12:42:09,703 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [211] - org.eclipse.osgi.services
2025-08-02 12:42:09,703 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [212] - org.eclipse.osgi.util
2025-08-02 12:42:09,703 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [213] - org.ehcache
2025-08-02 12:42:09,704 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [214] - org.gitlab.java-gitlab-api
2025-08-02 12:42:09,705 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [215] - org.glassfish.jersey
2025-08-02 12:42:09,705 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [216] - org.hibernate.annotations
2025-08-02 12:42:09,705 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [217] - org.hibernate.core
2025-08-02 12:42:09,706 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [218] - org.hibernate.entitymanager
2025-08-02 12:42:09,706 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [219] - org.hibernate.hikaricp
2025-08-02 12:42:09,706 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [220] - org.hibernate.jpa.2.1.api
2025-08-02 12:42:09,706 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [221] - org.jboss.logging
2025-08-02 12:42:09,706 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [222] - org.jvnet.mimepull
2025-08-02 12:42:09,706 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [223] - org.objectweb.asm
2025-08-02 12:42:09,706 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [224] - org.objectweb.jotm
2025-08-02 12:42:09,706 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [225] - org.opensaml
2025-08-02 12:42:09,719 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [226] - org.polarion.svncommons
2025-08-02 12:42:09,719 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [227] - org.polarion.svnwebclient
2025-08-02 12:42:09,719 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [228] - org.postgesql
2025-08-02 12:42:09,719 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [229] - org.projectlombok.lombok
2025-08-02 12:42:09,731 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [230] - org.rocksdb.rocksdbjni
2025-08-02 12:42:09,731 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [231] - org.springframework.data.core
2025-08-02 12:42:09,731 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [232] - org.springframework.data.jpa
2025-08-02 12:42:09,731 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [233] - org.springframework.spring-aop
2025-08-02 12:42:09,731 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [234] - org.springframework.spring-beans
2025-08-02 12:42:09,731 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [235] - org.springframework.spring-context
2025-08-02 12:42:09,732 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [236] - org.springframework.spring-core
2025-08-02 12:42:09,732 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [237] - org.springframework.spring-expression
2025-08-02 12:42:09,732 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [238] - org.springframework.spring-jdbc
2025-08-02 12:42:09,732 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [239] - org.springframework.spring-orm
2025-08-02 12:42:09,732 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [240] - org.springframework.spring-test
2025-08-02 12:42:09,732 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [241] - org.springframework.spring-tx
2025-08-02 12:42:09,732 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [242] - org.springframework.spring-web
2025-08-02 12:42:09,733 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [243] - org.springframework.spring-webmvc
2025-08-02 12:42:09,733 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [244] - org.tmatesoft.sqljet
2025-08-02 12:42:09,733 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [245] - org.tmatesoft.svnkit
2025-08-02 12:42:09,733 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [246] - saaj-api
2025-08-02 12:42:09,733 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [247] - sdk-lifecycle-collab
2025-08-02 12:42:09,733 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [248] - sdk-lifecycle-docmgmt
2025-08-02 12:42:09,733 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [249] - siemens.des.clientsecurity
2025-08-02 12:42:09,733 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [250] - slf4j.api
2025-08-02 12:42:09,734 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [251] - xml-apis
2025-08-02 12:42:09,734 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [252] - xml.apis.ext
2025-08-02 12:42:09,734 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [253] - xstream
2025-08-02 12:42:09,925 [main] INFO  com.polarion.core.util.remote.server.SocketRemoteControlServer - Remote control server socket is ready to listen on localhost/127.0.0.1:8887
2025-08-02 12:42:09,926 [xServer:8887] INFO  org.xsocket.connection.Server - server listening on localhost:8887 (xSocket 2.5.3)
2025-08-02 12:42:10,097 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database...
2025-08-02 12:42:10,147 [main] INFO  com.polarion.platform.sql.internal.PgServerInfo - PG server listening on localhost:5435
2025-08-02 12:42:12,268 [main] INFO  com.polarion.platform.internal.cache.CacheConfigurator - EHCache uses internal configuration
2025-08-02 12:42:12,559 [main] WARN  org.ehcache.impl.internal.executor.PooledExecutionService - No default pool configured, services requiring thread pools must be configured explicitly using named thread pools
2025-08-02 12:42:12,637 [main] INFO  org.ehcache.sizeof.filters.AnnotationSizeOfFilter - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-02 12:42:12,642 [main] INFO  org.ehcache.sizeof.impl.JvmInformation - Detected JVM data model settings of: 64-Bit OpenJDK JVM with Compressed OOPs
2025-08-02 12:42:12,655 [main] INFO  org.ehcache.sizeof.impl.AgentLoader - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-02 12:42:12,735 [main] INFO  com.polarion.platform.internal.cache.CachingProviderHandler - All the caches have been destroyed because of not clean shutdown. You can ignore this message if Polarion started in reindex mode.
2025-08-02 12:42:12,774 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-08-02 12:42:12,776 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-08-02 12:42:12,777 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion_history' is: *************************************************
2025-08-02 12:42:12,821 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database finished [ TIME 2.72 s. ]
2025-08-02 12:42:12,992 [main] INFO  com.polarion.platform.cluster.ClusterService - Initializing cluster service
2025-08-02 12:42:12,992 [main] INFO  com.polarion.platform.cluster.ClusterService - Cluster service is disabled.
2025-08-02 12:42:13,179 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-02 12:42:13,208 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Starting...
2025-08-02 12:42:13,263 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Start completed.
2025-08-02 12:42:13,338 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.159 s. ]
2025-08-02 12:42:13,338 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-02 12:42:13,350 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Starting...
2025-08-02 12:42:13,358 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Start completed.
2025-08-02 12:42:13,401 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0622 s. ]
2025-08-02 12:42:13,401 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot started
2025-08-02 12:42:13,443 [main] INFO  com.polarion.platform.repository.driver.svn.internal.security.SVNWatcher - SVN auth file watcher started with a period of 3000 milliseconds
2025-08-02 12:42:13,449 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-02 12:42:13,480 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion authenticated from system
2025-08-02 12:42:13,529 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion logged in from system
2025-08-02 12:42:13,535 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Created
2025-08-02 12:42:13,535 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Initialized
2025-08-02 12:42:13,541 [main | u:p] INFO  com.polarion.core.util.profiling.SimpleProfiler - Initialization
2025-08-02 12:42:13,552 [main | u:p] INFO  org.objectweb.jotm - JOTM started with a local transaction factory which is not bound.
2025-08-02 12:42:13,552 [main | u:p] INFO  org.objectweb.jotm - CAROL initialization
2025-08-02 12:42:13,558 [main | u:p] INFO  com.polarion.platform.internal.service.repository.listeners.job.PullingJob - lastFullyProcessedRevision [275]
2025-08-02 12:42:13,564 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - END initializeService
2025-08-02 12:42:13,568 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Polarion startup estimation:  [ TIME 15 s. ]
2025-08-02 12:42:13,568 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 15 s. ]
2025-08-02 12:42:13,575 [main | u:p] INFO  com.polarion.platform.monitoring - Full monitoring results are stored in file /opt/polarion/data/workspace/monitoring/results.txt
2025-08-02 12:42:13,603 [main | u:p] INFO  com.polarion.platform.monitoring - Executing actions from stage PREBOOT
2025-08-02 12:42:13,605 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 12:42:13,606 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,420.97 GB
 [Sat Aug 02 12:42:13 CST 2025]
2025-08-02 12:42:13,793 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.jvm.version'
2025-08-02 12:42:13,793 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.jvm.version (JVM Version) = JVM Version 
Microsoft OpenJDK 64-Bit Server VM 11.0.27+6-LTS
 [Sat Aug 02 12:42:13 CST 2025]
2025-08-02 12:42:13,796 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.os.version'
2025-08-02 12:42:13,796 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.os.version (OS Version) = OS Version 
Mac OS X 15.5 aarch64
 [Sat Aug 02 12:42:13 CST 2025]
2025-08-02 12:42:13,799 [main | u:p] INFO  com.polarion.platform.monitoring - Finished with actions from stage PREBOOT: {OK=3}
2025-08-02 12:42:13,800 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.81 s. ]
2025-08-02 12:42:13,800 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0584 s [66% update (144x), 34% query (12x)] (221x), svn: 0.014 s [55% getLatestRevision (2x), 32% testConnection (1x)] (4x)
2025-08-02 12:42:13,800 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - calling ILowLevelPersistence.boot to start persistence
2025-08-02 12:42:13,809 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization started
2025-08-02 12:42:13,846 [main | u:p] INFO  com.polarion.subterra.base.internal.location.LocationCacheContext - Registered invalidationListener: com.polarion.platform.repository.internal.config.RepositoryConfigService$1@346ee7b6
2025-08-02 12:42:13,876 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: BaselineCollection
2025-08-02 12:42:13,876 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: TestRun
2025-08-02 12:42:13,876 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: Plan
2025-08-02 12:42:13,891 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition started
2025-08-02 12:42:13,891 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:42:13,892 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-02 12:42:13,919 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition finished [ TIME 0.0279 s. ]
2025-08-02 12:42:13,919 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context tree: 
ROOT_CTX_NAME (ContextNature[Root], ContextId[context [global]])
+-default (ContextNature[Repository], ContextId[cluster default, context [global]])
  +-WBS (ContextNature[Project], ContextId[cluster default, context WBS])
  +-WBSdev (ContextNature[Project], ContextId[cluster default, context WBSdev])
  +-Demo Projects (ContextNature[ProjectGroup], ContextId[cluster default, context --Demo Projects])
  | +-elibrary (ContextNature[Project], ContextId[cluster default, context elibrary])
  | +-drivepilot (ContextNature[Project], ContextId[cluster default, context drivepilot])
  +-library (ContextNature[Project], ContextId[cluster default, context library])
  +-hesai (ContextNature[Project], ContextId[cluster default, context hesai])
2025-08-02 12:42:13,919 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-08-02 12:42:13,919 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0321 s [59% getDir2 content (2x), 36% info (3x)] (6x)
2025-08-02 12:42:13,919 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:42:13,919 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-02 12:42:13,920 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Startup workers for phase 3: 6
2025-08-02 12:42:13,926 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-08-02 12:42:13,926 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-08-02 12:42:13,926 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context library] (1/9) ...
2025-08-02 12:42:13,926 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-08-02 12:42:13,926 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[context [global]] (6/9) ...
2025-08-02 12:42:13,926 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-08-02 12:42:13,926 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-08-02 12:42:13,926 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context [global]] (5/9) ...
2025-08-02 12:42:13,926 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context --Demo Projects] (4/9) ...
2025-08-02 12:42:13,926 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-08-02 12:42:13,926 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBSdev] (2/9) ...
2025-08-02 12:42:13,927 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBS] (3/9) ...
2025-08-02 12:42:13,935 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[context [global]] (6/9) TOOK  [ TIME 0.00915 s. ]
2025-08-02 12:42:13,936 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-02 12:42:13,936 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context hesai] (7/9) ...
2025-08-02 12:42:14,024 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/.polarion'
2025-08-02 12:42:14,031 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/modules'
2025-08-02 12:42:14,038 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/documents'
2025-08-02 12:42:14,043 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/_wiki'
2025-08-02 12:42:14,044 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context --Demo Projects contains 0 primary objects (work items+comments).
2025-08-02 12:42:14,044 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context --Demo Projects] (4/9) TOOK  [ TIME 0.118 s. ]
2025-08-02 12:42:14,044 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-02 12:42:14,044 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context elibrary] (8/9) ...
2025-08-02 12:42:14,097 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/library/documents'
2025-08-02 12:42:14,097 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBS/documents'
2025-08-02 12:42:14,137 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context library contains 288 primary objects (work items+comments).
2025-08-02 12:42:14,137 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context library] (1/9) TOOK  [ TIME 0.211 s. ]
2025-08-02 12:42:14,137 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-02 12:42:14,137 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context drivepilot] (9/9) ...
2025-08-02 12:42:14,141 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/elibrary/documents'
2025-08-02 12:42:14,170 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBS contains 344 primary objects (work items+comments).
2025-08-02 12:42:14,171 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBS] (3/9) TOOK  [ TIME 0.244 s. ]
2025-08-02 12:42:14,203 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context elibrary contains 334 primary objects (work items+comments).
2025-08-02 12:42:14,203 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context elibrary] (8/9) TOOK  [ TIME 0.159 s. ]
2025-08-02 12:42:14,257 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/drivepilot/documents'
2025-08-02 12:42:14,276 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/hesai/documents'
2025-08-02 12:42:14,299 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context drivepilot contains 461 primary objects (work items+comments).
2025-08-02 12:42:14,299 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context drivepilot] (9/9) TOOK  [ TIME 0.162 s. ]
2025-08-02 12:42:14,373 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context hesai contains 1148 primary objects (work items+comments).
2025-08-02 12:42:14,373 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context hesai] (7/9) TOOK  [ TIME 0.437 s. ]
2025-08-02 12:42:14,463 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context null contains 2214 primary objects (work items+comments).
2025-08-02 12:42:14,463 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context [global]] (5/9) TOOK  [ TIME 0.537 s. ]
2025-08-02 12:42:14,493 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBSdev/documents'
2025-08-02 12:42:14,613 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBSdev contains 3322 primary objects (work items+comments).
2025-08-02 12:42:14,614 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBSdev] (2/9) TOOK  [ TIME 0.687 s. ]
2025-08-02 12:42:14,615 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.692 s, CPU [user: 0.123 s, system: 0.223 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0968 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-02 12:42:14,615 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.693 s, CPU [user: 0.238 s, system: 0.342 s], Allocated memory: 68.8 MB, transactions: 0, ObjectMaps: 0.12 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-02 12:42:14,615 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.692 s, CPU [user: 0.202 s, system: 0.285 s], Allocated memory: 52.9 MB, transactions: 0, ObjectMaps: 0.117 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-02 12:42:14,615 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.691 s, CPU [user: 0.0954 s, system: 0.168 s], Allocated memory: 14.4 MB, transactions: 0, ObjectMaps: 0.0686 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0498 s [77% log2 (10x), 15% getLatestRevision (2x)] (13x)
2025-08-02 12:42:14,615 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.691 s, CPU [user: 0.0652 s, system: 0.0908 s], Allocated memory: 8.8 MB, transactions: 0, svn: 0.0755 s [81% log2 (10x)] (13x), ObjectMaps: 0.062 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-02 12:42:14,615 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.692 s, CPU [user: 0.0721 s, system: 0.0993 s], Allocated memory: 9.5 MB, transactions: 0, svn: 0.0676 s [34% log2 (5x), 22% info (5x), 21% log (1x), 10% getLatestRevision (2x)] (18x), ObjectMaps: 0.0612 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-02 12:42:14,616 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.7 s. ]
2025-08-02 12:42:14,616 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.525 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.251 s [63% log2 (36x), 13% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-08-02 12:42:14,626 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [START].
2025-08-02 12:42:14,626 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:42:14,626 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-02 12:42:14,729 [main | u:p | u:p] ERROR com.polarion.platform.repository.internal.config.RepositoryConfigService$ConfigProblemCatcher - Failed to work with configuration from location /WBS/.polarion/repositories/repositories.xml:
[/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
com.polarion.platform.repository.config.RepositoryConfigurationException: [/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:87) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocations(AbstractDataHandler.java:61) ~[platform-repository.jar:?]
	at $IDataHandler_19869165e0c.readLocations($IDataHandler_19869165e0c.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readLocations(RepositoryConfigService.java:291) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$3.runImpl(RepositoryConfigService.java:328) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction$1.runWEx(RepositoryConfigService.java:113) ~[platform-repository.jar:?]
	at com.polarion.core.util.RunnableWEx.runWRet(RunnableWEx.java:61) ~[util.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction.run(RepositoryConfigService.java:123) ~[platform-repository.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:361) ~[?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:58) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:422) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:412) ~[platform.jar:?]
	at $ISecurityService_19869165c27.doAsSystemUser($ISecurityService_19869165c27.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readConfiguration(RepositoryConfigService.java:324) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getConfigurationImpl(RepositoryConfigService.java:239) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfigurationImpl(RepositoryConfigService.java:199) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:177) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.addConfigurationListener(RepositoryConfigService.java:263) ~[platform-repository.jar:?]
	at $IRepositoryConfigService_19869165c35.addConfigurationListener($IRepositoryConfigService_19869165c35.java) ~[?:?]
	at com.polarion.platform.repository.external.internal.ExternalRepositoryProviderRegistry.initialize(ExternalRepositoryProviderRegistry.java:146) ~[platform-repository.jar:?]
	at $IExternalRepositoryProviderRegistry_19869165cf1.initialize($IExternalRepositoryProviderRegistry_19869165cf1.java) ~[?:?]
	at $IExternalRepositoryProviderRegistry_19869165cf0.initialize($IExternalRepositoryProviderRegistry_19869165cf0.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule.initModule(RevisionsPersistenceModule.java:353) ~[platform-persistence.jar:?]
	at $IObjectPersistenceModule_19869165de0.initModule($IObjectPersistenceModule_19869165de0.java) ~[?:?]
	at com.polarion.subterra.persistence.internal.PersistenceEngine.initModule(PersistenceEngine.java:251) ~[subterra-uniform-persistence.jar:?]
	at $IPersistenceEngine_19869165dc8.initModule($IPersistenceEngine_19869165dc8.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.LowLevelDataService.boot(LowLevelDataService.java:352) ~[platform-persistence.jar:?]
	at $ILowLevelPersistence_19869165ced.boot($ILowLevelPersistence_19869165ced.java) ~[?:?]
	at $ILowLevelPersistence_19869165cec.boot($ILowLevelPersistence_19869165cec.java) ~[?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.lambda$0(PlatformService.java:294) ~[launcher.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:423) [?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:69) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:417) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:407) [platform.jar:?]
	at $ISecurityService_19869165c28.doAsSystemUser($ISecurityService_19869165c28.java) [?:?]
	at $ISecurityService_19869165c27.doAsSystemUser($ISecurityService_19869165c27.java) [?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.bootPlatform(PlatformService.java:286) [launcher.jar:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.start(PlatformService.java:92) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.runImpl(PolarionSVNApplication.java:139) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.run(PolarionSVNApplication.java:94) [launcher.jar:?]
	at com.polarion.core.boot.launchers.BasicAppLauncher.launch(BasicAppLauncher.java:53) [boot.jar:?]
	at com.polarion.core.boot.impl.AppLaunchersManager.start(AppLaunchersManager.java:184) [boot.jar:?]
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196) [org.eclipse.equinox.app_1.3.500.v20171221-2204.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:388) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:243) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:656) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:592) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.run(Main.java:1498) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.main(Main.java:1471) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
Caused by: com.thoughtworks.xstream.converters.ConversionException: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:77) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
Caused by: com.thoughtworks.xstream.mapper.CannotResolveClassException: AutoBranchGitLab
	at com.thoughtworks.xstream.mapper.DefaultMapper.realClass(DefaultMapper.java:81) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.DynamicProxyMapper.realClass(DynamicProxyMapper.java:55) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.PackageAliasingMapper.realClass(PackageAliasingMapper.java:88) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ClassAliasingMapper.realClass(ClassAliasingMapper.java:79) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ArrayMapper.realClass(ArrayMapper.java:74) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.SecurityMapper.realClass(SecurityMapper.java:71) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.CachingMapper.realClass(CachingMapper.java:47) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.util.HierarchicalStreams.readClassType(HierarchicalStreams.java:29) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readBareItem(AbstractCollectionConverter.java:131) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readItem(AbstractCollectionConverter.java:117) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readCompleteItem(AbstractCollectionConverter.java:147) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.ArrayConverter.unmarshal(ArrayConverter.java:54) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:72) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
2025-08-02 12:42:14,848 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-02 12:42:14,859 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions from repository default in context ContextId[context [global]] finished
2025-08-02 12:42:14,860 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [FINISHED].
2025-08-02 12:42:14,860 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-08-02 12:42:14,860 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.213 s [100% getReadConfiguration (48x)] (48x), svn: 0.0814 s [85% info (18x)] (38x)
2025-08-02 12:42:14,886 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:42:14,886 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-02 12:42:14,886 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting repository for build artifacts-related changes
2025-08-02 12:42:14,886 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[context [global]]
2025-08-02 12:42:14,886 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[context [global]]
2025-08-02 12:42:14,888 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[context [global]] has been successfully processed
2025-08-02 12:42:14,889 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[context [global]] finished [ TIME 0.0029 s. ]
2025-08-02 12:42:14,889 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-02 12:42:14,889 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context [global]]
2025-08-02 12:42:14,889 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context [global]]
2025-08-02 12:42:14,909 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context [global]] has been successfully processed
2025-08-02 12:42:14,937 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context [global]] finished [ TIME 0.0471 s. ]
2025-08-02 12:42:14,937 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-02 12:42:14,937 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBS]
2025-08-02 12:42:14,937 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBS]
2025-08-02 12:42:14,959 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBS] has been successfully processed
2025-08-02 12:42:14,987 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBS] finished [ TIME 0.0499 s. ]
2025-08-02 12:42:14,987 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-02 12:42:14,987 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBSdev]
2025-08-02 12:42:14,987 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBSdev]
2025-08-02 12:42:15,004 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBSdev] has been successfully processed
2025-08-02 12:42:15,024 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBSdev] finished [ TIME 0.0376 s. ]
2025-08-02 12:42:15,024 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-02 12:42:15,024 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context --Demo Projects]
2025-08-02 12:42:15,024 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context --Demo Projects]
2025-08-02 12:42:15,033 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context --Demo Projects] has been successfully processed
2025-08-02 12:42:15,045 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context --Demo Projects] finished [ TIME 0.0203 s. ]
2025-08-02 12:42:15,045 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-02 12:42:15,045 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context library]
2025-08-02 12:42:15,045 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context library]
2025-08-02 12:42:15,054 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context library] has been successfully processed
2025-08-02 12:42:15,065 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context library] finished [ TIME 0.0205 s. ]
2025-08-02 12:42:15,065 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-02 12:42:15,065 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context hesai]
2025-08-02 12:42:15,065 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context hesai]
2025-08-02 12:42:15,079 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context hesai] has been successfully processed
2025-08-02 12:42:15,094 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context hesai] finished [ TIME 0.0282 s. ]
2025-08-02 12:42:15,094 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-02 12:42:15,094 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context elibrary]
2025-08-02 12:42:15,094 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context elibrary]
2025-08-02 12:42:15,110 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context elibrary] has been successfully processed
2025-08-02 12:42:15,125 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context elibrary] finished [ TIME 0.0309 s. ]
2025-08-02 12:42:15,125 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-02 12:42:15,125 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context drivepilot]
2025-08-02 12:42:15,125 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context drivepilot]
2025-08-02 12:42:15,134 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context drivepilot] has been successfully processed
2025-08-02 12:42:15,148 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context drivepilot] finished [ TIME 0.0232 s. ]
2025-08-02 12:42:15,148 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-02 12:42:15,148 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... repository inspection finished [ TIME 0.262 s. ]
2025-08-02 12:42:15,148 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.29 s. ]
2025-08-02 12:42:15,148 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.227 s [75% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.165 s [100% getReadConfiguration (54x)] (54x)
2025-08-02 12:42:15,148 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:42:15,148 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-02 12:42:15,148 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting BIR for new or removed builds
2025-08-02 12:42:15,161 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were removed (including calculations from previous run)
2025-08-02 12:42:15,161 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were added or modified
2025-08-02 12:42:15,161 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... BIR inspection finished [ TIME 0.0124 s. ]
2025-08-02 12:42:15,161 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-02 12:42:15,161 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:42:15,161 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-02 12:42:15,161 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing startup index events, starting iterations.
2025-08-02 12:42:15,161 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Iteration 1 - processing 5 events
2025-08-02 12:42:15,166 [main | u:p] INFO  com.polarion.alm.tracker.internal.planning.PlanFieldsProvider - livePlanXMLLocation: Location[path /default/.reports/xml/live-plan.xml]
2025-08-02 12:42:15,183 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener -  - reindexing 1 existing objects and 0 deleted objects.
2025-08-02 12:42:15,244 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-02 12:42:15,246 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-02 12:42:15,247 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-08-02 12:42:15,247 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Attachment
2025-08-02 12:42:15,247 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPage
2025-08-02 12:42:15,247 [main | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-08-02 12:42:15,271 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPage
2025-08-02 12:42:15,271 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Attachment
2025-08-02 12:42:15,271 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPageAttachment
2025-08-02 12:42:15,272 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-08-02 12:42:15,272 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-08-02 12:42:15,275 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-08-02 12:42:15,277 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPageAttachment
2025-08-02 12:42:15,280 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem
2025-08-02 12:42:15,280 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: UserGroup
2025-08-02 12:42:15,282 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: UserGroup
2025-08-02 12:42:15,283 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BaselineCollection
2025-08-02 12:42:15,284 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRun
2025-08-02 12:42:15,289 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BaselineCollection
2025-08-02 12:42:15,289 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRun
2025-08-02 12:42:15,289 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Build
2025-08-02 12:42:15,290 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleComment
2025-08-02 12:42:15,290 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Build
2025-08-02 12:42:15,292 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleComment
2025-08-02 12:42:15,292 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Comment
2025-08-02 12:42:15,292 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BuildArtifact
2025-08-02 12:42:15,292 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: DocumentWorkflowSignature
2025-08-02 12:42:15,293 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BuildArtifact
2025-08-02 12:42:15,293 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPageAttachment
2025-08-02 12:42:15,294 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPageAttachment
2025-08-02 12:42:15,295 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Comment
2025-08-02 12:42:15,295 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: DocumentWorkflowSignature
2025-08-02 12:42:15,296 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-08-02 12:42:15,296 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem
2025-08-02 12:42:15,296 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Baseline
2025-08-02 12:42:15,297 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-08-02 12:42:15,299 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Baseline
2025-08-02 12:42:15,299 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Plan
2025-08-02 12:42:15,302 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Plan
2025-08-02 12:42:15,302 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunAttachment
2025-08-02 12:42:15,303 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkRecord
2025-08-02 12:42:15,304 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunAttachment
2025-08-02 12:42:15,304 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem-OutlineNumbers
2025-08-02 12:42:15,305 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-08-02 12:42:15,306 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem-OutlineNumbers
2025-08-02 12:42:15,306 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleAttachment
2025-08-02 12:42:15,307 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-08-02 12:42:15,308 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunComment
2025-08-02 12:42:15,308 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunComment
2025-08-02 12:42:15,308 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-08-02 12:42:15,309 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPage
2025-08-02 12:42:15,309 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkRecord
2025-08-02 12:42:15,310 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Module
2025-08-02 12:42:15,317 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-08-02 12:42:15,318 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Revision
2025-08-02 12:42:15,319 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-08-02 12:42:15,321 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPage
2025-08-02 12:42:15,330 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleAttachment
2025-08-02 12:42:15,335 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Module
2025-08-02 12:42:15,337 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}272
2025-08-02 12:42:15,337 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}270
2025-08-02 12:42:15,337 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}224
2025-08-02 12:42:15,337 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}274
2025-08-02 12:42:15,337 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}273
2025-08-02 12:42:15,337 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}225
2025-08-02 12:42:15,337 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}226
2025-08-02 12:42:15,337 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}271
2025-08-02 12:42:15,337 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}228
2025-08-02 12:42:15,337 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}230
2025-08-02 12:42:15,337 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}231
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}232
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}233
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}234
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}235
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}239
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}240
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}241
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}242
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}243
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}245
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}246
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}248
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}249
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}250
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}251
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}253
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}254
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}255
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}256
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}257
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}258
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}259
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}260
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}261
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}262
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}263
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}264
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}265
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}267
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}268
2025-08-02 12:42:15,338 [PolarionDocIdCreator-4] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}269
2025-08-02 12:42:15,338 [PolarionDocIdCreator-1] INFO  com.polarion.subterra.index.impl.lucene.baseline.PolarionDocIdCreator - Bloom filter loading for 28 indices took  [ TIME 0.102 s. ]
2025-08-02 12:42:15,343 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.DelegatingCalculatedFieldsListener - Calculated fields mode: async
2025-08-02 12:42:15,345 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing took  [ TIME 0.184 s. ]
2025-08-02 12:42:15,345 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.18 s. ]
2025-08-02 12:42:15,345 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.184 s [100% doFinishStartup (1x)] (1x), commit: 0.0513 s [100% Revision (1x)] (1x), Lucene: 0.0251 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0127 s [100% objectsToInv (1x)] (1x)
2025-08-02 12:42:15,345 [main | u:p] INFO  com.polarion.platform.internal.service.repository.ListenerManager - Starting the pulling job for repository: default
2025-08-02 12:42:15,345 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization finished [ TIME 1.54 s. ]
2025-08-02 12:42:15,345 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:42:15,345 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-02 12:42:15,346 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.CalculatedFieldsStorage - Checking integrity of calculated fields storage /opt/polarion/data/workspace/polarion-data/calculated-fields
2025-08-02 12:42:15,352 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-02 12:42:15,352 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:42:15,352 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-02 12:42:15,373 [main | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - Updating local scheduler state: start
2025-08-02 12:42:15,379 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-08-02 12:42:15,383 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-02 12:42:15,383 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-08-02 12:42:15,383 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-08-02 12:42:15,383 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutting down.
2025-08-02 12:42:15,383 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED paused.
2025-08-02 12:42:15,383 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-08-02 12:42:15,384 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-08-02 12:42:15,384 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-02 12:42:15,384 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-08-02 12:42:15,384 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-08-02 12:42:15,385 [main | u:p | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - 15 scheduled job(s) configured
2025-08-02 12:42:15,389 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
2025-08-02 12:42:15,569 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot finished
2025-08-02 12:42:15,569 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform started
2025-08-02 12:42:15,578 [main] INFO  com.polarion.portal.tomcat.TomcatPlugin - Tomcat home directory was set to /opt/polarion/data/workspace/.metadata/.plugins/com.polarion.portal.tomcat
2025-08-02 12:42:15,583 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Starting Tomcat...
2025-08-02 12:42:15,650 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: webui, contextRoot: webapp/webui, plugin: com.polarion.alm.ui, priority: -10]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,650 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion, contextRoot: webapp/authapp, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,650 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/.well-known, contextRoot: webapp/well-known, plugin: com.polarion.platform, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,650 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ProjectPlanGantt, contextRoot: webapp, plugin: com.polarion.alm.ProjectPlanGantt_new, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,650 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/activate, contextRoot: webapp/activation, plugin: com.polarion.psvn.launcher, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,651 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/announcements, contextRoot: webapp/announcements, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,651 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/bir, contextRoot: webapp/bir, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,651 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/checklist, contextRoot: src/main/webapp, plugin: com.fasnote.alm.checklist, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,651 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/codemirror-modes, contextRoot: webapp/codemirror-modes, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,651 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/defect, contextRoot: webapp, plugin: com.finething.hesai.defect, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,651 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/doorsconnector, contextRoot: webapp, plugin: com.polarion.synchronizer, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,651 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/export, contextRoot: webapp/export, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,651 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/fileupload, contextRoot: webapp/fileupload, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,651 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/gwt, contextRoot: war, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,651 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/hesai-ai, contextRoot: webapp, plugin: com.finething.hesai.ai, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,651 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/icons, contextRoot: webapp/icons, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,651 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/internal-login, contextRoot: webapp/internal-login, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,651 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/livechecklist, contextRoot: webapp, plugin: com.teamlive.livechecklist, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,651 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/module-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,651 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/modulehome, contextRoot: webapp/module-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,652 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/mxgraph, contextRoot: draw.io/war, plugin: com.polarion.alm.ui.diagrams.mxgraph, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,652 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/oauth-feishu, contextRoot: webapp, plugin: com.fasnote.alm.auth.feishu, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,652 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/page-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,652 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/perf-testing, contextRoot: webapp/perf-testing, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,652 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/plugin-manage, contextRoot: webapp, plugin: com.fasnote.alm.plugin.manage, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,652 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/print, contextRoot: webapp/print, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,652 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/register, contextRoot: webapp/register, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,652 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rest, contextRoot: webapp, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,652 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ria, contextRoot: webapp/ria, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,652 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/richpagehome, contextRoot: webapp/richpage-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,653 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt, contextRoot: src/main/webapp, plugin: com.siemens.polarion.rt, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,653 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt-connect, contextRoot: ws, plugin: com.siemens.polarion.rt.communication.polarion, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,653 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/scripting, contextRoot: webapp/scripting, plugin: com.polarion.scripting.servlet, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,653 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/sdk, contextRoot: webapp/sdk, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,653 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/svnwebclient, contextRoot: webapp, plugin: org.polarion.svnwebclient, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,653 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/swagger, contextRoot: webapp/swagger, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,653 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/synchronizer, contextRoot: webapp, plugin: com.polarion.synchronizer.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,653 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/template-download, contextRoot: webapp/project-template, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,653 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/testrun-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,653 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/tour, contextRoot: webapp/tour, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,653 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/watermark, contextRoot: webapp, plugin: com.fasnote.alm.watermark, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,653 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,653 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment-auth, contextRoot: webapp/wi-attachment-auth, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,653 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/widget-resource, contextRoot: webapp/widget-resource, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,654 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wiki, contextRoot: src/main/webapp, plugin: com.polarion.alm.wiki, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,654 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/workreport, contextRoot: webapp/workreport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,654 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ws, contextRoot: ws, plugin: com.polarion.alm.ws, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,654 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/xunitimport, contextRoot: webapp/xunitimport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,654 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/oslc, contextRoot: webapp, plugin: com.polarion.alm.oslc, priority: 1]'; protocol: AJP/1.3, port: 8889
2025-08-02 12:42:15,700 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Initializing ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-08-02 12:42:15,708 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-02 12:42:15,708 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-08-02 12:42:15,730 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1f63fdf4] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,730 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@9d7f34a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,730 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@335cb6cd] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,730 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@63ceb4ca] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,730 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@623fb8e7] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,730 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1639f9ae] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,746 [Catalina-utility-6] INFO  org.apache.catalina.startup.ContextConfig - No global web.xml found
2025-08-02 12:42:15,753 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,753 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,754 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,756 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,756 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [admin] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,776 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@47fb7f9] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,776 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@65ab87e4] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,778 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@128f0a3c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,780 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,780 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,781 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@49e42064] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,781 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@67d7f791] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,781 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6ebf8a38] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,783 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5b14131] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,783 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@8b9f657] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,785 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,785 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,787 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,788 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,790 [Catalina-utility-5] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 12:42:15,791 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2c14eb95] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,792 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4d443a45] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,794 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3e4e4fda] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,794 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@af5e2b3] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,798 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,799 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@59a9a490] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,799 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,800 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@14b45f13] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,805 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,808 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2e83f3f5] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,808 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@67c4963e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,809 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@74d3ed15] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,812 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,813 [Catalina-utility-1] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 12:42:15,813 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,817 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7b1e2dad] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,819 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3bc00b46] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,821 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@185f9c7] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,822 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,823 [Catalina-utility-1] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/oauth-feishu] - For security constraints with URL pattern [/userinfo] only the HTTP methods [POST GET] are covered. All other methods are uncovered.
2025-08-02 12:42:15,825 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,826 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3774ea9a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,826 [Catalina-utility-1] INFO  com.fasnote.alm.injection.osgi.InjectionActivator - 启动ALM依赖注入框架
2025-08-02 12:42:15,828 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7d700d35] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,835 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@66d774b8] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,847 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 创建OSGi感知的纯粹依赖注入器
2025-08-02 12:42:15,847 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 初始化包扫描提供者跟踪机制
2025-08-02 12:42:15,847 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 包扫描提供者跟踪机制初始化完成
2025-08-02 12:42:15,847 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 开始注册 OSGi 服务到依赖注入容器
2025-08-02 12:42:15,848 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: org.osgi.framework.BundleContext
2025-08-02 12:42:15,848 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - OSGi 服务注册完成，已注册 1 个 OSGi 服务提供者
2025-08-02 12:42:15,848 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.osgi.InjectionActivator - 启动Bundle跟踪机制
2025-08-02 12:42:15,850 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@10faca5b] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,851 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 包扫描提供者跟踪已启动
2025-08-02 12:42:15,851 [Catalina-utility-1] INFO  com.fasnote.alm.injection.osgi.InjectionActivator - 包扫描提供者跟踪机制启动成功
2025-08-02 12:42:15,851 [Catalina-utility-1] INFO  com.fasnote.alm.injection.osgi.InjectionActivator - ALM依赖注入框架启动成功
2025-08-02 12:42:15,851 [Catalina-utility-1] INFO  com.fasnote.alm.auth.feishu.Activator - Feishu Authentication Plugin starting...
2025-08-02 12:42:15,852 [Catalina-utility-1] INFO  com.fasnote.alm.auth.feishu.Activator - 注册 FeishuPackageScanProvider 为 OSGi 服务...
2025-08-02 12:42:15,852 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.auth.feishu 注册DI扫描包路径: [com.fasnote.alm.auth.feishu]
2025-08-02 12:42:15,852 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.auth.feishu 就绪，开始扫描服务 (状态: 8, 扫描包: [com.fasnote.alm.auth.feishu])
2025-08-02 12:42:15,852 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasnote.alm.auth.feishu
2025-08-02 12:42:15,852 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 开始扫描 Bundle 服务: com.fasnote.alm.auth.feishu (状态: 8, 扫描包: [com.fasnote.alm.auth.feishu])
2025-08-02 12:42:15,853 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,856 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@62d6611a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,857 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3e9298c5] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,858 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.auth.feishu 的包 com.fasnote.alm.auth.feishu 中发现 19 个类文件
2025-08-02 12:42:15,859 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory$FeishuAuthenticatorManagerInvocationHandler (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,859 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuOAuth2LoginClient (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,860 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,860 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.config.FeishuConfigurationAdapter (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,860 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuOAuth2Authenticator (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,861 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,862 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,862 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,872 [Catalina-utility-1] INFO  com.fasnote.alm.plugin.manage.Activator - 启动许可证管理插件...
2025-08-02 12:42:15,872 [Catalina-utility-1] DEBUG com.fasnote.alm.plugin.manage.Activator - 初始化许可证配置...
2025-08-02 12:42:15,872 [Catalina-utility-1] DEBUG com.fasnote.alm.plugin.manage.Activator - 从环境变量加载配置完成
2025-08-02 12:42:15,873 [Catalina-utility-1] DEBUG com.fasnote.alm.plugin.manage.Activator - 许可证配置初始化完成
2025-08-02 12:42:15,873 [Catalina-utility-1] INFO  com.fasnote.alm.plugin.manage.Activator - 注册 LicensePackageScanProvider 为 OSGi 服务...
2025-08-02 12:42:15,873 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,873 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.plugin.manage 注册DI扫描包路径: [com.fasnote.alm.plugin.manage.injection.module]
2025-08-02 12:42:15,873 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.plugin.manage 就绪，开始扫描服务 (状态: 8, 扫描包: [com.fasnote.alm.plugin.manage.injection.module])
2025-08-02 12:42:15,873 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,873 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 开始扫描 Bundle 服务: com.fasnote.alm.plugin.manage (状态: 8, 扫描包: [com.fasnote.alm.plugin.manage.injection.module])
2025-08-02 12:42:15,874 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.plugin.manage 的包 com.fasnote.alm.plugin.manage.injection.module 中发现 5 个类文件
2025-08-02 12:42:15,876 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,876 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 发现 IModule 实现: com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule (Bundle: com.fasnote.alm.plugin.manage)
2025-08-02 12:42:15,877 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功使用无参构造函数创建模块: com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule
2025-08-02 12:42:15,881 [Catalina-utility-6] INFO  org.apache.tomcat.dbcp.dbcp2.BasicDataSourceFactory - Name = XWikiDS Ignoring unknown property: value of "DB Connection" for "description" property
2025-08-02 12:42:15,881 [Catalina-utility-1] INFO  com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule - 配置插件管理主模块...
2025-08-02 12:42:15,881 [Catalina-utility-1] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseModule使用无参构造函数创建
2025-08-02 12:42:15,881 [Catalina-utility-1] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 配置统一许可证模块（重构后）...
2025-08-02 12:42:15,881 [Catalina-utility-1] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 注册核心组件...
2025-08-02 12:42:15,882 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: java.util.Map
2025-08-02 12:42:15,883 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,883 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.security.MachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,883 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,883 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.security.MachineCodeProvider, 实现: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,891 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.security.MachineCodeProvider -> com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider  [LAZY_SINGLETON]
2025-08-02 12:42:15,894 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,894 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,894 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,894 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.config.LicenseConfiguration, 实例类: com.fasnote.alm.plugin.manage.config.LicenseConfiguration, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,894 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage.config.LicenseConfiguration, 实例类型: LicenseConfiguration
2025-08-02 12:42:15,894 [Catalina-utility-1] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 核心组件注册完成
2025-08-02 12:42:15,894 [Catalina-utility-1] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 注册管理器组件...
2025-08-02 12:42:15,896 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.BundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,896 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IBundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,896 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.BundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,896 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IBundleManager, 实现: com.fasnote.alm.plugin.manage.core.BundleManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,896 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IBundleManager -> com.fasnote.alm.plugin.manage.core.BundleManager  [LAZY_SINGLETON]
2025-08-02 12:42:15,897 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.ClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,897 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,897 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.ClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,897 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IClassLoaderManager, 实现: com.fasnote.alm.plugin.manage.core.ClassLoaderManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,897 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IClassLoaderManager -> com.fasnote.alm.plugin.manage.core.ClassLoaderManager  [LAZY_SINGLETON]
2025-08-02 12:42:15,897 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,897 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,897 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,897 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseValidator, 实现: com.fasnote.alm.plugin.manage.core.LicenseValidator, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,897 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseValidator -> com.fasnote.alm.plugin.manage.core.LicenseValidator  [LAZY_SINGLETON]
2025-08-02 12:42:15,898 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,898 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,898 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,898 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager, 实现: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,898 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager -> com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager  [LAZY_SINGLETON]
2025-08-02 12:42:15,898 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,899 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,899 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,899 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator, 实现: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,899 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator  [LAZY_SINGLETON]
2025-08-02 12:42:15,899 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,899 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,899 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,899 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager, 实现: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,899 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager  [LAZY_SINGLETON]
2025-08-02 12:42:15,899 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,899 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,899 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,899 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseFileManager, 实现: com.fasnote.alm.plugin.manage.core.LicenseFileManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,899 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseFileManager -> com.fasnote.alm.plugin.manage.core.LicenseFileManager  [LAZY_SINGLETON]
2025-08-02 12:42:15,899 [Catalina-utility-1] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 管理器组件注册完成
2025-08-02 12:42:15,900 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,900 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,900 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,900 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseManager, 实现: com.fasnote.alm.plugin.manage.core.LicenseManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,900 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseManager -> com.fasnote.alm.plugin.manage.core.LicenseManager  [LAZY_SINGLETON]
2025-08-02 12:42:15,900 [Catalina-utility-1] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseManager接口绑定配置完成
2025-08-02 12:42:15,900 [Catalina-utility-1] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseAwareServiceResolver 将通过自动扫描机制注册
2025-08-02 12:42:15,901 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: com.fasnote.alm.plugin.manage.api.LicenseAware
2025-08-02 12:42:15,901 [Catalina-utility-1] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 统一许可证模块配置完成（重构后）
2025-08-02 12:42:15,909 [Catalina-utility-1] INFO  com.fasnote.alm.plugin.manage.web.injection.WebModule - 配置Web层依赖注入模块...
2025-08-02 12:42:15,909 [Catalina-utility-1] DEBUG com.fasnote.alm.plugin.manage.web.injection.WebModule - 注册Web服务...
2025-08-02 12:42:15,910 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,910 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,910 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,910 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl, 实现: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,910 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl  [LAZY_SINGLETON]
2025-08-02 12:42:15,910 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,910 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.web.service.PluginManagementService -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,910 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,910 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.web.service.PluginManagementService, 实现: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,911 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.web.service.PluginManagementService -> com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl  [LAZY_SINGLETON]
2025-08-02 12:42:15,911 [Catalina-utility-1] DEBUG com.fasnote.alm.plugin.manage.web.injection.WebModule - Web服务注册完成
2025-08-02 12:42:15,911 [Catalina-utility-1] INFO  com.fasnote.alm.plugin.manage.web.injection.WebModule - Web层依赖注入模块配置完成
2025-08-02 12:42:15,911 [Catalina-utility-1] INFO  com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule - 插件管理主模块配置完成
2025-08-02 12:42:15,911 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 已安装模块: PluginManageMainModule (优先级: 0)
2025-08-02 12:42:15,911 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功安装模块: com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule
2025-08-02 12:42:15,911 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.LicensePackageScanProvider (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,911 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.LicenseModule$LicenseAwareProvider$1 (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,911 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.LicenseModule$LicenseAwareProvider (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,911 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.LicenseModule (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,912 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 发现 IModule 实现: com.fasnote.alm.plugin.manage.injection.module.LicenseModule (Bundle: com.fasnote.alm.plugin.manage)
2025-08-02 12:42:15,912 [Catalina-utility-1] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseModule使用无参构造函数创建
2025-08-02 12:42:15,912 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功使用无参构造函数创建模块: com.fasnote.alm.plugin.manage.injection.module.LicenseModule
2025-08-02 12:42:15,912 [Catalina-utility-1] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 配置统一许可证模块（重构后）...
2025-08-02 12:42:15,912 [Catalina-utility-1] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 注册核心组件...
2025-08-02 12:42:15,912 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: java.util.Map
2025-08-02 12:42:15,912 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,912 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.security.MachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,912 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,912 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.security.MachineCodeProvider, 实现: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.security.MachineCodeProvider -> com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider  [LAZY_SINGLETON]
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.config.LicenseConfiguration, 实例类: com.fasnote.alm.plugin.manage.config.LicenseConfiguration, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,913 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage.config.LicenseConfiguration, 实例类型: LicenseConfiguration
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 核心组件注册完成
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 注册管理器组件...
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.BundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IBundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.BundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IBundleManager, 实现: com.fasnote.alm.plugin.manage.core.BundleManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IBundleManager -> com.fasnote.alm.plugin.manage.core.BundleManager  [LAZY_SINGLETON]
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.ClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.ClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IClassLoaderManager, 实现: com.fasnote.alm.plugin.manage.core.ClassLoaderManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IClassLoaderManager -> com.fasnote.alm.plugin.manage.core.ClassLoaderManager  [LAZY_SINGLETON]
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseValidator, 实现: com.fasnote.alm.plugin.manage.core.LicenseValidator, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseValidator -> com.fasnote.alm.plugin.manage.core.LicenseValidator  [LAZY_SINGLETON]
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,913 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager, 实现: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager -> com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager  [LAZY_SINGLETON]
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator, 实现: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator  [LAZY_SINGLETON]
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager, 实现: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager  [LAZY_SINGLETON]
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseFileManager, 实现: com.fasnote.alm.plugin.manage.core.LicenseFileManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseFileManager -> com.fasnote.alm.plugin.manage.core.LicenseFileManager  [LAZY_SINGLETON]
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 管理器组件注册完成
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseManager, 实现: com.fasnote.alm.plugin.manage.core.LicenseManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseManager -> com.fasnote.alm.plugin.manage.core.LicenseManager  [LAZY_SINGLETON]
2025-08-02 12:42:15,914 [Catalina-utility-1] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseManager接口绑定配置完成
2025-08-02 12:42:15,914 [Catalina-utility-1] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseAwareServiceResolver 将通过自动扫描机制注册
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: com.fasnote.alm.plugin.manage.api.LicenseAware
2025-08-02 12:42:15,914 [Catalina-utility-1] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 统一许可证模块配置完成（重构后）
2025-08-02 12:42:15,914 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 已安装模块: RefactoredLicenseModule (优先级: 5)
2025-08-02 12:42:15,914 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功安装模块: com.fasnote.alm.plugin.manage.injection.module.LicenseModule
2025-08-02 12:42:15,914 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle 服务扫描完成: com.fasnote.alm.plugin.manage - 耗时: 41ms, 模块: 2, 服务: 0, 错误: 0
2025-08-02 12:42:15,914 [Catalina-utility-1] INFO  com.fasnote.alm.plugin.manage.Activator - LicensePackageScanProvider OSGi 服务注册成功
2025-08-02 12:42:15,914 [Catalina-utility-1] INFO  com.fasnote.alm.plugin.manage.Activator - 扫描包路径: [com.fasnote.alm.plugin.manage.injection.module]
2025-08-02 12:42:15,914 [Catalina-utility-1] INFO  com.fasnote.alm.plugin.manage.Activator - 许可证管理插件启动成功
2025-08-02 12:42:15,915 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 在注解 com.fasnote.alm.plugin.manage.annotation.LicenseImplementation 中发现 @Service 元注解
2025-08-02 12:42:15,915 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 发现服务相关注解: LicenseImplementation 在类 com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer
2025-08-02 12:42:15,915 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 发现服务类: com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu)
2025-08-02 12:42:15,918 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 自动注册服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu)
2025-08-02 12:42:15,920 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 设置默认服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer
2025-08-02 12:42:15,920 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 添加服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer (总数: 1)
2025-08-02 12:42:15,920 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu
2025-08-02 12:42:15,920 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu
2025-08-02 12:42:15,921 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuStateUtils (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,922 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuUserInfoMapper (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,922 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuUserInfoMapper$FeishuUserData (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,924 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuApiClient (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,925 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuUserInfoMapper$FeishuApiResponse (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,925 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.OAuth2UrlHandler (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,926 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuOAuth2LoginClient$1 (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,927 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuOAuth2DefaultLoginHandler (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,927 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuUserInfoServlet (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,928 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,931 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 在注解 com.fasnote.alm.plugin.manage.annotation.FallbackImplementation 中发现 @Service 元注解
2025-08-02 12:42:15,931 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 发现服务相关注解: FallbackImplementation 在类 com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer
2025-08-02 12:42:15,931 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 发现服务类: com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu)
2025-08-02 12:42:15,931 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 自动注册服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu)
2025-08-02 12:42:15,931 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 添加服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer (总数: 2)
2025-08-02 12:42:15,931 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu
2025-08-02 12:42:15,931 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu
2025-08-02 12:42:15,931 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuPackageScanProvider (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,931 [Catalina-utility-1] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.Activator (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 12:42:15,931 [Catalina-utility-1] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle 服务扫描完成: com.fasnote.alm.auth.feishu - 耗时: 79ms, 模块: 0, 服务: 2, 错误: 0
2025-08-02 12:42:15,931 [Catalina-utility-1] INFO  com.fasnote.alm.auth.feishu.Activator - FeishuPackageScanProvider OSGi 服务注册成功
2025-08-02 12:42:15,931 [Catalina-utility-1] INFO  com.fasnote.alm.auth.feishu.Activator - 扫描包路径: [com.fasnote.alm.auth.feishu]
2025-08-02 12:42:15,931 [Catalina-utility-1] INFO  com.fasnote.alm.auth.feishu.Activator - Feishu Authentication Plugin started successfully
2025-08-02 12:42:15,931 [Catalina-utility-1] INFO  com.fasnote.alm.auth.feishu.Activator - 飞书插件实现类将通过 OSGi 服务自动扫描注册
2025-08-02 12:42:15,947 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@136468c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,953 [Catalina-utility-4] INFO  org.polarion.svncommons.commentscache.CommentsCache - Initializing comments cache. Id: http://localhost/repo, repository: http://localhost/repo/, url: http://localhost/repo/, cache directory: /opt/polarion/data/workspace/polarion-data/log-messages-cache, cache page size: 100
2025-08-02 12:42:15,966 [Catalina-utility-6] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 12:42:15,972 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@76378274] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:15,975 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:15,977 [Catalina-utility-4] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 12:42:16,031 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@753a5ef4] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:16,034 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:16,044 [Catalina-utility-6] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 12:42:16,048 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3021acbe] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:16,051 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:16,055 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5695fad9] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:16,057 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:16,060 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@31be1586] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:16,079 [Catalina-utility-6] INFO  com.polarion.portal.velocity.VelocityPathManager - VelocityTemplatesPath=/opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/authapp/, /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/, /opt/polarion/polarion/plugins/com.polarion.alm.wiki_3.22.1/src/main/webapp/, ., /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/
2025-08-02 12:42:16,152 [Catalina-utility-1] WARN  org.springframework.web.context.support.AnnotationConfigWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#simpleHealth()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#health() mapped.
2025-08-02 12:42:16,154 [Catalina-utility-1] FATAL org.springframework.web.servlet.DispatcherServlet - Context initialization failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#simpleHealth()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#health() mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1794) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264) [catalina.jar:9.0.53]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386) [catalina.jar:9.0.53]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628) [?:?]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#simpleHealth()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#health() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:636) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:603) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:318) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:378) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:75) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:288) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:286) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	... 30 more
2025-08-02 12:42:16,154 [Catalina-utility-1] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/checklist] - Servlet.init() for servlet [dispatcher] threw exception
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#simpleHealth()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#health() mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1794) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) ~[javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264) [catalina.jar:9.0.53]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386) [catalina.jar:9.0.53]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628) [?:?]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#simpleHealth()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#health() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:636) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:603) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:318) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:378) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:75) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:288) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:286) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	... 30 more
2025-08-02 12:42:16,156 [Catalina-utility-1] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/checklist] - Servlet [dispatcher] in web application [/polarion/checklist] threw load() exception
java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#simpleHealth()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#health() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:636) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:603) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:318) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:378) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:75) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:288) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:286) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) ~[javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264) [catalina.jar:9.0.53]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386) [catalina.jar:9.0.53]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628) [?:?]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-02 12:42:16,159 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@41d783f4] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:16,168 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@649e878d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:16,216 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2032cd25] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:16,218 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:16,219 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5197d8bd] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:16,221 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1a4323ac] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:16,223 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:16,227 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4fb63dde] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:16,228 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@60496385] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:16,234 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:16,240 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@54f0372a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:16,243 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3a4af9a7] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:16,244 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:16,245 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 12:42:16,249 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7b50d62] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:16,250 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6b8f9714] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:16,257 [Catalina-utility-6] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 12:42:16,279 [Catalina-utility-2] INFO  com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Spring context refreshed, scanning for prompt variables...
2025-08-02 12:42:16,282 [Catalina-utility-2] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: polarionTool, Class: com.finething.hesai.ai.util.VelocityPolarionTool, Methods found: 29
2025-08-02 12:42:16,282 [Catalina-utility-2] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: enumTool, Class: com.finething.hesai.ai.util.EnumUtil, Methods found: 1
2025-08-02 12:42:16,282 [Catalina-utility-2] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: linkWorkItemUtil, Class: com.finething.hesai.ai.util.LinkWorkItemUtil, Methods found: 6
2025-08-02 12:42:16,284 [Catalina-utility-2] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: polarionContext, Class: com.finething.hesai.ai.service.impl.PolarionContextServiceImpl, Methods found: 4
2025-08-02 12:42:16,284 [Catalina-utility-2] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - No methods with @MethodDescription found for variable: polarionHelper, Class: com.finething.hesai.ai.service.impl.DefaultVariableDescriptionProvider$HelperMethods
2025-08-02 12:42:16,285 [Catalina-utility-2] INFO  com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Finished scanning. Found 4 prompt variables.
2025-08-02 12:42:16,291 [Catalina-utility-1] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-08-02 12:42:16,343 [Catalina-utility-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Started.
2025-08-02 12:42:16,858 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-08-02 12:42:16,861 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[90]')
2025-08-02 12:42:16,861 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[90]')
2025-08-02 12:42:16,864 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[96]')
2025-08-02 12:42:16,875 [Catalina-utility-1] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-08-02 12:42:16,876 [Catalina-utility-1] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[90]')
2025-08-02 12:42:16,877 [Catalina-utility-1] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[92]')
2025-08-02 12:42:17,124 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Starting ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-08-02 12:42:17,129 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Tomcat is listening on port 8889 using AJP/1.3 protocol with 600000 timeout in millis
2025-08-02 12:42:17,129 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Starting Help Service...
2025-08-02 12:42:17,132 [main] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@42ffed17] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 12:42:17,136 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Help Service started
2025-08-02 12:42:17,181 [main | u:p] INFO  com.xpn.xwiki.XWiki - xwiki.cfg taken from /WEB-INF/xwiki.cfg because the XWikiConfig variable is not set in the context
2025-08-02 12:42:17,603 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-08-02 12:42:17,623 [Thread-33] INFO  class com.polarion.alm.server.util.ChartExporterStartup - Chart renderer says: Server started on 127.0.0.1:34567
2025-08-02 12:42:17,703 [ajp-nio-127.0.0.1-8889-exec-1 | cID:69167ad7-c0a844bd-2de76568-1899d118] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-08-02 12:42:17,722 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-08-02 12:42:17,722 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
2025-08-02 12:42:17,736 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" has id 69167b00-c0a844bd-2de76568-72fe7a8f
2025-08-02 12:42:17,737 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to UNSCHEDULED
2025-08-02 12:42:17,738 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "Attachment Indexer" is /opt/polarion/data/workspace/polarion-data/jobs/20250802-1242
2025-08-02 12:42:17,739 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" runs as user "polarion"
2025-08-02 12:42:17,740 [main | u:p] INFO  com.polarion.platform.monitoring - Next slow periodic actions will be executed on Sun Aug 03 01:00:17 CST 2025
2025-08-02 12:42:17,740 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.tracker.internal.HttpsConfiguratorStartup successfully initialized
2025-08-02 12:42:17,741 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.ChartExporterStartup successfully initialized
2025-08-02 12:42:17,741 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.wiki.WikiPlugin successfully initialized
2025-08-02 12:42:17,741 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.qcentre.internal.QCentreStartup successfully initialized
2025-08-02 12:42:17,741 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.siemens.polarion.rt.communication.connection.RtCommunicationStartup successfully initialized
2025-08-02 12:42:17,741 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to ACTIVATING
2025-08-02 12:42:17,741 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.internal.startup.NotificationServerStartup successfully initialized
2025-08-02 12:42:17,741 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.subterra.index.impl.IndexingJobsStartup successfully initialized
2025-08-02 12:42:17,741 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.ui.server.ServerStartup successfully initialized
2025-08-02 12:42:17,741 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.FormulaServerStartup successfully initialized
2025-08-02 12:42:17,741 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.monitoring.internal.MonitoringServiceStart successfully initialized
2025-08-02 12:42:17,742 [main] INFO  com.polarion.platform.monitoring - Executing actions from stage POSTBOOT
2025-08-02 12:42:17,742 [main] INFO  com.polarion.platform.monitoring - Executing action 'polarion.info.cache.statistics'
2025-08-02 12:42:17,742 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to WAITING
2025-08-02 12:42:17,751 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to RUNNING
2025-08-02 12:42:17,752 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" has id 69167b12-c0a844bd-2de76568-59af556a
2025-08-02 12:42:17,752 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to UNSCHEDULED
2025-08-02 12:42:17,752 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "DB History Creator" is /opt/polarion/data/workspace/polarion-data/jobs/20250802-1242_0
2025-08-02 12:42:17,753 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" runs as user "polarion"
2025-08-02 12:42:17,754 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to ACTIVATING
2025-08-02 12:42:17,755 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to WAITING
2025-08-02 12:42:17,755 [main] INFO  com.polarion.platform.monitoring - polarion.info.cache.statistics (Statistics of caches) = Statistics of caches 
                         CACHE       HITS     MISSES  HIT_RATIO       GETS       PUTS    REMOVAL   EVICTION 
     attachments-content-cache          0          0          0%          0          0          0          0 
                baseline-cache          0          0          0%          0          0          0          0 
                            bq          0          0          0%          0          0          0          0 
                dao-attachment          0          0          0%          0          0          0          0 
                   dao-comment          0          0          0%          0          0          0          0 
                    dao-global          0          4          0%          4          0          0          0 
                    dao-module          0          0          0%          0          0          0          0 
          dao-moduleattachment          0          0          0%          0          0          0          0 
             dao-modulecomment          0          0          0%          0          0          0          0 
                      dao-plan          0          0          0%          0          0          0          0 
                   dao-project          0          0          0%          0          0          0          0 
              dao-projectgroup          0          6          0%          6          3          0          0 
                  dao-richpage          0          0          0%          0          0          0          0 
        dao-richpageattachment          0          0          0%          0          0          0          0 
                   dao-testrun          0          0          0%          0          0          0          0 
         dao-testrunattachment          0          0          0%          0          0          0          0 
                      dao-user          0          0          0%          0          0          0          0 
                  dao-wikipage          0          0          0%          0          0          0          0 
        dao-wikipageattachment          0          0          0%          0          0          0          0 
                  dao-workitem          0          0          0%          0          0          0          0 
      derived-linked-revisions          0          0          0%          0          0          0          0 
                  formulas-svg          0          0          0%          0          0          0          0 
                github-commits          0          0          0%          0          0          0          0 
            historical-queries          0          0          0%          0          0          0          0 
      historical-queries-sizes          0          0          0%          0          0          0          0 
        oslc-linked-item-cache          0          0          0%          0          0          0          0 
               plan-statistics          0          0          0%          0          0          0          0 
                   rmd-default          4          5         44%          9          7          0          0 
                   ss-combined          0          0          0%          0          0          0          0 
                    ss-context          0          0          0%          0          0          0          0 
                     wiki-page          2          8         20%         10          9          1          0 
              wiki-page-exists          0          0          0%          0          9          1          0 

 [Sat Aug 02 12:42:17 CST 2025]
2025-08-02 12:42:17,759 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to RUNNING
2025-08-02 12:42:17,761 [main] INFO  com.polarion.platform.monitoring - Finished with actions from stage POSTBOOT: {OK=1}
2025-08-02 12:42:17,761 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.41 s. ]
2025-08-02 12:42:17,761 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.288 s [90% info (158x)] (168x)
2025-08-02 12:42:17,761 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 12:42:17,762 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.77 s. ]
2025-08-02 12:42:17,762 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 12:42:17,770 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 3, persistence listener: 0.0159 s [81% indexRefreshPersistenceListener (1x)] (7x), notification worker: 0.00966 s [32% TestRunActivityCreator (1x), 30% WorkItemActivityCreator (1x), 30% PlanActivityCreator (1x)] (4x)
2025-08-02 12:42:17,771 [PreLoadDataService] INFO  com.polarion.psvn.launcher.internal.data.PreLoadDataService - Preloading data ...
2025-08-02 12:42:17,773 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to FINISHED
2025-08-02 12:42:17,773 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - Status of job "Attachment Indexer" is OK
2025-08-02 12:42:17,780 [Thread-36] INFO  com.polarion.core.util.process.JavaRunner - Executing /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/bin/java
  -- args [-jar, /opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar, --server.port=40608, --jwksUrl=http://localhost/polarion/.well-known/jwks.json]
  -- env null
  -- dir /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess9326122932368120369.tmp
2025-08-02 12:42:17,839 [Notification-Worker-6 | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem
2025-08-02 12:42:17,853 [Notification-Worker-6 | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem
2025-08-02 12:42:17,964 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661a459ec9c43_0_661a459ec9c43_0_: finished. Total: 0.196 s, CPU [user: 0.119 s, system: 0.0128 s], Allocated memory: 16.1 MB, resolve: 0.0518 s [94% User (2x)] (4x), Lucene: 0.018 s [100% search (1x)] (1x), svn: 0.0132 s [49% testConnection (1x), 45% getLatestRevision (2x)] (5x)
2025-08-02 12:42:17,976 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-08-02 12:42:17,977 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-08-02 12:42:17,979 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-08-02 12:42:17,981 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-08-02 12:42:18,051 [Activities-Bulk-Publisher] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Activities
2025-08-02 12:42:18,130 [Thread-43] INFO  class com.polarion.alm.server.util.FormulaServerStartup - Formula renderer says: Server started on 127.0.0.1:34568
2025-08-02 12:42:18,302 [Thread-40] INFO  NotificationService - 
2025-08-02 12:42:18,303 [Thread-40] INFO  NotificationService -   .   ____          _            __ _ _
2025-08-02 12:42:18,303 [Thread-40] INFO  NotificationService -  /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
2025-08-02 12:42:18,303 [Thread-40] INFO  NotificationService - ( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
2025-08-02 12:42:18,303 [Thread-40] INFO  NotificationService -  \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
2025-08-02 12:42:18,303 [Thread-40] INFO  NotificationService -   '  |____| .__|_| |_|_| |_\__, | / / / /
2025-08-02 12:42:18,303 [Thread-40] INFO  NotificationService -  =========|_|==============|___/=/_/_/_/
2025-08-02 12:42:18,304 [Thread-40] INFO  NotificationService -  :: Spring Boot ::                (v2.6.6)
2025-08-02 12:42:18,304 [Thread-40] INFO  NotificationService - 
2025-08-02 12:42:18,365 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Starting Application using Java 11.0.27 on zhangwendeMini2.lan with PID 89383 (/opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar started by zhangwentian in /private/var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess9326122932368120369.tmp)
2025-08-02 12:42:18,366 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - No active profile set, falling back to 1 default profile: "default"
2025-08-02 12:42:18,446 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 25, for prototypes: WorkItem; with days range: 180d, took  [ TIME 0.593 s. ] 
2025-08-02 12:42:18,447 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.609 s, CPU [user: 0.00651 s, system: 0.00142 s], Allocated memory: 356.5 kB, transactions: 1
2025-08-02 12:42:18,448 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.206 s [100% RevisionActivityCreator (2x)] (2x), resolve: 0.0665 s [91% User (3x)] (6x), Lucene: 0.0318 s [57% search (1x), 26% add (1x)] (3x), Incremental Baseline: 0.026 s [100% WorkItem (24x)] (24x), svn: 0.0193 s [48% getLatestRevision (3x), 48% testConnection (2x)] (7x), ObjectMaps: 0.0121 s [72% getPrimaryObjectLocation (2x), 18% getPrimaryObjectProperty (1x)] (7x)
2025-08-02 12:42:18,448 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.688 s, CPU [user: 0.133 s, system: 0.0209 s], Allocated memory: 18.8 MB, transactions: 25, svn: 0.536 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0423 s [84% buildBaselineSnapshots (1x)] (26x)
2025-08-02 12:42:18,450 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to FINISHED
2025-08-02 12:42:18,451 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - Status of job "DB History Creator" is OK
2025-08-02 12:42:18,785 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 25, for prototypes: WorkItem; with days range: 180d, took  [ TIME 0.339 s. ] 
2025-08-02 12:42:18,803 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a459ecb045_0_661a459ecb045_0_: finished. Total: 1.03 s, CPU [user: 0.301 s, system: 0.0759 s], Allocated memory: 51.5 MB, svn: 0.622 s [53% getDatedRevision (181x), 30% getDir2 content (25x)] (328x), resolve: 0.317 s [100% Category (117x)] (117x), ObjectMaps: 0.118 s [42% getPrimaryObjectProperty (117x), 34% getPrimaryObjectLocation (117x), 24% getLastPromoted (117x)] (473x)
2025-08-02 12:42:18,806 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-08-02 12:42:18,808 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-08-02 12:42:18,823 [Thread-40] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 40608 (http)
2025-08-02 12:42:18,829 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-40608"]
2025-08-02 12:42:18,829 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-02 12:42:18,829 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-02 12:42:18,834 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-08-02 12:42:18,836 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-08-02 12:42:18,879 [Thread-40] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-02 12:42:18,879 [Thread-40] INFO  NotificationService - [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 484 ms
2025-08-02 12:42:18,896 [Thread-40] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing AtmosphereFramework
2025-08-02 12:42:18,898 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-08-02 12:42:18,900 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-08-02 12:42:19,052 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-40608"]
2025-08-02 12:42:19,078 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using org.atmosphere.cpr.DefaultAnnotationProcessor for processing annotation
2025-08-02 12:42:19,078 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.DefaultAnnotationProcessor - AnnotationProcessor class org.atmosphere.cpr.DefaultAnnotationProcessor$BytecodeBasedAnnotationProcessor being used
2025-08-02 12:42:19,091 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AnnotationHandler - Found Annotation in class com.siemens.polarion.service.notification.NotificationService being scanned: interface org.atmosphere.config.service.ManagedService
2025-08-02 12:42:19,093 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.AtmosphereResourceLifecycleInterceptor
2025-08-02 12:42:19,094 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.client.TrackMessageSizeInterceptor
2025-08-02 12:42:19,095 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.SuspendTrackerInterceptor
2025-08-02 12:42:19,095 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.config.managed.ManagedServiceInterceptor
2025-08-02 12:42:19,099 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class com.siemens.polarion.service.notification.JwtVerificationInterceptor
2025-08-02 12:42:19,103 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.util.ForkJoinPool - Using ForkJoinPool  java.util.concurrent.ForkJoinPool. Set the org.atmosphere.cpr.broadcaster.maxAsyncWriteThreads to -1 to fully use its power.
2025-08-02 12:42:19,106 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler mapped to context-path /notification and Broadcaster Class org.atmosphere.cpr.DefaultBroadcaster
2025-08-02 12:42:19,106 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor [Atmosphere LifeCycle,  Track Message Size Interceptor using |, UUID Tracking Interceptor, @ManagedService Interceptor, @Service Event Listeners, com.siemens.polarion.service.notification.JwtVerificationInterceptor] mapped to AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler
2025-08-02 12:42:19,114 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Auto detecting WebSocketHandler in /WEB-INF/classes/
2025-08-02 12:42:19,116 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed WebSocketProtocol org.atmosphere.websocket.protocol.SimpleHttpProtocol 
2025-08-02 12:42:19,117 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.container.JSR356AsyncSupport - JSR 356 Mapping path /notification
2025-08-02 12:42:19,121 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installing Default AtmosphereInterceptors
2025-08-02 12:42:19,121 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CorsInterceptor : CORS Interceptor Support
2025-08-02 12:42:19,121 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CacheHeadersInterceptor : Default Response's Headers Interceptor
2025-08-02 12:42:19,121 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.PaddingAtmosphereInterceptor : Browser Padding Interceptor Support
2025-08-02 12:42:19,122 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.AndroidAtmosphereInterceptor : Android Interceptor Support
2025-08-02 12:42:19,122 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.HeartbeatInterceptor : Heartbeat Interceptor Support
2025-08-02 12:42:19,122 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.SSEAtmosphereInterceptor : SSE Interceptor Support
2025-08-02 12:42:19,122 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JSONPAtmosphereInterceptor : JSONP Interceptor Support
2025-08-02 12:42:19,123 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JavaScriptProtocol : Atmosphere JavaScript Protocol
2025-08-02 12:42:19,123 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor : org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor
2025-08-02 12:42:19,123 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.OnDisconnectInterceptor : Browser disconnection detection
2025-08-02 12:42:19,123 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.IdleResourceInterceptor : org.atmosphere.interceptor.IdleResourceInterceptor
2025-08-02 12:42:19,123 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Set org.atmosphere.cpr.AtmosphereInterceptor.disableDefaults to disable them.
2025-08-02 12:42:19,123 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor CORS Interceptor Support with priority FIRST_BEFORE_DEFAULT 
2025-08-02 12:42:19,124 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Default Response's Headers Interceptor with priority AFTER_DEFAULT 
2025-08-02 12:42:19,124 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser Padding Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 12:42:19,125 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Android Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 12:42:19,125 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.interceptor.HeartbeatInterceptor - HeartbeatInterceptor configured with padding value 'X', client frequency 30 seconds and server frequency 120 seconds
2025-08-02 12:42:19,125 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Heartbeat Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 12:42:19,125 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor SSE Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 12:42:19,125 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor JSONP Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 12:42:19,125 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Atmosphere JavaScript Protocol with priority AFTER_DEFAULT 
2025-08-02 12:42:19,125 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor with priority AFTER_DEFAULT 
2025-08-02 12:42:19,125 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser disconnection detection with priority AFTER_DEFAULT 
2025-08-02 12:42:19,125 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.IdleResourceInterceptor with priority BEFORE_DEFAULT 
2025-08-02 12:42:19,125 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using EndpointMapper class org.atmosphere.util.DefaultEndpointMapper
2025-08-02 12:42:19,125 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterCache: org.atmosphere.cache.UUIDBroadcasterCache
2025-08-02 12:42:19,125 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Default Broadcaster Class: org.atmosphere.cpr.DefaultBroadcaster
2025-08-02 12:42:19,125 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Shared List Resources: false
2025-08-02 12:42:19,125 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Polling Wait Time 100
2025-08-02 12:42:19,125 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Shared ExecutorService supported: true
2025-08-02 12:42:19,125 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Messaging ExecutorService Pool Size unavailable - Not instance of ThreadPoolExecutor
2025-08-02 12:42:19,125 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Async I/O Thread Pool Size: 200
2025-08-02 12:42:19,125 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterFactory: org.atmosphere.cpr.DefaultBroadcasterFactory
2025-08-02 12:42:19,125 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using AtmosphereResurceFactory: org.atmosphere.cpr.DefaultAtmosphereResourceFactory
2025-08-02 12:42:19,126 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using WebSocketProcessor: org.atmosphere.websocket.DefaultWebSocketProcessor
2025-08-02 12:42:19,127 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Invoke AtmosphereInterceptor on WebSocket message true
2025-08-02 12:42:19,127 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - HttpSession supported: false
2025-08-02 12:42:19,127 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using Spring Web ObjectFactory for dependency injection and object creation
2025-08-02 12:42:19,127 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using async support: org.atmosphere.container.JSR356AsyncSupport running under container: Apache Tomcat/9.0.60 using javax.servlet/3.0 and jsr356/WebSocket API
2025-08-02 12:42:19,128 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere Framework 2.6.4 started.
2025-08-02 12:42:19,128 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 
2025-08-02 12:42:19,128 [Thread-40] INFO  NotificationService - 
2025-08-02 12:42:19,128 [Thread-40] INFO  NotificationService - 	For Atmosphere Framework Commercial Support, visit 
2025-08-02 12:42:19,128 [Thread-40] INFO  NotificationService - 	http://www.async-io.org/ or send an <NAME_EMAIL>
2025-08-02 12:42:19,128 [Thread-40] INFO  NotificationService - 
2025-08-02 12:42:19,131 [Thread-40] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 40608 (http) with context path ''
2025-08-02 12:42:19,135 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Started Application in 1.048 seconds (JVM running for 1.342)
2025-08-02 12:42:19,152 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testtype) created
2025-08-02 12:42:19,157 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (subtype) created
2025-08-02 12:42:19,161 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a45a00444a_0_661a45a00444a_0_: finished. Total: 0.136 s, CPU [user: 0.0496 s, system: 0.00443 s], Allocated memory: 19.7 MB, svn: 0.102 s [67% getDir2 content (17x), 33% getFile content (44x)] (62x), RepositoryConfigService: 0.0528 s [98% getReadConfiguration (170x)] (192x)
2025-08-02 12:42:19,379 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (YesNo) created
2025-08-02 12:42:19,385 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (software_VerificationMethod) created
2025-08-02 12:42:19,389 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checklist) created
2025-08-02 12:42:19,392 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonreqproperty) created
2025-08-02 12:42:19,396 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectoriented) created
2025-08-02 12:42:19,397 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submodule) created
2025-08-02 12:42:19,398 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (yesno) created
2025-08-02 12:42:19,398 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (cICategory) created
2025-08-02 12:42:19,399 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (wpFormat) created
2025-08-02 12:42:19,400 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (trigger) created
2025-08-02 12:42:19,404 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ASILLevel) created
2025-08-02 12:42:19,405 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CSRelated) created
2025-08-02 12:42:19,409 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_Module) created
2025-08-02 12:42:19,420 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (规格对象类型) created
2025-08-02 12:42:19,422 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (jenkins_job) created
2025-08-02 12:42:19,422 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (truefalse) created
2025-08-02 12:42:19,423 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (takeOnGroups) created
2025-08-02 12:42:19,430 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasetype) created
2025-08-02 12:42:19,433 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processGroup) created
2025-08-02 12:42:19,436 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeManagement) created
2025-08-02 12:42:19,442 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (seriousness) created
2025-08-02 12:42:19,447 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softReqClass) created
2025-08-02 12:42:19,451 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWDetailDesign) created
2025-08-02 12:42:19,453 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PhaseChecklists) created
2025-08-02 12:42:19,455 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PassNotpass) created
2025-08-02 12:42:19,457 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineType) created
2025-08-02 12:42:19,460 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (boolYesOrNo) created
2025-08-02 12:42:19,462 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testlevel) created
2025-08-02 12:42:19,464 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (source) created
2025-08-02 12:42:19,468 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectType) created
2025-08-02 12:42:19,470 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atppblversion) created
2025-08-02 12:42:19,472 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (aSIL) created
2025-08-02 12:42:19,476 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (EE) created
2025-08-02 12:42:19,478 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueType) created
2025-08-02 12:42:19,480 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SYS_reqClassification) created
2025-08-02 12:42:19,484 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (oem_2Status) created
2025-08-02 12:42:19,486 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (class) created
2025-08-02 12:42:19,488 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (promotionState) created
2025-08-02 12:42:19,490 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (git_project) created
2025-08-02 12:42:19,495 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (storageType) created
2025-08-02 12:42:19,496 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueproperty) created
2025-08-02 12:42:19,500 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonissueclass) created
2025-08-02 12:42:19,502 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (AgreeDisagree) created
2025-08-02 12:42:19,504 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SA_Category) created
2025-08-02 12:42:19,507 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relevance) created
2025-08-02 12:42:19,510 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (implementationPhase) created
2025-08-02 12:42:19,512 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplier_2Status) created
2025-08-02 12:42:19,517 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Testtype) created
2025-08-02 12:42:19,519 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (conf_baselineTime) created
2025-08-02 12:42:19,532 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (levelneed) created
2025-08-02 12:42:19,534 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (finalresult) created
2025-08-02 12:42:19,536 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testability) created
2025-08-02 12:42:19,538 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solution) created
2025-08-02 12:42:19,540 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Responsible) created
2025-08-02 12:42:19,542 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationstatus) created
2025-08-02 12:42:19,548 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsiassigngroup) created
2025-08-02 12:42:19,550 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqCategory) created
2025-08-02 12:42:19,552 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineName) created
2025-08-02 12:42:19,554 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskType) created
2025-08-02 12:42:19,561 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeReason) created
2025-08-02 12:42:19,563 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectmodule) created
2025-08-02 12:42:19,567 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseOutputMethod) created
2025-08-02 12:42:19,569 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SoftwareFeature) created
2025-08-02 12:42:19,576 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ResponsibleGroup) created
2025-08-02 12:42:19,579 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsifunctionmodule) created
2025-08-02 12:42:19,581 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (FwReqSource) created
2025-08-02 12:42:19,583 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (occurPhase) created
2025-08-02 12:42:19,585 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (compiletask) created
2025-08-02 12:42:19,589 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBVerificationMethod) created
2025-08-02 12:42:19,598 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (functionmodule) created
2025-08-02 12:42:19,601 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (variant) created
2025-08-02 12:42:19,603 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Fusatype) created
2025-08-02 12:42:19,606 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareversion) created
2025-08-02 12:42:19,610 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (appversion) created
2025-08-02 12:42:19,611 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casefirstmodule) created
2025-08-02 12:42:19,614 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditType) created
2025-08-02 12:42:19,621 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Samplestage) created
2025-08-02 12:42:19,623 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casesecondmodule) created
2025-08-02 12:42:19,626 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issue_source) created
2025-08-02 12:42:19,629 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ifNeedRegressionTesting) created
2025-08-02 12:42:19,632 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atpsfsversion) created
2025-08-02 12:42:19,636 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CustomerAllocation) created
2025-08-02 12:42:19,639 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issuesubclass) created
2025-08-02 12:42:19,642 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_importance) created
2025-08-02 12:42:19,644 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reviewMethod) created
2025-08-02 12:42:19,648 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_findType) created
2025-08-02 12:42:19,651 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (editType) created
2025-08-02 12:42:19,656 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testingobjects) created
2025-08-02 12:42:19,658 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcaselevel) created
2025-08-02 12:42:19,662 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplierproblem) created
2025-08-02 12:42:19,664 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqattribute) created
2025-08-02 12:42:19,667 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (fsigroup) created
2025-08-02 12:42:19,669 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_reqsource) created
2025-08-02 12:42:19,672 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (preset) created
2025-08-02 12:42:19,675 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Mechverificationmethod) created
2025-08-02 12:42:19,677 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CPMToTPM) created
2025-08-02 12:42:19,679 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBType) created
2025-08-02 12:42:19,682 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasesign) created
2025-08-02 12:42:19,685 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationphase) created
2025-08-02 12:42:19,691 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processArea) created
2025-08-02 12:42:19,693 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (artifactType) created
2025-08-02 12:42:19,702 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Classification) created
2025-08-02 12:42:19,706 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationmethod) created
2025-08-02 12:42:19,709 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeType) created
2025-08-02 12:42:19,711 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareAndSoftwareSubType) created
2025-08-02 12:42:19,715 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWIntegrationVerificationMethod) created
2025-08-02 12:42:19,718 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (category) created
2025-08-02 12:42:19,734 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBCategory) created
2025-08-02 12:42:19,741 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softreqclass) created
2025-08-02 12:42:19,745 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestMethod) created
2025-08-02 12:42:19,749 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reType) created
2025-08-02 12:42:19,754 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (VerificationCriteria) created
2025-08-02 12:42:19,758 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLinechecklist) created
2025-08-02 12:42:19,763 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Category) created
2025-08-02 12:42:19,768 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWUnitTestDerivingMethods) created
2025-08-02 12:42:19,771 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (firmware_Category) created
2025-08-02 12:42:19,775 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testMethod) created
2025-08-02 12:42:19,787 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QAPorcessAreas) created
2025-08-02 12:42:19,798 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (findSource) created
2025-08-02 12:42:19,808 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a45a02644b_0_661a45a02644b_0_: finished. Total: 0.647 s, CPU [user: 0.33 s, system: 0.0148 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.478 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.322 s [63% getFile content (412x), 37% getDir2 content (21x)] (434x)
2025-08-02 12:42:19,978 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (tshirt-sizes) created
2025-08-02 12:42:19,984 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqtype) created
2025-08-02 12:42:19,987 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a45a0c804c_0_661a45a0c804c_0_: finished. Total: 0.179 s, CPU [user: 0.0447 s, system: 0.00689 s], Allocated memory: 18.1 MB, svn: 0.152 s [78% getDir2 content (18x), 22% getFile content (29x)] (48x), RepositoryConfigService: 0.0538 s [98% getReadConfiguration (124x)] (148x)
2025-08-02 12:42:20,215 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Feasibility) created
2025-08-02 12:42:20,217 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (locaMod) created
2025-08-02 12:42:20,218 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseType) created
2025-08-02 12:42:20,220 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ControlLevel) created
2025-08-02 12:42:20,221 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (NCitemSev) created
2025-08-02 12:42:20,222 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (signType) created
2025-08-02 12:42:20,224 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (WBSCategory) created
2025-08-02 12:42:20,226 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseEnv) created
2025-08-02 12:42:20,227 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verifiability) created
2025-08-02 12:42:20,229 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ProjectUser) created
2025-08-02 12:42:20,231 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (standardReq) created
2025-08-02 12:42:20,232 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (statusa) created
2025-08-02 12:42:20,233 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@WorkItems[type:configurationitemversion]) created
2025-08-02 12:42:20,234 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CIRevisionStatus) created
2025-08-02 12:42:20,236 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (dogTimeout) created
2025-08-02 12:42:20,237 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQCategory) created
2025-08-02 12:42:20,239 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proStage) created
2025-08-02 12:42:20,241 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (BaselineType) created
2025-08-02 12:42:20,242 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (custConfStat) created
2025-08-02 12:42:20,243 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sofReqVer) created
2025-08-02 12:42:20,246 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Source) created
2025-08-02 12:42:20,248 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scCategory) created
2025-08-02 12:42:20,249 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseType) created
2025-08-02 12:42:20,250 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solAdv) created
2025-08-02 12:42:20,251 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseMet) created
2025-08-02 12:42:20,252 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMeth) created
2025-08-02 12:42:20,254 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseMe) created
2025-08-02 12:42:20,255 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseEnv) created
2025-08-02 12:42:20,258 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTarget) created
2025-08-02 12:42:20,260 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ReviewForm) created
2025-08-02 12:42:20,261 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseType) created
2025-08-02 12:42:20,263 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@Collection) created
2025-08-02 12:42:20,263 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submissionStage) created
2025-08-02 12:42:20,265 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMet) created
2025-08-02 12:42:20,266 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandType) created
2025-08-02 12:42:20,267 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseMet) created
2025-08-02 12:42:20,269 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskUrgen) created
2025-08-02 12:42:20,270 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solveMethod) created
2025-08-02 12:42:20,271 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (audMethod) created
2025-08-02 12:42:20,273 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (desStat) created
2025-08-02 12:42:20,275 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scType) created
2025-08-02 12:42:20,277 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseType) created
2025-08-02 12:42:20,278 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (releaseType) created
2025-08-02 12:42:20,279 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseEnv) created
2025-08-02 12:42:20,281 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (targetStage) created
2025-08-02 12:42:20,282 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ClassificationType) created
2025-08-02 12:42:20,283 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testItem) created
2025-08-02 12:42:20,285 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (InfoSecurity) created
2025-08-02 12:42:20,286 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Verification) created
2025-08-02 12:42:20,287 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMod) created
2025-08-02 12:42:20,294 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verMethod) created
2025-08-02 12:42:20,296 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (diagramCategory) created
2025-08-02 12:42:20,297 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (assSubsystem) created
2025-08-02 12:42:20,299 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (OccurrenceProbability) created
2025-08-02 12:42:20,300 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (developmentMethod) created
2025-08-02 12:42:20,301 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (portType) created
2025-08-02 12:42:20,303 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checkType) created
2025-08-02 12:42:20,304 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandStatus) created
2025-08-02 12:42:20,306 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (important) created
2025-08-02 12:42:20,307 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMec) created
2025-08-02 12:42:20,308 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseTy) created
2025-08-02 12:42:20,309 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (recentPre) created
2025-08-02 12:42:20,310 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseDesignMethod) created
2025-08-02 12:42:20,312 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testCasePri) created
2025-08-02 12:42:20,315 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relObj) created
2025-08-02 12:42:20,316 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proSer) created
2025-08-02 12:42:20,318 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestProblemType) created
2025-08-02 12:42:20,320 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (chipName) created
2025-08-02 12:42:20,321 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTiming) created
2025-08-02 12:42:20,323 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a45a10604e_0_661a45a10604e_0_: finished. Total: 0.267 s, CPU [user: 0.105 s, system: 0.00617 s], Allocated memory: 389.5 MB, svn: 0.177 s [61% getDir2 content (21x), 39% getFile content (185x)] (207x), RepositoryConfigService: 0.145 s [96% getReadConfiguration (2787x)] (3025x)
2025-08-02 12:42:20,374 [PreLoadDataService | u:p] ERROR com.polarion.subterra.base.data.model.CustomField - Unknown custom field type 'enum' - using 'string' - for field with id 'taskType' for 'WorkItem task /default/WBS'
2025-08-02 12:42:20,381 [PreLoadDataService] INFO  com.polarion.psvn.launcher.internal.data.PreLoadDataService - Preloading data FINISHED took  [ TIME 2.61 s. ]
2025-08-02 12:42:20,381 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.61 s, CPU [user: 0.968 s, system: 0.127 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.58 s [45% getDir2 content (133x), 31% getFile content (865x), 21% getDatedRevision (181x)] (1224x), RepositoryConfigService: 0.835 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.399 s [80% Category (117x), 14% Project (7x)] (139x), ObjectMaps: 0.156 s [43% getPrimaryObjectProperty (131x), 31% getPrimaryObjectLocation (137x), 25% getLastPromoted (131x)] (536x)
2025-08-02 12:42:20,381 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 61, svn: 2.12 s [41% getDatedRevision (362x), 33% getDir2 content (133x), 23% getFile content (865x)] (1407x), RepositoryConfigService: 0.835 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.399 s [80% Category (117x), 14% Project (7x)] (140x), ObjectMaps: 0.156 s [43% getPrimaryObjectProperty (131x), 31% getPrimaryObjectLocation (137x), 25% getLastPromoted (131x)] (536x)
2025-08-02 12:42:27,786 [Thread-36] INFO  NotificationService - Notification service was started successfully.
2025-08-02 12:42:41,424 [ajp-nio-127.0.0.1-8889-exec-3 | cID:6916d6f6-c0a844bd-2de76568-80c69bbc] WARN  org.springframework.web.context.support.AnnotationConfigWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#simpleHealth()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#health() mapped.
2025-08-02 12:42:41,426 [ajp-nio-127.0.0.1-8889-exec-3 | cID:6916d6f6-c0a844bd-2de76568-80c69bbc] FATAL org.springframework.web.servlet.DispatcherServlet - Context initialization failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#simpleHealth()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#health() mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1794) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:767) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:128) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#simpleHealth()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#health() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:636) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:603) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:318) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:378) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:75) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:288) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:286) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	... 37 more
2025-08-02 12:42:41,430 [ajp-nio-127.0.0.1-8889-exec-3 | cID:6916d6f6-c0a844bd-2de76568-80c69bbc] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/checklist] - Servlet.init() for servlet [dispatcher] threw exception
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#simpleHealth()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#health() mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1794) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) ~[javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:767) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:128) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#simpleHealth()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#health() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:636) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:603) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:318) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:378) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:75) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:288) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:286) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	... 37 more
2025-08-02 12:42:41,431 [ajp-nio-127.0.0.1-8889-exec-3 | cID:6916d6f6-c0a844bd-2de76568-80c69bbc] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/checklist].[dispatcher] - Allocate exception for servlet [dispatcher]
java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#simpleHealth()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#health() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:636) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:603) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:318) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:378) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:75) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:288) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:286) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) ~[javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:767) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:128) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-02 12:42:41,452 [ajp-nio-127.0.0.1-8889-exec-2 | cID:6916d6f6-c0a844bd-2de76568-7b952d3f] WARN  org.springframework.web.context.support.AnnotationConfigWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#simpleHealth()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#health() mapped.
2025-08-02 12:42:41,453 [ajp-nio-127.0.0.1-8889-exec-3 | cID:6916d6f6-c0a844bd-2de76568-80c69bbc] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?_t=1754109761237': Total: 0.178 s, CPU [user: 0.0435 s, system: 0.0304 s], Allocated memory: 2.7 MB, transactions: 0
2025-08-02 12:42:41,453 [ajp-nio-127.0.0.1-8889-exec-2 | cID:6916d6f6-c0a844bd-2de76568-7b952d3f] FATAL org.springframework.web.servlet.DispatcherServlet - Context initialization failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#simpleHealth()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#health() mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1794) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:767) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:128) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#simpleHealth()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#health() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:636) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:603) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:318) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:378) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:75) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:288) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:286) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	... 37 more
2025-08-02 12:42:41,458 [ajp-nio-127.0.0.1-8889-exec-2 | cID:6916d6f6-c0a844bd-2de76568-7b952d3f] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/checklist] - Servlet.init() for servlet [dispatcher] threw exception
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#simpleHealth()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#health() mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1794) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) ~[javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:767) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:128) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#simpleHealth()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#health() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:636) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:603) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:318) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:378) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:75) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:288) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:286) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	... 37 more
2025-08-02 12:42:41,462 [ajp-nio-127.0.0.1-8889-exec-2 | cID:6916d6f6-c0a844bd-2de76568-7b952d3f] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/checklist].[dispatcher] - Allocate exception for servlet [dispatcher]
java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#simpleHealth()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#health() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:636) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:603) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:318) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:378) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:75) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:288) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:286) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) ~[javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:767) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:128) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-02 12:42:41,470 [ajp-nio-127.0.0.1-8889-exec-2 | cID:6916d6f6-c0a844bd-2de76568-7b952d3f] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?_t=1754109761237': Total: 0.197 s, CPU [user: 0.0183 s, system: 0.0039 s], Allocated memory: 1.9 MB, transactions: 0
