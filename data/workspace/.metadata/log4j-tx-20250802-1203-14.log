2025-08-02 12:03:19,090 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0449 s [58% update (144x), 42% query (12x)] (221x), svn: 0.0146 s [43% testConnection (1x), 39% getLatestRevision (2x)] (4x)
2025-08-02 12:03:19,203 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0357 s [62% getDir2 content (2x), 30% info (3x)] (6x)
2025-08-02 12:03:19,961 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.754 s, CPU [user: 0.205 s, system: 0.308 s], Allocated memory: 53.3 MB, transactions: 0, ObjectMaps: 0.163 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-02 12:03:19,961 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.754 s, CPU [user: 0.118 s, system: 0.243 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.101 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-02 12:03:19,961 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.754 s, CPU [user: 0.0875 s, system: 0.176 s], Allocated memory: 14.6 MB, transactions: 0, ObjectMaps: 0.077 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0528 s [76% log2 (10x), 15% getLatestRevision (2x)] (13x)
2025-08-02 12:03:19,961 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.754 s, CPU [user: 0.0531 s, system: 0.0944 s], Allocated memory: 7.1 MB, transactions: 0, ObjectMaps: 0.0542 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-02 12:03:19,961 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.754 s, CPU [user: 0.245 s, system: 0.376 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.134 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-02 12:03:19,961 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.754 s, CPU [user: 0.0812 s, system: 0.107 s], Allocated memory: 10.9 MB, transactions: 0, svn: 0.121 s [54% log2 (10x), 14% info (5x), 14% log (1x)] (24x), ObjectMaps: 0.0648 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-02 12:03:19,962 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.594 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.267 s [63% log2 (36x), 13% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-08-02 12:03:20,223 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.222 s [100% getReadConfiguration (48x)] (48x), svn: 0.0786 s [79% info (18x), 9% getLatestRevision (1x)] (38x)
2025-08-02 12:03:20,509 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.22 s [77% info (94x), 15% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.175 s [100% getReadConfiguration (54x)] (54x)
2025-08-02 12:03:20,742 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.124 s, CPU [user: 0.023 s, system: 0.00617 s], Allocated memory: 8.5 MB
2025-08-02 12:03:20,772 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 27, PersistenceEngineListener: 0.249 s [100% doFinishStartup (1x)] (1x), Lucene: 0.0513 s [100% refresh (1x)] (1x), commit: 0.0417 s [100% Revision (1x)] (1x), DB: 0.0204 s [61% update (3x), 19% query (1x), 14% execute (1x)] (8x), derivedLinkedRevisionsContributor: 0.0137 s [100% objectsToInv (1x)] (1x)
2025-08-02 12:03:23,238 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.418 s [90% info (158x)] (168x)
2025-08-02 12:03:23,560 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661a3cb6fbc42_0_661a3cb6fbc42_0_: finished. Total: 0.313 s, CPU [user: 0.146 s, system: 0.0176 s], Allocated memory: 16.1 MB, resolve: 0.103 s [97% User (2x)] (4x), Lucene: 0.0239 s [100% search (1x)] (1x)
2025-08-02 12:03:24,100 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.76 s, CPU [user: 0.00605 s, system: 0.00155 s], Allocated memory: 356.8 kB, transactions: 1
2025-08-02 12:03:24,101 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 30, notification worker: 0.325 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.121 s [94% User (3x)] (6x), Lucene: 0.0453 s [53% search (1x), 32% add (1x)] (3x), Incremental Baseline: 0.028 s [100% WorkItem (24x)] (24x), svn: 0.022 s [58% getLatestRevision (3x), 36% testConnection (2x)] (7x), ObjectMaps: 0.0176 s [64% getPrimaryObjectLocation (2x), 23% getPrimaryObjectProperty (1x)] (7x)
2025-08-02 12:03:24,101 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.865 s, CPU [user: 0.152 s, system: 0.0245 s], Allocated memory: 19.2 MB, transactions: 26, svn: 0.677 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0433 s [71% buildBaselineSnapshots (1x), 29% buildBaseline (25x)] (26x)
2025-08-02 12:03:24,435 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a3cb6fac40_0_661a3cb6fac40_0_: finished. Total: 1.19 s, CPU [user: 0.341 s, system: 0.0908 s], Allocated memory: 51.6 MB, svn: 0.702 s [44% getDatedRevision (181x), 37% getDir2 content (25x)] (328x), resolve: 0.424 s [100% Category (117x)] (117x), ObjectMaps: 0.167 s [43% getPrimaryObjectProperty (117x), 35% getPrimaryObjectLocation (117x), 22% getLastPromoted (117x)] (473x)
2025-08-02 12:03:24,677 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a3cb83c048_0_661a3cb83c048_0_: finished. Total: 0.149 s, CPU [user: 0.0649 s, system: 0.0107 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0751 s [57% getReadConfiguration (180x), 43% getReadUserConfiguration (10x)] (190x), svn: 0.074 s [52% info (21x), 42% getFile content (16x)] (39x), resolve: 0.0446 s [100% User (9x)] (9x), ObjectMaps: 0.0189 s [54% getPrimaryObjectProperty (8x), 25% getPrimaryObjectLocation (8x), 21% getLastPromoted (8x)] (32x)
2025-08-02 12:03:24,979 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a3cb873c4a_0_661a3cb873c4a_0_: finished. Total: 0.228 s, CPU [user: 0.0865 s, system: 0.00801 s], Allocated memory: 19.8 MB, svn: 0.148 s [60% getDir2 content (17x), 40% getFile content (44x)] (62x), RepositoryConfigService: 0.102 s [98% getReadConfiguration (170x)] (192x)
2025-08-02 12:03:26,594 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a3cb8acc4b_0_661a3cb8acc4b_0_: finished. Total: 1.61 s, CPU [user: 0.539 s, system: 0.0892 s], Allocated memory: 1.1 GB, RepositoryConfigService: 1.04 s [98% getReadConfiguration (8682x)] (9021x), svn: 1.02 s [55% getFile content (412x), 45% getDir2 content (21x)] (434x)
2025-08-02 12:03:26,857 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a3cba4084c_0_661a3cba4084c_0_: finished. Total: 0.262 s, CPU [user: 0.0642 s, system: 0.0105 s], Allocated memory: 17.9 MB, svn: 0.214 s [77% getDir2 content (18x), 23% getFile content (29x)] (48x), RepositoryConfigService: 0.0857 s [93% getReadConfiguration (124x)] (148x)
2025-08-02 12:03:26,984 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a3cba8244d_0_661a3cba8244d_0_: finished. Total: 0.127 s, CPU [user: 0.0259 s, system: 0.00401 s], Allocated memory: 5.3 MB, svn: 0.119 s [93% getDir2 content (11x)] (20x), RepositoryConfigService: 0.0133 s [97% getReadConfiguration (38x)] (54x)
2025-08-02 12:03:27,716 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a3cbaa204e_0_661a3cbaa204e_0_: finished. Total: 0.731 s, CPU [user: 0.261 s, system: 0.0258 s], Allocated memory: 384.3 MB, svn: 0.499 s [51% getFile content (185x), 49% getDir2 content (21x)] (207x), RepositoryConfigService: 0.445 s [97% getReadConfiguration (2787x)] (3025x)
2025-08-02 12:03:27,834 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a3cbb5904f_0_661a3cbb5904f_0_: finished. Total: 0.118 s, CPU [user: 0.0263 s, system: 0.00302 s], Allocated memory: 13.1 MB, svn: 0.102 s [73% getDir2 content (18x), 27% getFile content (33x)] (52x), RepositoryConfigService: 0.0405 s [99% getReadConfiguration (128x)] (150x)
2025-08-02 12:03:27,834 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.59 s, CPU [user: 1.49 s, system: 0.254 s], Allocated memory: 1.6 GB, transactions: 11, svn: 2.94 s [48% getDir2 content (133x), 39% getFile content (865x)] (1224x), RepositoryConfigService: 1.87 s [96% getReadConfiguration (12165x)] (12859x), resolve: 0.525 s [81% Category (117x)] (139x)
2025-08-02 12:03:27,834 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 62, svn: 3.63 s [39% getDir2 content (133x), 32% getFile content (865x), 27% getDatedRevision (362x)] (1408x), RepositoryConfigService: 1.87 s [96% getReadConfiguration (12165x)] (12859x), resolve: 0.525 s [81% Category (117x)] (140x), ObjectMaps: 0.21 s [45% getPrimaryObjectProperty (131x), 33% getPrimaryObjectLocation (137x), 22% getLastPromoted (131x)] (536x)
2025-08-02 12:30:34,215 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.26123046875
2025-08-02 12:30:44,213 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.30078125
2025-08-02 12:30:54,215 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.375537109375
2025-08-02 12:31:04,212 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.226171875
2025-08-02 12:31:14,211 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.123779296875
2025-08-02 12:31:24,215 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.13193359375
2025-08-02 12:31:34,214 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.09814453125
2025-08-02 12:31:44,214 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.086572265625
