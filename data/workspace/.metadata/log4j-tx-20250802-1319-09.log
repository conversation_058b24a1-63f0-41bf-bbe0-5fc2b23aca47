2025-08-02 13:19:15,431 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.087 s [64% update (144x), 36% query (12x)] (221x), svn: 0.023 s [71% getLatestRevision (2x), 18% testConnection (1x)] (4x)
2025-08-02 13:19:15,562 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0441 s [66% getDir2 content (2x), 28% info (3x)] (6x)
2025-08-02 13:19:16,457 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.891 s, CPU [user: 0.137 s, system: 0.19 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.102 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-02 13:19:16,457 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.891 s, CPU [user: 0.255 s, system: 0.269 s], Allocated memory: 53.3 MB, transactions: 0, ObjectMaps: 0.153 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-02 13:19:16,457 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.891 s, CPU [user: 0.0605 s, system: 0.0649 s], Allocated memory: 7.1 MB, transactions: 0, svn: 0.0562 s [69% log2 (5x), 22% getLatestRevision (1x)] (7x), ObjectMaps: 0.0534 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-02 13:19:16,457 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.891 s, CPU [user: 0.108 s, system: 0.133 s], Allocated memory: 14.2 MB, transactions: 0, svn: 0.0949 s [80% log2 (10x)] (13x), ObjectMaps: 0.0827 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-02 13:19:16,457 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.891 s, CPU [user: 0.078 s, system: 0.0723 s], Allocated memory: 8.6 MB, transactions: 0, svn: 0.141 s [81% log2 (10x)] (13x), ObjectMaps: 0.0471 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-02 13:19:16,457 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.891 s, CPU [user: 0.336 s, system: 0.348 s], Allocated memory: 71.1 MB, transactions: 0, ObjectMaps: 0.162 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.141 s [28% log2 (5x), 24% info (5x), 21% getLatestRevision (2x), 18% log (1x)] (18x)
2025-08-02 13:19:16,458 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.599 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.493 s [62% log2 (36x), 18% getLatestRevision (9x), 7% testConnection (6x)] (61x)
2025-08-02 13:19:16,611 [main | u:p] INFO  TXLOGGER - Tx 661a4e1583801_0_661a4e1583801_0_: finished. Total: 0.115 s, CPU [user: 0.0908 s, system: 0.00454 s], Allocated memory: 21.8 MB
2025-08-02 13:19:16,833 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.315 s [100% getReadConfiguration (48x)] (48x), svn: 0.123 s [83% info (18x)] (38x)
2025-08-02 13:19:17,411 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.424 s [77% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.328 s [100% getReadConfiguration (54x)] (54x)
2025-08-02 13:19:17,690 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.125 s, CPU [user: 0.0386 s, system: 0.0126 s], Allocated memory: 9.5 MB
2025-08-02 13:19:17,730 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.296 s [100% doFinishStartup (1x)] (1x), commit: 0.0689 s [100% Revision (1x)] (1x), Lucene: 0.0346 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0309 s [100% objectsToInv (1x)] (1x)
2025-08-02 13:19:21,978 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.784 s [90% info (158x)] (170x)
2025-08-02 13:19:22,442 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661a4e1ae0c42_0_661a4e1ae0c42_0_: finished. Total: 0.454 s, CPU [user: 0.204 s, system: 0.0209 s], Allocated memory: 16.1 MB, resolve: 0.128 s [97% User (2x)] (4x), Lucene: 0.0272 s [100% search (1x)] (1x)
2025-08-02 13:19:23,304 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.14 s, CPU [user: 0.00851 s, system: 0.00173 s], Allocated memory: 356.3 kB, transactions: 1
2025-08-02 13:19:23,311 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, notification worker: 0.468 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.168 s [95% User (3x)] (6x), Lucene: 0.0586 s [46% search (1x), 41% add (1x)] (3x), Incremental Baseline: 0.0448 s [100% WorkItem (24x)] (24x), persistence listener: 0.0282 s [82% indexRefreshPersistenceListener (1x)] (7x)
2025-08-02 13:19:23,313 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.33 s, CPU [user: 0.227 s, system: 0.0364 s], Allocated memory: 18.8 MB, transactions: 25, svn: 1.03 s [98% getDatedRevision (181x)] (183x)
2025-08-02 13:19:24,519 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a4e1ae2445_0_661a4e1ae2445_0_: finished. Total: 2.53 s, CPU [user: 0.619 s, system: 0.146 s], Allocated memory: 51.6 MB, svn: 1.81 s [64% getDatedRevision (181x), 24% getDir2 content (25x)] (328x), resolve: 0.653 s [100% Category (117x)] (117x), ObjectMaps: 0.235 s [45% getPrimaryObjectProperty (117x), 34% getPrimaryObjectLocation (117x), 21% getLastPromoted (117x)] (473x)
2025-08-02 13:19:24,897 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a4e1d7ec48_0_661a4e1d7ec48_0_: finished. Total: 0.228 s, CPU [user: 0.0984 s, system: 0.0166 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.112 s [53% getReadConfiguration (180x), 47% getReadUserConfiguration (10x)] (190x), svn: 0.11 s [59% info (21x), 36% getFile content (16x)] (39x), resolve: 0.065 s [100% User (9x)] (9x), ObjectMaps: 0.0239 s [58% getPrimaryObjectProperty (8x), 23% getPrimaryObjectLocation (8x)] (32x), GlobalHandler: 0.0139 s [73% applyTxChanges (2x), 27% get (27x)] (29x)
2025-08-02 13:19:25,319 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a4e1dcc04a_0_661a4e1dcc04a_0_: finished. Total: 0.342 s, CPU [user: 0.107 s, system: 0.0115 s], Allocated memory: 19.8 MB, svn: 0.279 s [74% getDir2 content (17x), 26% getFile content (44x)] (62x), RepositoryConfigService: 0.11 s [98% getReadConfiguration (170x)] (192x)
2025-08-02 13:19:26,483 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a4e1e21c4b_0_661a4e1e21c4b_0_: finished. Total: 1.16 s, CPU [user: 0.521 s, system: 0.0331 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.865 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.595 s [71% getFile content (412x), 29% getDir2 content (21x)] (434x), GC: 0.07 s [100% G1 Young Generation (4x)] (4x)
2025-08-02 13:19:26,602 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a4e1f44c4c_0_661a4e1f44c4c_0_: finished. Total: 0.118 s, CPU [user: 0.0259 s, system: 0.00268 s], Allocated memory: 17.9 MB, svn: 0.105 s [85% getDir2 content (18x)] (48x), RepositoryConfigService: 0.025 s [97% getReadConfiguration (124x)] (148x)
2025-08-02 13:19:27,202 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a4e1f75c4e_0_661a4e1f75c4e_0_: finished. Total: 0.523 s, CPU [user: 0.209 s, system: 0.0183 s], Allocated memory: 384.3 MB, svn: 0.343 s [56% getFile content (185x), 44% getDir2 content (21x)] (207x), RepositoryConfigService: 0.341 s [97% getReadConfiguration (2787x)] (3025x)
2025-08-02 13:19:27,347 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a4e1ff884f_0_661a4e1ff884f_0_: finished. Total: 0.145 s, CPU [user: 0.0332 s, system: 0.00354 s], Allocated memory: 13.1 MB, svn: 0.126 s [81% getDir2 content (18x)] (52x), RepositoryConfigService: 0.0384 s [98% getReadConfiguration (128x)] (150x)
2025-08-02 13:19:27,347 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 5.36 s, CPU [user: 1.75 s, system: 0.25 s], Allocated memory: 1.6 GB, transactions: 11, svn: 3.53 s [35% getDir2 content (133x), 33% getDatedRevision (181x), 29% getFile content (865x)] (1224x), RepositoryConfigService: 1.6 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.804 s [81% Category (117x)] (139x), ObjectMaps: 0.292 s [49% getPrimaryObjectProperty (131x), 31% getPrimaryObjectLocation (137x)] (536x)
2025-08-02 13:19:27,347 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 61, svn: 4.56 s [47% getDatedRevision (362x), 27% getDir2 content (133x), 22% getFile content (865x)] (1409x), RepositoryConfigService: 1.6 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.804 s [81% Category (117x)] (140x), ObjectMaps: 0.292 s [49% getPrimaryObjectProperty (131x), 31% getPrimaryObjectLocation (137x)] (536x)
2025-08-02 13:21:06,934 [ajp-nio-127.0.0.1-8889-exec-3 | cID:6939eec6-c0a844bd-2e1c5321-2682230b] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?_t=1754112061051': Total: 5.81 s, CPU [user: 0.0429 s, system: 0.0307 s], Allocated memory: 512.7 kB, transactions: 0
2025-08-02 13:21:08,800 [ajp-nio-127.0.0.1-8889-exec-2 | cID:6939eec5-c0a844bd-2e1c5321-3faf0e8b] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?_t=1754112061051': Total: 7.67 s, CPU [user: 0.0214 s, system: 0.0303 s], Allocated memory: 641.8 kB, transactions: 0
