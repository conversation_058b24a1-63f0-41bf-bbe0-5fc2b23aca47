2025-08-02 12:38:07,089 [Catalina-utility-4] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-08-02 12:38:07,652 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-08-02 12:38:07,654 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[90]')
2025-08-02 12:38:07,654 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[90]')
2025-08-02 12:38:07,656 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[96]')
2025-08-02 12:38:07,660 [Catalina-utility-4] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-08-02 12:38:07,660 [Catalina-utility-4] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[90]')
2025-08-02 12:38:07,660 [Catalina-utility-4] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[92]')
2025-08-02 12:38:08,660 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-08-02 12:38:08,767 [ajp-nio-127.0.0.1-8889-exec-1 | cID:6912ae6c-c0a844bd-09cb9484-f233186f] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-08-02 12:38:08,785 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-08-02 12:38:08,786 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
