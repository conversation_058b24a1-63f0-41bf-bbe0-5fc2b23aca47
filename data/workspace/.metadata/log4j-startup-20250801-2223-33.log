2025-08-01 22:23:33,240 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-01 22:23:33,240 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-01 22:23:33,240 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-01 22:23:33,240 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-01 22:23:33,240 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-01 22:23:33,240 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 22:23:33,240 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-01 22:23:37,332 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-01 22:23:37,460 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.127 s. ]
2025-08-01 22:23:37,460 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-01 22:23:37,501 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0416 s. ]
2025-08-01 22:23:37,555 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-01 22:23:37,688 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 16 s. ]
2025-08-01 22:23:37,924 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.69 s. ]
2025-08-01 22:23:38,005 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 22:23:38,005 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-01 22:23:38,026 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-08-01 22:23:38,027 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 22:23:38,027 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-01 22:23:38,031 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-08-01 22:23:38,031 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-08-01 22:23:38,031 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-08-01 22:23:38,031 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-08-01 22:23:38,031 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-08-01 22:23:38,032 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-01 22:23:38,039 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-01 22:23:38,183 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-01 22:23:38,284 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-01 22:23:38,769 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.74 s. ]
2025-08-01 22:23:38,780 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 22:23:38,780 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-01 22:23:39,007 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-01 22:23:39,020 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.25 s. ]
2025-08-01 22:23:39,049 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 22:23:39,049 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-01 22:23:39,053 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-01 22:23:39,094 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-01 22:23:39,136 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-01 22:23:39,171 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-01 22:23:39,191 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-01 22:23:39,211 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-01 22:23:39,234 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-01 22:23:39,260 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-01 22:23:39,284 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-01 22:23:39,284 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.26 s. ]
2025-08-01 22:23:39,284 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 22:23:39,284 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-01 22:23:39,299 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-01 22:23:39,299 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 22:23:39,299 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-01 22:23:39,403 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-01 22:23:39,406 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-01 22:23:39,523 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.22 s. ]
2025-08-01 22:23:39,524 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 22:23:39,524 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-01 22:23:39,533 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-01 22:23:39,533 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 22:23:39,533 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-01 22:23:42,268 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.74 s. ]
2025-08-01 22:23:42,269 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-01 22:23:42,269 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.03 s. ]
2025-08-01 22:23:42,269 [main] INFO  com.polarion.platform.startup - ****************************************************************
