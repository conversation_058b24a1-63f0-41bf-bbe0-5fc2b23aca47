2025-08-01 22:23:38,894 [main | u:p | u:p] ERROR com.polarion.platform.repository.internal.config.RepositoryConfigService$ConfigProblemCatcher - Failed to work with configuration from location /WBS/.polarion/repositories/repositories.xml:
[/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
com.polarion.platform.repository.config.RepositoryConfigurationException: [/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:87) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocations(AbstractDataHandler.java:61) ~[platform-repository.jar:?]
	at $IDataHandler_19866044c57.readLocations($IDataHandler_19866044c57.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readLocations(RepositoryConfigService.java:291) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$3.runImpl(RepositoryConfigService.java:328) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction$1.runWEx(RepositoryConfigService.java:113) ~[platform-repository.jar:?]
	at com.polarion.core.util.RunnableWEx.runWRet(RunnableWEx.java:61) ~[util.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction.run(RepositoryConfigService.java:123) ~[platform-repository.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:361) ~[?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:58) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:422) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:412) ~[platform.jar:?]
	at $ISecurityService_19866044a72.doAsSystemUser($ISecurityService_19866044a72.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readConfiguration(RepositoryConfigService.java:324) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getConfigurationImpl(RepositoryConfigService.java:239) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfigurationImpl(RepositoryConfigService.java:199) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:177) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.addConfigurationListener(RepositoryConfigService.java:263) ~[platform-repository.jar:?]
	at $IRepositoryConfigService_19866044a80.addConfigurationListener($IRepositoryConfigService_19866044a80.java) ~[?:?]
	at com.polarion.platform.repository.external.internal.ExternalRepositoryProviderRegistry.initialize(ExternalRepositoryProviderRegistry.java:146) ~[platform-repository.jar:?]
	at $IExternalRepositoryProviderRegistry_19866044b3c.initialize($IExternalRepositoryProviderRegistry_19866044b3c.java) ~[?:?]
	at $IExternalRepositoryProviderRegistry_19866044b3b.initialize($IExternalRepositoryProviderRegistry_19866044b3b.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule.initModule(RevisionsPersistenceModule.java:353) ~[platform-persistence.jar:?]
	at $IObjectPersistenceModule_19866044c2b.initModule($IObjectPersistenceModule_19866044c2b.java) ~[?:?]
	at com.polarion.subterra.persistence.internal.PersistenceEngine.initModule(PersistenceEngine.java:251) ~[subterra-uniform-persistence.jar:?]
	at $IPersistenceEngine_19866044c13.initModule($IPersistenceEngine_19866044c13.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.LowLevelDataService.boot(LowLevelDataService.java:352) ~[platform-persistence.jar:?]
	at $ILowLevelPersistence_19866044b38.boot($ILowLevelPersistence_19866044b38.java) ~[?:?]
	at $ILowLevelPersistence_19866044b37.boot($ILowLevelPersistence_19866044b37.java) ~[?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.lambda$0(PlatformService.java:294) ~[launcher.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:423) [?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:69) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:417) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:407) [platform.jar:?]
	at $ISecurityService_19866044a73.doAsSystemUser($ISecurityService_19866044a73.java) [?:?]
	at $ISecurityService_19866044a72.doAsSystemUser($ISecurityService_19866044a72.java) [?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.bootPlatform(PlatformService.java:286) [launcher.jar:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.start(PlatformService.java:92) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.runImpl(PolarionSVNApplication.java:139) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.run(PolarionSVNApplication.java:94) [launcher.jar:?]
	at com.polarion.core.boot.launchers.BasicAppLauncher.launch(BasicAppLauncher.java:53) [boot.jar:?]
	at com.polarion.core.boot.impl.AppLaunchersManager.start(AppLaunchersManager.java:184) [boot.jar:?]
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196) [org.eclipse.equinox.app_1.3.500.v20171221-2204.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:388) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:243) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:656) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:592) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.run(Main.java:1498) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.main(Main.java:1471) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
Caused by: com.thoughtworks.xstream.converters.ConversionException: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:77) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
Caused by: com.thoughtworks.xstream.mapper.CannotResolveClassException: AutoBranchGitLab
	at com.thoughtworks.xstream.mapper.DefaultMapper.realClass(DefaultMapper.java:81) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.DynamicProxyMapper.realClass(DynamicProxyMapper.java:55) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.PackageAliasingMapper.realClass(PackageAliasingMapper.java:88) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ClassAliasingMapper.realClass(ClassAliasingMapper.java:79) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ArrayMapper.realClass(ArrayMapper.java:74) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.SecurityMapper.realClass(SecurityMapper.java:71) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.CachingMapper.realClass(CachingMapper.java:47) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.util.HierarchicalStreams.readClassType(HierarchicalStreams.java:29) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readBareItem(AbstractCollectionConverter.java:131) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readItem(AbstractCollectionConverter.java:117) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readCompleteItem(AbstractCollectionConverter.java:147) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.ArrayConverter.unmarshal(ArrayConverter.java:54) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:72) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
2025-08-01 22:23:40,029 [Catalina-utility-5] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/oauth-feishu] - For security constraints with URL pattern [/userinfo] only the HTTP methods [POST GET] are covered. All other methods are uncovered.
2025-08-01 22:23:46,969 [PreLoadDataService | u:p] ERROR com.polarion.subterra.base.data.model.CustomField - Unknown custom field type 'enum' - using 'string' - for field with id 'taskType' for 'WorkItem task /default/WBS'
2025-08-01 22:23:47,210 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: com.polarion.space.read; ignoring.
2025-08-01 22:23:47,211 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,212 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,213 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,213 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,213 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,213 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,213 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,213 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,213 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,213 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,213 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,213 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,213 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,213 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,213 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,214 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,214 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,214 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,214 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,214 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,214 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,214 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,214 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,214 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,214 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,214 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,214 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,214 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,214 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,214 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,214 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,214 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,214 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,215 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,215 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,215 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,215 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,215 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,215 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,215 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,215 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:47,215 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66047a76-c0a844bd-2596a640-870c449d | u:admin | i:elibrary/Specification/Administration Specification] ERROR com.polarion.platform.internal.security.ACLParser - Unknown permission name: persistence.object.ModuleComment.key.resolved.modify; ignoring.
2025-08-01 22:23:50,562 [ajp-nio-127.0.0.1-8889-exec-15 | cID:66048b1e-c0a844bd-2596a640-f1d54f2c] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/livechecklist] - Servlet.init() for servlet [config] threw exception
java.lang.NoClassDefFoundError: com/teamlive/utils/GuiceService
	at com.teamlive.livechecklist.servlet.AbstractServlet.init(AbstractServlet.java:45) ~[com.teamlive.livechecklist_1.0.0.202304091555/:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:767) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:128) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:659) [catalina.jar:9.0.53]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invokeInternal(PolarionAuthenticator.java:248) [platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invoke(PolarionAuthenticator.java:242) [platform.jar:?]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:312) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: java.lang.ClassNotFoundException: com.teamlive.utils.GuiceService cannot be found by com.teamlive.livechecklist_1.0.0.202304091555
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:508) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at java.lang.ClassLoader.loadClass(ClassLoader.java:527) ~[?:?]
	... 24 more
2025-08-01 22:23:50,563 [ajp-nio-127.0.0.1-8889-exec-15 | cID:66048b1e-c0a844bd-2596a640-f1d54f2c] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/livechecklist].[config] - Allocate exception for servlet [config]
java.lang.ClassNotFoundException: com.teamlive.utils.GuiceService cannot be found by com.teamlive.livechecklist_1.0.0.202304091555
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:508) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at java.lang.ClassLoader.loadClass(ClassLoader.java:527) ~[?:?]
	at com.teamlive.livechecklist.servlet.AbstractServlet.init(AbstractServlet.java:45) ~[com.teamlive.livechecklist_1.0.0.202304091555/:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:767) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:128) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:659) [catalina.jar:9.0.53]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invokeInternal(PolarionAuthenticator.java:248) [platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invoke(PolarionAuthenticator.java:242) [platform.jar:?]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:312) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-01 22:23:50,584 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66048b36-c0a844bd-2596a640-89dc5685] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/livechecklist] - Servlet.init() for servlet [config] threw exception
java.lang.NoClassDefFoundError: com/teamlive/utils/GuiceService
	at com.teamlive.livechecklist.servlet.AbstractServlet.init(AbstractServlet.java:45) ~[com.teamlive.livechecklist_1.0.0.202304091555/:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:767) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:128) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:659) [catalina.jar:9.0.53]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invokeInternal(PolarionAuthenticator.java:248) [platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invoke(PolarionAuthenticator.java:242) [platform.jar:?]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:312) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-01 22:23:50,584 [ajp-nio-127.0.0.1-8889-exec-10 | cID:66048b36-c0a844bd-2596a640-89dc5685] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/livechecklist].[config] - Allocate exception for servlet [config]
java.lang.NoClassDefFoundError: com/teamlive/utils/GuiceService
	at com.teamlive.livechecklist.servlet.AbstractServlet.init(AbstractServlet.java:45) ~[com.teamlive.livechecklist_1.0.0.202304091555/:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:767) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:128) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:659) [catalina.jar:9.0.53]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invokeInternal(PolarionAuthenticator.java:248) [platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invoke(PolarionAuthenticator.java:242) [platform.jar:?]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:312) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
