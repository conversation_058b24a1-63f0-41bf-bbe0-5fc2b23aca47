2025-08-02 12:37:59,828 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 12:37:59,828 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-02 12:37:59,828 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 12:37:59,828 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-02 12:37:59,829 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-02 12:37:59,829 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:37:59,829 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-02 12:38:03,979 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-02 12:38:04,109 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.13 s. ]
2025-08-02 12:38:04,109 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-02 12:38:04,156 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0462 s. ]
2025-08-02 12:38:04,209 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-02 12:38:04,334 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 15 s. ]
2025-08-02 12:38:04,580 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.76 s. ]
2025-08-02 12:38:04,683 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:38:04,683 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-02 12:38:04,711 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-08-02 12:38:04,711 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:38:04,711 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-02 12:38:04,716 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-08-02 12:38:04,716 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-08-02 12:38:04,716 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-08-02 12:38:04,716 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-08-02 12:38:04,716 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-08-02 12:38:04,716 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-08-02 12:38:04,728 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-02 12:38:04,881 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-02 12:38:04,994 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-02 12:38:05,456 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.75 s. ]
2025-08-02 12:38:05,467 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:38:05,467 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-02 12:38:05,659 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-02 12:38:05,670 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.21 s. ]
2025-08-02 12:38:05,697 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:38:05,697 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-02 12:38:05,700 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-02 12:38:05,745 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-02 12:38:05,790 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-02 12:38:05,825 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-02 12:38:05,848 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-02 12:38:05,872 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-02 12:38:05,906 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-02 12:38:05,945 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-02 12:38:05,977 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-02 12:38:05,977 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.31 s. ]
2025-08-02 12:38:05,977 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:38:05,977 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-02 12:38:05,991 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-02 12:38:05,991 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:38:05,991 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-02 12:38:06,081 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-02 12:38:06,083 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-02 12:38:06,192 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.2 s. ]
2025-08-02 12:38:06,193 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:38:06,193 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-02 12:38:06,199 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-02 12:38:06,199 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:38:06,199 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-02 12:38:08,825 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.63 s. ]
2025-08-02 12:38:08,825 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 12:38:08,825 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9 s. ]
2025-08-02 12:38:08,825 [main] INFO  com.polarion.platform.startup - ****************************************************************
