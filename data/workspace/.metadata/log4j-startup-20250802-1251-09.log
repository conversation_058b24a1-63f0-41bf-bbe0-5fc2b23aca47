2025-08-02 12:51:09,177 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 12:51:09,178 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-02 12:51:09,178 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 12:51:09,178 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-02 12:51:09,178 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-02 12:51:09,178 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:51:09,178 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-02 12:51:13,377 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-02 12:51:13,533 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.156 s. ]
2025-08-02 12:51:13,533 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-02 12:51:13,593 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0597 s. ]
2025-08-02 12:51:13,641 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-02 12:51:13,772 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 15 s. ]
2025-08-02 12:51:14,068 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.9 s. ]
2025-08-02 12:51:14,161 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:51:14,161 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-02 12:51:14,186 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-08-02 12:51:14,186 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:51:14,186 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-02 12:51:14,191 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-08-02 12:51:14,191 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-08-02 12:51:14,191 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-08-02 12:51:14,191 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-08-02 12:51:14,191 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-08-02 12:51:14,191 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-08-02 12:51:14,197 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-02 12:51:14,330 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-02 12:51:14,468 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-02 12:51:14,950 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.76 s. ]
2025-08-02 12:51:14,961 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:51:14,961 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-02 12:51:15,158 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-02 12:51:15,171 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-08-02 12:51:15,196 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:51:15,196 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-02 12:51:15,200 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-02 12:51:15,252 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-02 12:51:15,303 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-02 12:51:15,344 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-02 12:51:15,366 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-02 12:51:15,389 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-02 12:51:15,427 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-02 12:51:15,477 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-02 12:51:15,527 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-02 12:51:15,527 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.36 s. ]
2025-08-02 12:51:15,527 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:51:15,527 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-02 12:51:15,541 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-02 12:51:15,541 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:51:15,541 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-02 12:51:15,647 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-02 12:51:15,649 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-02 12:51:15,770 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-08-02 12:51:15,772 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:51:15,772 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-02 12:51:15,785 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-02 12:51:15,785 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:51:15,786 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-02 12:51:18,850 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.06 s. ]
2025-08-02 12:51:18,851 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 12:51:18,851 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.67 s. ]
2025-08-02 12:51:18,851 [main] INFO  com.polarion.platform.startup - ****************************************************************
