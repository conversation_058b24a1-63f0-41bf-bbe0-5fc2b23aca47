2025-08-02 13:36:23,031 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:36:23,031 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-02 13:36:23,031 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:36:23,031 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-02 13:36:23,031 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-02 13:36:23,031 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:36:23,032 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-02 13:36:27,305 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-02 13:36:27,461 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.155 s. ]
2025-08-02 13:36:27,461 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-02 13:36:27,525 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0646 s. ]
2025-08-02 13:36:27,573 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-02 13:36:27,679 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 18 s. ]
2025-08-02 13:36:27,900 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.87 s. ]
2025-08-02 13:36:27,982 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:36:27,982 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-02 13:36:28,010 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-02 13:36:28,011 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:36:28,011 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-02 13:36:28,016 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-08-02 13:36:28,016 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-08-02 13:36:28,016 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-08-02 13:36:28,016 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-08-02 13:36:28,016 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (5/9)
2025-08-02 13:36:28,016 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-02 13:36:28,024 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-02 13:36:28,136 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-02 13:36:28,238 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-02 13:36:28,711 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.7 s. ]
2025-08-02 13:36:28,723 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:36:28,723 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-02 13:36:28,922 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-02 13:36:28,936 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-08-02 13:36:28,963 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:36:28,963 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-02 13:36:28,967 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-02 13:36:29,017 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-02 13:36:29,057 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-02 13:36:29,092 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-02 13:36:29,116 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-02 13:36:29,140 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-02 13:36:29,174 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-02 13:36:29,213 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-02 13:36:29,244 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-02 13:36:29,244 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.31 s. ]
2025-08-02 13:36:29,244 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:36:29,244 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-02 13:36:29,258 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-02 13:36:29,258 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:36:29,258 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-02 13:36:29,352 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-02 13:36:29,354 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-02 13:36:29,450 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.19 s. ]
2025-08-02 13:36:29,451 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:36:29,451 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-02 13:36:29,457 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-02 13:36:29,457 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:36:29,457 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-02 13:36:31,765 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.31 s. ]
2025-08-02 13:36:31,765 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:36:31,765 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.73 s. ]
2025-08-02 13:36:31,765 [main] INFO  com.polarion.platform.startup - ****************************************************************
