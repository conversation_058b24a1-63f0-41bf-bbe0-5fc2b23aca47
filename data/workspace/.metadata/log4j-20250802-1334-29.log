2025-08-02 13:34:29,609 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using logging context STANDALONE
2025-08-02 13:34:29,611 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Launchers manager started...
2025-08-02 13:34:29,611 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using home directory /opt/polarion/polarion
2025-08-02 13:34:29,611 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using root directory /opt/polarion
2025-08-02 13:34:29,611 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using workspace directory /opt/polarion/data/workspace
2025-08-02 13:34:29,611 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using config directory /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-08-02 13:34:29,611 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading external properties ...
2025-08-02 13:34:29,611 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using external property file /opt/polarion/etc/polarion.properties
2025-08-02 13:34:29,611 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading internal properties ...
2025-08-02 13:34:29,613 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Host: zhangwentiandeMac-mini-2.local (127.0.0.1)
2025-08-02 13:34:29,616 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Product: com.polarion.alm
2025-08-02 13:34:29,616 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Version: 3.22.1
2025-08-02 13:34:29,616 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Build: 20220419-1528-22_R1-be3adceb
2025-08-02 13:34:29,617 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/fasnote
2025-08-02 13:34:29,617 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/2404
2025-08-02 13:34:29,617 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Extensions: [exts]
2025-08-02 13:34:29,620 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace location: /opt/polarion/data/workspace
2025-08-02 13:34:29,620 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace lock acquired
2025-08-02 13:34:29,620 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Found applications: [polarion.server, polarion.coordinator, polarion.rt]
2025-08-02 13:34:29,620 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Starting application: polarion.server
2025-08-02 13:34:29,625 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Application extension successfully read
2025-08-02 13:34:29,630 [main] INFO  com.polarion.platform.internal.SystemStatistics - Initializing monitoring, isThreadCpuTimeSupported: true, isThreadContentionMonitoringSupported: true, isThreadAllocatedMemorySupported: true
2025-08-02 13:34:29,630 [main] INFO  com.polarion.platform.internal.SystemStatistics - State before enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-08-02 13:34:29,630 [main] INFO  com.polarion.platform.internal.SystemStatistics - State after enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-08-02 13:34:29,634 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:34:29,634 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-02 13:34:29,634 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:34:29,634 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-02 13:34:29,634 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-02 13:34:29,634 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:34:29,634 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-02 13:34:29,635 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** Java system properties listing: 
2025-08-02 13:34:29,654 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminPasswd = admin
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminUser = admin
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.auth = false
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.host = 
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.password = **PASSWORD**HIDDEN**
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.port = 25
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.user = 
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - awt.toolkit = sun.lwawt.macosx.LWCToolkit
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - base.url = http://localhost
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - bfh.jobs.workdir = /opt/polarion/data/workspace/polarion-data/jobs
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - BIRDir = /opt/polarion/data/BIR
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - calculated.fields.mode = async
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.activationHelpLink = https://polarion.plm.automation.siemens.com/getlicense
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.server = https://license.polarion.com/licenseGenerator/generator/generate
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.enabled = false
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.url = http://www.gravatar.com/avatar/$emailHash$?d=identicon&s=50
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.application = polarion.server
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.config = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.data = /opt/polarion/data
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.eclipse = /opt/polarion/polarion
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.home = /opt/polarion/polarion
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.licenseDir = /opt/polarion/polarion/license
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.internalPG = polarion:polarion@localhost:5434
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.disabled = true
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.receivers = 
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.sender = 
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.subject.prefix = 
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.persistence.notifications.disabled = true
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.propertyFile = /opt/polarion/etc/polarion.properties
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.root = /opt/polarion
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.workspace = /opt/polarion/data/workspace
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.collaborationNotifications.enabled = true
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.document.listStyle = 1ai
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.loggingContext = STANDALONE
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.preview.thumbnailsDataDir = /opt/polarion/data/workspace/previews-data/thumbnails
2025-08-02 13:34:29,655 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.cors.allowedOrigins = *
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.enabled = true
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.swaggerUi.enabled = true
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.accEndpointUrl = https://acc.collab.sws.siemens.com
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.baseDomain = sws.siemens.com
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.shareEndpointUrl = https://share.sws.siemens.com
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - content.types.user.table = /opt/polarion/polarion/plugins/com.polarion.core.boot_3.22.1/content-types.properties
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlHostname = localhost
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlPort = 8887
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.location = Sandbox/
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.useUserId = true
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.limitedAccessMessage = You may create a project in the Sandbox project group (only). Please fill in the required properties below. For example:<br/><table><tr><td>Location:</td><td>Sandbox/MyFirstProject</td></tr><tr><td>ID:</td><td>MyFirstProject</td></tr></table><br/>Or use the suggested defaults.
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug = false
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.license.validation = true
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.machine.code.generation = true
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.security.validation = true
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.ALM = alm_vmodel
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Pro = alm_vmodel
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.QA = qa_vmodel
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Requirements = req_vmodel
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XBase = alm_vmodel
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XEnterprise = alm_vmodel
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XPro = alm_vmodel
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - derby.system.home = /opt/polarion/data/logs/derby
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.application = com.polarion.core.boot.app
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.commands = -application
com.polarion.core.boot.app
-data
/opt/polarion/data/workspace
-configuration
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
-dev
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
-os
linux
-ws
linux
-arch
arm64
-appId
polarion.server

2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.home.location = file:/opt/polarion/polarion/plugins/
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.p2.data.area = @config.dir/.p2
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.pde.launch = true
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.startTime = *************
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.stateSaveDelayInterval = 30000
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - enableCreateAccountForm = false
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - equinox.init.uuid = true
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - error.report.email = 
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.encoding = UTF-8
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.separator = /
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ftp.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gopherProxySet = false
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gosh.args = --nointeractive
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - htpasswd.path = htpasswd
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - http.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - index.activities = /opt/polarion/data/workspace/polarion-data/index
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.graphicsenv = sun.awt.CGraphicsEnvironment
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.headless = true
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.printerjob = sun.lwawt.macosx.CPrinterJob
2025-08-02 13:34:29,656 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.path = /opt/polarion/polarion/plugins/org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.version = 55.0
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.home = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.io.tmpdir = /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.library.path = /Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.name = OpenJDK Runtime Environment
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.version = 11.0.27+6-LTS
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.security.policy = /opt/polarion/polarion/policy
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.maintenance.version = 3
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.name = Java Platform API Specification
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.vendor = Oracle Corporation
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.version = 11
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor = Microsoft
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url = https://www.microsoft.com
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url.bug = https://github.com/microsoft/openjdk/issues
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.version = Microsoft-11367290
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version = 11.0.27
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version.date = 2025-04-15
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.compressedOopsMode = Zero based
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.info = mixed mode
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.name = OpenJDK 64-Bit Server VM
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.name = Java Virtual Machine Specification
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.vendor = Oracle Corporation
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.version = 11
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.vendor = Microsoft
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.version = 11.0.27+6-LTS
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - javasvn.timeout = 10000
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - jdk.debug = release
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ldap.bind.password = **PASSWORD**HIDDEN**
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.audit.enabled = true
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.auto.scan.enabled = true
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.size = 100
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.ttl = 1800
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.check.interval = 0
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.expired = true
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.local.files = true
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.features = all
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.max.users = 10
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.plugin.id = com.fasnote.alm.plugin.manage
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.mode = true
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.show.machine.code = true
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.machine.binding = true
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.network.validation = true
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.directory = dev-licenses
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.encryption.enabled = false
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.hot.reload.enabled = true
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.log.level = DEBUG
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.machine.binding.enabled = false
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.max.plugins = 1000
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.scan.interval = 60
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.signature.validation.enabled = false
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.validation.timeout = 1000
2025-08-02 13:34:29,657 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - licenseForNewUserAccount = 
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - line.separator = 

2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.contextSelector = org.apache.logging.log4j.core.selector.BasicContextSelector
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.loggerContextFactory = org.apache.logging.log4j.core.impl.Log4jContextFactory
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - logDir = /opt/polarion/data/workspace/.metadata/
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - login = polarion
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - mavenConfigDir = /opt/polarion/polarion/../maven
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - minimalPasswordLength = **PASSWORD**HIDDEN**
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.equinox.simpleconfigurator.configUrl = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/org.eclipse.equinox.simpleconfigurator/bundles.info
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.lyo.oslc4j.strictDatatypes = false
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.executionenvironment = OSGi/Minimum-1.0, OSGi/Minimum-1.1, OSGi/Minimum-1.2, JavaSE/compact1-1.8, JavaSE/compact2-1.8, JavaSE/compact3-1.8, JRE-1.1, J2SE-1.2, J2SE-1.3, J2SE-1.4, J2SE-1.5, JavaSE-1.6, JavaSE-1.7, JavaSE-1.8, JavaSE-9, JavaSE-10, JavaSE-11
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.language = zh
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.name = MacOSX
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.version = 15.5.0
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.processor = aarch64
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.storage = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.capabilities = osgi.ee; osgi.ee="OSGi/Minimum"; version:List<Version>="1.0, 1.1, 1.2", osgi.ee; osgi.ee="JRE"; version:List<Version>="1.0, 1.1", osgi.ee; osgi.ee="JavaSE"; version:List<Version>="1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact1"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact2"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact3"; version:List<Version>="1.8, 9.0, 10.0, 11.0"
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.packages = com.sun.jarsigner, com.sun.java.accessibility.util, com.sun.javadoc, com.sun.jdi, com.sun.jdi.connect, com.sun.jdi.connect.spi, com.sun.jdi.event, com.sun.jdi.request, com.sun.jndi.ldap.spi, com.sun.management, com.sun.net.httpserver, com.sun.net.httpserver.spi, com.sun.nio.file, com.sun.nio.sctp, com.sun.security.auth, com.sun.security.auth.callback, com.sun.security.auth.login, com.sun.security.auth.module, com.sun.security.jgss, com.sun.source.doctree, com.sun.source.tree, com.sun.source.util, com.sun.tools.attach, com.sun.tools.attach.spi, com.sun.tools.javac, com.sun.tools.javadoc, com.sun.tools.jconsole, java.applet, java.awt, java.awt.color, java.awt.datatransfer, java.awt.desktop, java.awt.dnd, java.awt.event, java.awt.font, java.awt.geom, java.awt.im, java.awt.im.spi, java.awt.image, java.awt.image.renderable, java.awt.print, java.beans, java.beans.beancontext, java.io, java.lang, java.lang.annotation, java.lang.instrument, java.lang.invoke, java.lang.management, java.lang.module, java.lang.ref, java.lang.reflect, java.math, java.net, java.net.http, java.net.spi, java.nio, java.nio.channels, java.nio.channels.spi, java.nio.charset, java.nio.charset.spi, java.nio.file, java.nio.file.attribute, java.nio.file.spi, java.rmi, java.rmi.activation, java.rmi.dgc, java.rmi.registry, java.rmi.server, java.security, java.security.acl, java.security.cert, java.security.interfaces, java.security.spec, java.sql, java.text, java.text.spi, java.time, java.time.chrono, java.time.format, java.time.temporal, java.time.zone, java.util, java.util.concurrent, java.util.concurrent.atomic, java.util.concurrent.locks, java.util.function, java.util.jar, java.util.logging, java.util.prefs, java.util.regex, java.util.spi, java.util.stream, java.util.zip, javax.accessibility, javax.annotation.processing, javax.crypto, javax.crypto.interfaces, javax.crypto.spec, javax.imageio, javax.imageio.event, javax.imageio.metadata, javax.imageio.plugins.bmp, javax.imageio.plugins.jpeg, javax.imageio.plugins.tiff, javax.imageio.spi, javax.imageio.stream, javax.lang.model, javax.lang.model.element, javax.lang.model.type, javax.lang.model.util, javax.management, javax.management.loading, javax.management.modelmbean, javax.management.monitor, javax.management.openmbean, javax.management.relation, javax.management.remote, javax.management.remote.rmi, javax.management.timer, javax.naming, javax.naming.directory, javax.naming.event, javax.naming.ldap, javax.naming.spi, javax.net, javax.net.ssl, javax.print, javax.print.attribute, javax.print.attribute.standard, javax.print.event, javax.rmi.ssl, javax.script, javax.security.auth, javax.security.auth.callback, javax.security.auth.kerberos, javax.security.auth.login, javax.security.auth.spi, javax.security.auth.x500, javax.security.cert, javax.security.sasl, javax.smartcardio, javax.sound.midi, javax.sound.midi.spi, javax.sound.sampled, javax.sound.sampled.spi, javax.sql, javax.sql.rowset, javax.sql.rowset.serial, javax.sql.rowset.spi, javax.swing, javax.swing.border, javax.swing.colorchooser, javax.swing.event, javax.swing.filechooser, javax.swing.plaf, javax.swing.plaf.basic, javax.swing.plaf.metal, javax.swing.plaf.multi, javax.swing.plaf.nimbus, javax.swing.plaf.synth, javax.swing.table, javax.swing.text, javax.swing.text.html, javax.swing.text.html.parser, javax.swing.text.rtf, javax.swing.tree, javax.swing.undo, javax.tools, javax.transaction.xa, javax.xml, javax.xml.catalog, javax.xml.crypto, javax.xml.crypto.dom, javax.xml.crypto.dsig, javax.xml.crypto.dsig.dom, javax.xml.crypto.dsig.keyinfo, javax.xml.crypto.dsig.spec, javax.xml.datatype, javax.xml.namespace, javax.xml.parsers, javax.xml.stream, javax.xml.stream.events, javax.xml.stream.util, javax.xml.transform, javax.xml.transform.dom, javax.xml.transform.sax, javax.xml.transform.stax, javax.xml.transform.stream, javax.xml.validation, javax.xml.xpath, jdk.dynalink, jdk.dynalink.beans, jdk.dynalink.linker, jdk.dynalink.linker.support, jdk.dynalink.support, jdk.javadoc.doclet, jdk.jfr, jdk.jfr.consumer, jdk.jshell, jdk.jshell.execution, jdk.jshell.spi, jdk.jshell.tool, jdk.management.jfr, jdk.nashorn.api.scripting, jdk.nashorn.api.tree, jdk.net, jdk.nio, jdk.security.jarsigner, jdk.swing.interop, netscape.javascript, org.ietf.jgss, org.w3c.dom, org.w3c.dom.bootstrap, org.w3c.dom.css, org.w3c.dom.events, org.w3c.dom.html, org.w3c.dom.ls, org.w3c.dom.ranges, org.w3c.dom.stylesheets, org.w3c.dom.traversal, org.w3c.dom.views, org.w3c.dom.xpath, org.xml.sax, org.xml.sax.ext, org.xml.sax.helpers, sun.misc, sun.reflect
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.uuid = 8981859f-3a32-4078-88a3-b7d164260a8c
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.vendor = Eclipse
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.version = 1.9.0
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.extension = true
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.fragment = true
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.requirebundle = true
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.client.readbuffer.usedirect = true
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.server.readbuffer.usedirect = true
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.arch = aarch64
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.name = Mac OS X
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.version = 15.5
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.arch = arm64
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles = reference:file:/opt/polarion/polarion/plugins/org.eclipse.equinox.simpleconfigurator_1.3.0.v20180502-1828.jar@1:start
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles.defaultStartLevel = 4
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.checkConfiguration = true
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation = true
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation.default = true
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.area = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.cascaded = false
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.dev = file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework = file:/opt/polarion/polarion/plugins/org.eclipse.osgi_3.13.0.v20180409-1500.jar
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.shape = jar
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.useSystemProperties = true
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.install.area = file:/opt/polarion/polarion/plugins/
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.instance.area = file:/opt/polarion/data/workspace/
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.logfile = /opt/polarion/data/workspace/.metadata/.log
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.nl = zh_CN_#Hans
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.os = linux
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.syspath = /opt/polarion/polarion/plugins
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.tracefile = /opt/polarion/data/workspace/.metadata/trace.log
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.ws = linux
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - password = **PASSWORD**HIDDEN**
2025-08-02 13:34:29,658 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - path.separator = :
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfbox.fontcache = /opt/polarion/data/workspace/polarion-data
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfexport.config = /opt/polarion/polarion/configuration/pdfexport.xml
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.id = polarion-shared
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.url = file:///opt/polarion/data/shared-maven-repo
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.maven.location.maven2 = /opt/polarion/polarion/../maven/distribution
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.size = 100
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.with.history = false
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.tx.doc.cache.size = 100
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.alm = https://polarion.plm.automation.siemens.com/products/polarion-alm
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.qa = https://polarion.plm.automation.siemens.com/products/polarion-qa
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/polarion-requirements
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - repo = http://localhost/repo
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - rolesForNewUserAccount = user
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - RRDir = /opt/polarion/data/RR
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - SDKDir = /opt/polarion/polarion/SDK
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - secure.approvals = false
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - shutdownCatchPhrase = shutdown
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - simple.profiler.enabled = false
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - skip.data.preloading = false
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - socksNonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stderr.encoding = UTF-8
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stdout.encoding = UTF-8
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - storeUrl.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/licensing?product=REQUIREMENTS
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.arch.data.model = 64
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.boot.library.path = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/lib
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.endian = little
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.isalist = 
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.io.unicode.encoding = UnicodeBig
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.command = org.eclipse.equinox.launcher.Main -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -configuration file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/ -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.launcher = SUN_STANDARD
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.jnu.encoding = UTF-8
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.management.compiler = HotSpot 64-Bit Tiered Compilers
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.os.patch.level = unknown
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.contact = https://polarion.plm.automation.siemens.com/techsupport/resources
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.license.email = <EMAIL>
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.sales.email = <EMAIL>
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.access.file = /opt/polarion/data/svn/access
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.passwd.file = /opt/polarion/data/svn/passwd
2025-08-02 13:34:29,659 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.http.encoding = UTF-8
2025-08-02 13:34:29,660 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.library.gnome-keyring.enabled = false
2025-08-02 13:34:29,660 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.ajp13-port = 8889
2025-08-02 13:34:29,660 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.request.safeListedHosts = 0.0.0.0
2025-08-02 13:34:29,660 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.country = CN
2025-08-02 13:34:29,660 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.dir = /Applications/Eclipse JEE.app/Contents/MacOS
2025-08-02 13:34:29,660 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.home = /Users/<USER>
2025-08-02 13:34:29,660 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.language = zh
2025-08-02 13:34:29,660 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.name = zhangwentian
2025-08-02 13:34:29,660 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.script = Hans
2025-08-02 13:34:29,660 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.timezone = Asia/Shanghai
2025-08-02 13:34:29,660 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - userAccountVault = /opt/polarion/data/workspace/user-account-vault
2025-08-02 13:34:29,660 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - workDir = /opt/polarion/data/workspace/polarion-data
2025-08-02 13:34:29,660 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** END of Java system properties
2025-08-02 13:34:29,661 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - XML parsers factory: com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl
2025-08-02 13:34:29,662 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Starting Platform...
2025-08-02 13:34:29,669 [main] INFO  PolarionLicensing - Searching for valid license file in /opt/polarion/polarion/license
2025-08-02 13:34:29,671 [main] INFO  PolarionLicensing - Trying to load license file polarion.lic
2025-08-02 13:34:29,672 [main] INFO  PolarionLicensing - The license file contains the following fields:
2025-08-02 13:34:29,672 [main] INFO  PolarionLicensing - *** License fields ***
2025-08-02 13:34:29,672 [main] INFO  PolarionLicensing - VariantsNamedUsers = 3
2025-08-02 13:34:29,672 [main] INFO  PolarionLicensing - almNamedUsers = 3
2025-08-02 13:34:29,672 [main] INFO  PolarionLicensing - dateCreated = 23.07.2025
2025-08-02 13:34:29,672 [main] INFO  PolarionLicensing - expirationDate = 21.08.2025
2025-08-02 13:34:29,672 [main] INFO  PolarionLicensing - hardwareKey = 8AG9-261C-1962
2025-08-02 13:34:29,672 [main] INFO  PolarionLicensing - licenseFormat = 2022
2025-08-02 13:34:29,672 [main] INFO  PolarionLicensing - licenseType = EVAL
2025-08-02 13:34:29,672 [main] INFO  PolarionLicensing - multiInstanceRunningInstances = 3
2025-08-02 13:34:29,672 [main] INFO  PolarionLicensing - userCompany = Polarion Eval
2025-08-02 13:34:29,672 [main] INFO  PolarionLicensing - *** License fields END ***
2025-08-02 13:34:29,686 [main] INFO  PolarionLicensing - Removing allocations by null
2025-08-02 13:34:29,687 [main] INFO  PolarionLicensing - STATS:concurrentVariantsUser,current:0,peak:0,limit:0
2025-08-02 13:34:29,687 [main] INFO  PolarionLicensing - 0 namedReviewerUser assignments (out of 0) loaded: []
2025-08-02 13:34:29,687 [main] INFO  PolarionLicensing - 0 concurrentReviewerUser assignments (out of 0) loaded: []
2025-08-02 13:34:29,687 [main] INFO  PolarionLicensing - STATS:concurrentReviewerUser,current:0,peak:0,limit:0
2025-08-02 13:34:29,687 [main] INFO  PolarionLicensing - 0 namedXBaseUser assignments (out of 0) loaded: []
2025-08-02 13:34:29,687 [main] INFO  PolarionLicensing - 0 concurrentXBaseUser assignments (out of 0) loaded: []
2025-08-02 13:34:29,687 [main] INFO  PolarionLicensing - STATS:concurrentXBaseUser,current:0,peak:0,limit:0
2025-08-02 13:34:29,687 [main] INFO  PolarionLicensing - 0 namedXProUser assignments (out of 0) loaded: []
2025-08-02 13:34:29,687 [main] INFO  PolarionLicensing - 0 concurrentXProUser assignments (out of 0) loaded: []
2025-08-02 13:34:29,687 [main] INFO  PolarionLicensing - STATS:concurrentXProUser,current:0,peak:0,limit:0
2025-08-02 13:34:29,687 [main] INFO  PolarionLicensing - 0 namedXEnterpriseUser assignments (out of 0) loaded: []
2025-08-02 13:34:29,687 [main] INFO  PolarionLicensing - 0 concurrentXEnterpriseUser assignments (out of 0) loaded: []
2025-08-02 13:34:29,687 [main] INFO  PolarionLicensing - STATS:concurrentXEnterpriseUser,current:0,peak:0,limit:0
2025-08-02 13:34:29,687 [main] INFO  PolarionLicensing - 0 namedProUser assignments (out of 0) loaded: []
2025-08-02 13:34:29,687 [main] INFO  PolarionLicensing - 0 concurrentProUser assignments (out of 0) loaded: []
2025-08-02 13:34:29,687 [main] INFO  PolarionLicensing - STATS:concurrentProUser,current:0,peak:0,limit:0
2025-08-02 13:34:29,687 [main] INFO  PolarionLicensing - 0 namedRequirementsUser assignments (out of 0) loaded: []
2025-08-02 13:34:29,687 [main] INFO  PolarionLicensing - 0 concurrentRequirementsUser assignments (out of 0) loaded: []
2025-08-02 13:34:29,687 [main] INFO  PolarionLicensing - STATS:concurrentRequirementsUser,current:0,peak:0,limit:0
2025-08-02 13:34:29,688 [main] INFO  PolarionLicensing - 0 namedQAUser assignments (out of 0) loaded: []
2025-08-02 13:34:29,688 [main] INFO  PolarionLicensing - 0 concurrentQAUser assignments (out of 0) loaded: []
2025-08-02 13:34:29,688 [main] INFO  PolarionLicensing - STATS:concurrentQAUser,current:0,peak:0,limit:0
2025-08-02 13:34:29,688 [main] INFO  PolarionLicensing - 3 namedALMUser assignments (out of 3) loaded: [admin, ou_d6f3139d36fb2978b33a8f870096b9e3, mTest]
2025-08-02 13:34:29,688 [main] INFO  PolarionLicensing - 0 concurrentALMUser assignments (out of 0) loaded: []
2025-08-02 13:34:29,688 [main] INFO  PolarionLicensing - STATS:concurrentALMUser,current:0,peak:0,limit:0
2025-08-02 13:34:29,688 [main] INFO  PolarionLicensing - 
*******************************************************************
 Polarion successfully activated
*******************************************************************
2025-08-02 13:34:29,743 [main] INFO  com.polarion.platform.internal.i18n.LanguageContributor - Localization file /META-INF/messages_en.properties read successfully (7789 messages)
2025-08-02 13:34:29,770 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Processing bundles:
2025-08-02 13:34:29,771 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [0] - org.eclipse.osgi
2025-08-02 13:34:29,771 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [1] - org.eclipse.equinox.simpleconfigurator
2025-08-02 13:34:29,771 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [2] - antlr
2025-08-02 13:34:29,771 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [3] - antlr4
2025-08-02 13:34:29,771 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [4] - antlr4-runtime
2025-08-02 13:34:29,771 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [5] - bcprov
2025-08-02 13:34:29,772 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [6] - com.auth0.java-jwt
2025-08-02 13:34:29,772 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [7] - com.fasnote.alm.auth.feishu
2025-08-02 13:34:29,774 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.fasnote.alm.auth.feishu to HiveMind
2025-08-02 13:34:29,774 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [8] - com.fasnote.alm.checklist
2025-08-02 13:34:29,775 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [9] - com.fasnote.alm.injection
2025-08-02 13:34:29,775 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [10] - com.fasnote.alm.plugin.manage
2025-08-02 13:34:29,775 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [11] - com.fasnote.alm.test
2025-08-02 13:34:29,776 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [12] - com.fasnote.alm.watermark
2025-08-02 13:34:29,776 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [13] - com.fasterxml.classmate
2025-08-02 13:34:29,777 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [14] - com.fasterxml.jackson
2025-08-02 13:34:29,777 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [15] - com.fasterxml.jackson.dataformat.jackson-dataformat-yaml
2025-08-02 13:34:29,777 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [16] - com.fasterxml.jackson.jaxrs
2025-08-02 13:34:29,777 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [17] - com.fasterxml.woodstox
2025-08-02 13:34:29,777 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [18] - com.finething.hesai.ai
2025-08-02 13:34:29,779 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.finething.hesai.ai to HiveMind
2025-08-02 13:34:29,779 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [19] - com.finething.hesai.defect
2025-08-02 13:34:29,779 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.finething.hesai.defect to HiveMind
2025-08-02 13:34:29,779 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [20] - com.google.gson
2025-08-02 13:34:29,779 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [21] - com.google.guava
2025-08-02 13:34:29,779 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [22] - com.google.guava.failureaccess
2025-08-02 13:34:29,779 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [23] - com.ibm.icu.icu4j
2025-08-02 13:34:29,780 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [24] - com.icl.saxon
2025-08-02 13:34:29,780 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [25] - com.jayway.jsonpath.json-path
2025-08-02 13:34:29,780 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [26] - com.jcraft.jsch
2025-08-02 13:34:29,780 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [27] - com.networknt.json-schema-validator
2025-08-02 13:34:29,780 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [28] - com.nimbusds.content-type
2025-08-02 13:34:29,780 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [29] - com.nimbusds.nimbus-jose-jwt
2025-08-02 13:34:29,780 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [30] - com.opensymphony.quartz
2025-08-02 13:34:29,781 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [31] - com.polarion.alm.ProjectPlanGantt_new
2025-08-02 13:34:29,782 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.ProjectPlanGantt_new to HiveMind
2025-08-02 13:34:29,782 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [32] - com.polarion.alm.builder
2025-08-02 13:34:29,782 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.builder to HiveMind
2025-08-02 13:34:29,782 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [33] - com.polarion.alm.checker
2025-08-02 13:34:29,783 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.checker to HiveMind
2025-08-02 13:34:29,783 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [34] - com.polarion.alm.extension.vcontext
2025-08-02 13:34:29,783 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.extension.vcontext to HiveMind
2025-08-02 13:34:29,783 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [35] - com.polarion.alm.impex
2025-08-02 13:34:29,784 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.impex to HiveMind
2025-08-02 13:34:29,784 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [36] - com.polarion.alm.install
2025-08-02 13:34:29,784 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [37] - com.polarion.alm.oslc
2025-08-02 13:34:29,786 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.oslc to HiveMind
2025-08-02 13:34:29,787 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [38] - com.polarion.alm.projects
2025-08-02 13:34:29,787 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.projects to HiveMind
2025-08-02 13:34:29,787 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [39] - com.polarion.alm.qcentre
2025-08-02 13:34:29,787 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.qcentre to HiveMind
2025-08-02 13:34:29,787 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [40] - com.polarion.alm.tracker
2025-08-02 13:34:29,788 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.tracker to HiveMind
2025-08-02 13:34:29,788 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [41] - com.polarion.alm.ui
2025-08-02 13:34:29,791 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.ui to HiveMind
2025-08-02 13:34:29,791 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [42] - com.polarion.alm.ui.diagrams.mxgraph
2025-08-02 13:34:29,792 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [43] - com.polarion.alm.wiki
2025-08-02 13:34:29,793 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.wiki to HiveMind
2025-08-02 13:34:29,793 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [44] - com.polarion.alm.ws
2025-08-02 13:34:29,793 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [45] - com.polarion.alm.ws.client
2025-08-02 13:34:29,794 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [46] - com.polarion.cluster
2025-08-02 13:34:29,794 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.cluster to HiveMind
2025-08-02 13:34:29,794 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [47] - com.polarion.core.boot
2025-08-02 13:34:29,794 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [48] - com.polarion.core.util
2025-08-02 13:34:29,795 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [49] - com.polarion.fop
2025-08-02 13:34:29,796 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [50] - com.polarion.platform
2025-08-02 13:34:29,796 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform to HiveMind
2025-08-02 13:34:29,796 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [51] - com.polarion.platform.guice
2025-08-02 13:34:29,796 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [52] - com.polarion.platform.hivemind
2025-08-02 13:34:29,797 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.hivemind to HiveMind
2025-08-02 13:34:29,797 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [53] - com.polarion.platform.jobs
2025-08-02 13:34:29,798 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.jobs to HiveMind
2025-08-02 13:34:29,798 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [54] - com.polarion.platform.monitoring
2025-08-02 13:34:29,799 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.monitoring to HiveMind
2025-08-02 13:34:29,799 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [55] - com.polarion.platform.persistence
2025-08-02 13:34:29,799 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.persistence to HiveMind
2025-08-02 13:34:29,799 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [56] - com.polarion.platform.repository
2025-08-02 13:34:29,800 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository to HiveMind
2025-08-02 13:34:29,800 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [57] - com.polarion.platform.repository.driver.svn
2025-08-02 13:34:29,800 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.driver.svn to HiveMind
2025-08-02 13:34:29,800 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [58] - com.polarion.platform.repository.external
2025-08-02 13:34:29,801 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external to HiveMind
2025-08-02 13:34:29,801 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [59] - com.polarion.platform.repository.external.git
2025-08-02 13:34:29,801 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.git to HiveMind
2025-08-02 13:34:29,801 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [60] - com.polarion.platform.repository.external.svn
2025-08-02 13:34:29,801 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.svn to HiveMind
2025-08-02 13:34:29,801 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [61] - com.polarion.platform.sql
2025-08-02 13:34:29,801 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [62] - com.polarion.portal.tomcat
2025-08-02 13:34:29,805 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [63] - com.polarion.psvn.launcher
2025-08-02 13:34:29,805 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.psvn.launcher to HiveMind
2025-08-02 13:34:29,805 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [64] - com.polarion.psvn.translations.en
2025-08-02 13:34:29,806 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [65] - com.polarion.purevariants
2025-08-02 13:34:29,806 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.purevariants to HiveMind
2025-08-02 13:34:29,806 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [66] - com.polarion.qcentre
2025-08-02 13:34:29,807 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [67] - com.polarion.scripting
2025-08-02 13:34:29,813 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.scripting to HiveMind
2025-08-02 13:34:29,813 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [68] - com.polarion.scripting.servlet
2025-08-02 13:34:29,813 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [69] - com.polarion.subterra.base
2025-08-02 13:34:29,813 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [70] - com.polarion.subterra.index
2025-08-02 13:34:29,814 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.index to HiveMind
2025-08-02 13:34:29,815 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [71] - com.polarion.subterra.persistence
2025-08-02 13:34:29,815 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence to HiveMind
2025-08-02 13:34:29,815 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [72] - com.polarion.subterra.persistence.document
2025-08-02 13:34:29,816 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence.document to HiveMind
2025-08-02 13:34:29,816 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [73] - com.polarion.synchronizer
2025-08-02 13:34:29,816 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.synchronizer to HiveMind
2025-08-02 13:34:29,816 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [74] - com.polarion.synchronizer.proxy.feishu
2025-08-02 13:34:29,817 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [75] - com.polarion.synchronizer.proxy.hpalm
2025-08-02 13:34:29,821 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [76] - com.polarion.synchronizer.proxy.jira
2025-08-02 13:34:29,821 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [77] - com.polarion.synchronizer.proxy.polarion
2025-08-02 13:34:29,822 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [78] - com.polarion.synchronizer.proxy.reqif
2025-08-02 13:34:29,831 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [79] - com.polarion.synchronizer.ui
2025-08-02 13:34:29,831 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [80] - com.polarion.usdp.persistence
2025-08-02 13:34:29,832 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.usdp.persistence to HiveMind
2025-08-02 13:34:29,832 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [81] - com.polarion.xray.doc.user
2025-08-02 13:34:29,832 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [82] - com.siemens.des.logger.api
2025-08-02 13:34:29,832 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [83] - com.siemens.plm.bitools.analytics
2025-08-02 13:34:29,832 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [84] - com.siemens.polarion.ct.collectors.git
2025-08-02 13:34:29,833 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.ct.collectors.git to HiveMind
2025-08-02 13:34:29,833 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [85] - com.siemens.polarion.eclipse.configurator
2025-08-02 13:34:29,834 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [86] - com.siemens.polarion.integration.ci
2025-08-02 13:34:29,834 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.integration.ci to HiveMind
2025-08-02 13:34:29,834 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [87] - com.siemens.polarion.previewer
2025-08-02 13:34:29,835 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer to HiveMind
2025-08-02 13:34:29,835 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [88] - com.siemens.polarion.previewer.external
2025-08-02 13:34:29,835 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer.external to HiveMind
2025-08-02 13:34:29,835 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [89] - com.siemens.polarion.rest
2025-08-02 13:34:29,835 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [90] - com.siemens.polarion.rt
2025-08-02 13:34:29,836 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [91] - com.siemens.polarion.rt.api
2025-08-02 13:34:29,836 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [92] - com.siemens.polarion.rt.collectors.git
2025-08-02 13:34:29,836 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [93] - com.siemens.polarion.rt.communication.common
2025-08-02 13:34:29,836 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [94] - com.siemens.polarion.rt.communication.polarion
2025-08-02 13:34:29,837 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.rt.communication.polarion to HiveMind
2025-08-02 13:34:29,837 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [95] - com.siemens.polarion.rt.communication.rt
2025-08-02 13:34:29,837 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [96] - com.siemens.polarion.rt.parsers.c
2025-08-02 13:34:29,837 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [97] - com.siemens.polarion.rt.ui
2025-08-02 13:34:29,838 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [98] - com.siemens.polarion.synchronizer.proxy.tfs
2025-08-02 13:34:29,838 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [99] - com.sun.activation.javax.activation
2025-08-02 13:34:29,839 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [100] - com.sun.istack.commons-runtime
2025-08-02 13:34:29,839 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [101] - com.sun.jna
2025-08-02 13:34:29,839 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [102] - com.sun.jna.platform
2025-08-02 13:34:29,839 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [103] - com.sun.xml.bind.jaxb-impl
2025-08-02 13:34:29,839 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [104] - com.teamlive.hozon.expcounter
2025-08-02 13:34:29,839 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.teamlive.hozon.expcounter to HiveMind
2025-08-02 13:34:29,839 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [105] - com.teamlive.livechecklist
2025-08-02 13:34:29,840 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.teamlive.livechecklist to HiveMind
2025-08-02 13:34:29,840 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [106] - com.trilead.ssh2
2025-08-02 13:34:29,840 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [107] - com.zaxxer.hikariCP
2025-08-02 13:34:29,840 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [108] - des-sdk-core
2025-08-02 13:34:29,840 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [109] - des-sdk-dss
2025-08-02 13:34:29,840 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [110] - io.github.resilience4j.circuitbreaker
2025-08-02 13:34:29,840 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [111] - io.github.resilience4j.core
2025-08-02 13:34:29,840 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [112] - io.github.resilience4j.retry
2025-08-02 13:34:29,840 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [113] - io.swagger
2025-08-02 13:34:29,840 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [114] - io.vavr
2025-08-02 13:34:29,840 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [115] - jakaroma
2025-08-02 13:34:29,840 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [116] - jakarta.validation.validation-api
2025-08-02 13:34:29,841 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [117] - javassist
2025-08-02 13:34:29,841 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [118] - javax.annotation-api
2025-08-02 13:34:29,841 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [119] - javax.cache
2025-08-02 13:34:29,841 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [120] - javax.el
2025-08-02 13:34:29,841 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [121] - javax.inject
2025-08-02 13:34:29,841 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [122] - javax.servlet
2025-08-02 13:34:29,841 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [123] - javax.servlet.jsp
2025-08-02 13:34:29,841 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [124] - javax.transaction
2025-08-02 13:34:29,841 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [125] - jaxb-api
2025-08-02 13:34:29,841 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [126] - jcip-annotations
2025-08-02 13:34:29,841 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [127] - jcl.over.slf4j
2025-08-02 13:34:29,841 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [128] - jul.to.slf4j
2025-08-02 13:34:29,841 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [129] - kuromoji-core
2025-08-02 13:34:29,841 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [130] - kuromoji-ipadic
2025-08-02 13:34:29,842 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [131] - lang-tag
2025-08-02 13:34:29,842 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [132] - net.htmlparser.jericho
2025-08-02 13:34:29,842 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [133] - net.java.dev.jna
2025-08-02 13:34:29,842 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [134] - net.minidev.accessors-smart
2025-08-02 13:34:29,842 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [135] - net.minidev.asm
2025-08-02 13:34:29,842 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [136] - net.minidev.json-smart
2025-08-02 13:34:29,842 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [137] - net.n3.nanoxml
2025-08-02 13:34:29,842 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [138] - net.sourceforge.cssparser
2025-08-02 13:34:29,842 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [139] - nu.xom
2025-08-02 13:34:29,842 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [140] - oauth2-oidc-sdk
2025-08-02 13:34:29,843 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [141] - org.apache.ant
2025-08-02 13:34:29,850 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [142] - org.apache.avro
2025-08-02 13:34:29,850 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [143] - org.apache.axis
2025-08-02 13:34:29,853 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [144] - org.apache.batik
2025-08-02 13:34:29,854 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [145] - org.apache.commons.codec
2025-08-02 13:34:29,854 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [146] - org.apache.commons.collections
2025-08-02 13:34:29,854 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [147] - org.apache.commons.commons-beanutils
2025-08-02 13:34:29,854 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [148] - org.apache.commons.commons-collections4
2025-08-02 13:34:29,854 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [149] - org.apache.commons.commons-compress
2025-08-02 13:34:29,854 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [150] - org.apache.commons.commons-fileupload
2025-08-02 13:34:29,854 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [151] - org.apache.commons.digester
2025-08-02 13:34:29,854 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [152] - org.apache.commons.exec
2025-08-02 13:34:29,854 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [153] - org.apache.commons.io
2025-08-02 13:34:29,854 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [154] - org.apache.commons.lang
2025-08-02 13:34:29,855 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [155] - org.apache.commons.lang3
2025-08-02 13:34:29,855 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [156] - org.apache.commons.logging
2025-08-02 13:34:29,855 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [157] - org.apache.curator
2025-08-02 13:34:29,860 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [158] - org.apache.fop
2025-08-02 13:34:29,860 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [159] - org.apache.hivemind
2025-08-02 13:34:29,860 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle org.apache.hivemind to HiveMind
2025-08-02 13:34:29,860 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [160] - org.apache.httpcomponents.httpclient
2025-08-02 13:34:29,861 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [161] - org.apache.httpcomponents.httpcore
2025-08-02 13:34:29,861 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [162] - org.apache.jasper.glassfish
2025-08-02 13:34:29,861 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [163] - org.apache.kafka.clients
2025-08-02 13:34:29,861 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [164] - org.apache.kafka.streams
2025-08-02 13:34:29,861 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [165] - org.apache.logging.log4j.1.2-api
2025-08-02 13:34:29,861 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [166] - org.apache.logging.log4j.api
2025-08-02 13:34:29,861 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [167] - org.apache.logging.log4j.apiconf
2025-08-02 13:34:29,862 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [168] - org.apache.logging.log4j.core
2025-08-02 13:34:29,862 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [169] - org.apache.logging.log4j.slf4j-impl
2025-08-02 13:34:29,862 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [170] - org.apache.lucene.analyzers-common
2025-08-02 13:34:29,862 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [171] - org.apache.lucene.analyzers-common
2025-08-02 13:34:29,862 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [172] - org.apache.lucene.analyzers-smartcn
2025-08-02 13:34:29,863 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [173] - org.apache.lucene.core
2025-08-02 13:34:29,863 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [174] - org.apache.lucene.core
2025-08-02 13:34:29,865 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [175] - org.apache.lucene.grouping
2025-08-02 13:34:29,865 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [176] - org.apache.lucene.queryparser
2025-08-02 13:34:29,865 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [177] - org.apache.oro
2025-08-02 13:34:29,865 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [178] - org.apache.pdfbox.fontbox
2025-08-02 13:34:29,865 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [179] - org.apache.poi
2025-08-02 13:34:29,867 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [180] - org.apache.tika
2025-08-02 13:34:30,302 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [181] - org.apache.xalan
2025-08-02 13:34:30,302 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [182] - org.apache.xercesImpl
2025-08-02 13:34:30,302 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [183] - org.apache.xml.serializer
2025-08-02 13:34:30,302 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [184] - org.apache.xmlgraphics.commons
2025-08-02 13:34:30,302 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [185] - org.apache.zookeeper
2025-08-02 13:34:30,303 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [186] - org.codehaus.groovy
2025-08-02 13:34:30,303 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [187] - org.codehaus.jettison
2025-08-02 13:34:30,303 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [188] - org.dom4j
2025-08-02 13:34:30,303 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [189] - org.eclipse.core.contenttype
2025-08-02 13:34:30,304 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [190] - org.eclipse.core.expressions
2025-08-02 13:34:30,304 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [191] - org.eclipse.core.filesystem
2025-08-02 13:34:30,304 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [192] - org.eclipse.core.jobs
2025-08-02 13:34:30,304 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [193] - org.eclipse.core.net
2025-08-02 13:34:30,304 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [194] - org.eclipse.core.resources
2025-08-02 13:34:30,304 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [195] - org.eclipse.core.runtime
2025-08-02 13:34:30,304 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [196] - org.eclipse.equinox.app
2025-08-02 13:34:30,304 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [197] - org.eclipse.equinox.common
2025-08-02 13:34:30,304 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [198] - org.eclipse.equinox.event
2025-08-02 13:34:30,304 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [199] - org.eclipse.equinox.http.registry
2025-08-02 13:34:30,304 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [200] - org.eclipse.equinox.http.servlet
2025-08-02 13:34:30,304 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [201] - org.eclipse.equinox.jsp.jasper
2025-08-02 13:34:30,304 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [202] - org.eclipse.equinox.jsp.jasper.registry
2025-08-02 13:34:30,304 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [203] - org.eclipse.equinox.launcher
2025-08-02 13:34:30,304 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [204] - org.eclipse.equinox.preferences
2025-08-02 13:34:30,304 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [205] - org.eclipse.equinox.registry
2025-08-02 13:34:30,305 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [206] - org.eclipse.equinox.security
2025-08-02 13:34:30,305 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [207] - org.eclipse.help
2025-08-02 13:34:30,305 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [208] - org.eclipse.help.base
2025-08-02 13:34:30,305 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [209] - org.eclipse.help.webapp
2025-08-02 13:34:30,305 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [210] - org.eclipse.jgit
2025-08-02 13:34:30,305 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [211] - org.eclipse.osgi.services
2025-08-02 13:34:30,305 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [212] - org.eclipse.osgi.util
2025-08-02 13:34:30,305 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [213] - org.ehcache
2025-08-02 13:34:30,306 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [214] - org.gitlab.java-gitlab-api
2025-08-02 13:34:30,306 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [215] - org.glassfish.jersey
2025-08-02 13:34:30,307 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [216] - org.hibernate.annotations
2025-08-02 13:34:30,307 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [217] - org.hibernate.core
2025-08-02 13:34:30,307 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [218] - org.hibernate.entitymanager
2025-08-02 13:34:30,307 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [219] - org.hibernate.hikaricp
2025-08-02 13:34:30,307 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [220] - org.hibernate.jpa.2.1.api
2025-08-02 13:34:30,307 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [221] - org.jboss.logging
2025-08-02 13:34:30,307 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [222] - org.jvnet.mimepull
2025-08-02 13:34:30,307 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [223] - org.objectweb.asm
2025-08-02 13:34:30,307 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [224] - org.objectweb.jotm
2025-08-02 13:34:30,308 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [225] - org.opensaml
2025-08-02 13:34:30,319 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [226] - org.polarion.svncommons
2025-08-02 13:34:30,320 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [227] - org.polarion.svnwebclient
2025-08-02 13:34:30,320 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [228] - org.postgesql
2025-08-02 13:34:30,320 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [229] - org.projectlombok.lombok
2025-08-02 13:34:30,330 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [230] - org.rocksdb.rocksdbjni
2025-08-02 13:34:30,330 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [231] - org.springframework.data.core
2025-08-02 13:34:30,330 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [232] - org.springframework.data.jpa
2025-08-02 13:34:30,330 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [233] - org.springframework.spring-aop
2025-08-02 13:34:30,330 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [234] - org.springframework.spring-beans
2025-08-02 13:34:30,330 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [235] - org.springframework.spring-context
2025-08-02 13:34:30,331 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [236] - org.springframework.spring-core
2025-08-02 13:34:30,331 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [237] - org.springframework.spring-expression
2025-08-02 13:34:30,331 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [238] - org.springframework.spring-jdbc
2025-08-02 13:34:30,331 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [239] - org.springframework.spring-orm
2025-08-02 13:34:30,331 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [240] - org.springframework.spring-test
2025-08-02 13:34:30,331 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [241] - org.springframework.spring-tx
2025-08-02 13:34:30,331 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [242] - org.springframework.spring-web
2025-08-02 13:34:30,331 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [243] - org.springframework.spring-webmvc
2025-08-02 13:34:30,332 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [244] - org.tmatesoft.sqljet
2025-08-02 13:34:30,332 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [245] - org.tmatesoft.svnkit
2025-08-02 13:34:30,332 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [246] - saaj-api
2025-08-02 13:34:30,332 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [247] - sdk-lifecycle-collab
2025-08-02 13:34:30,332 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [248] - sdk-lifecycle-docmgmt
2025-08-02 13:34:30,332 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [249] - siemens.des.clientsecurity
2025-08-02 13:34:30,332 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [250] - slf4j.api
2025-08-02 13:34:30,333 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [251] - xml-apis
2025-08-02 13:34:30,333 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [252] - xml.apis.ext
2025-08-02 13:34:30,333 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [253] - xstream
2025-08-02 13:34:30,510 [main] INFO  com.polarion.core.util.remote.server.SocketRemoteControlServer - Remote control server socket is ready to listen on localhost/127.0.0.1:8887
2025-08-02 13:34:30,510 [xServer:8887] INFO  org.xsocket.connection.Server - server listening on localhost:8887 (xSocket 2.5.3)
2025-08-02 13:34:30,677 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database...
2025-08-02 13:34:30,729 [main] INFO  com.polarion.platform.sql.internal.PgServerInfo - PG server listening on localhost:5435
2025-08-02 13:34:32,857 [main] INFO  com.polarion.platform.internal.cache.CacheConfigurator - EHCache uses internal configuration
2025-08-02 13:34:33,114 [main] WARN  org.ehcache.impl.internal.executor.PooledExecutionService - No default pool configured, services requiring thread pools must be configured explicitly using named thread pools
2025-08-02 13:34:33,183 [main] INFO  org.ehcache.sizeof.filters.AnnotationSizeOfFilter - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-02 13:34:33,187 [main] INFO  org.ehcache.sizeof.impl.JvmInformation - Detected JVM data model settings of: 64-Bit OpenJDK JVM with Compressed OOPs
2025-08-02 13:34:33,199 [main] INFO  org.ehcache.sizeof.impl.AgentLoader - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-02 13:34:33,272 [main] INFO  com.polarion.platform.internal.cache.CachingProviderHandler - All the caches have been destroyed because of not clean shutdown. You can ignore this message if Polarion started in reindex mode.
2025-08-02 13:34:33,305 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-08-02 13:34:33,306 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-08-02 13:34:33,307 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion_history' is: *************************************************
2025-08-02 13:34:33,353 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database finished [ TIME 2.67 s. ]
2025-08-02 13:34:33,547 [main] INFO  com.polarion.platform.cluster.ClusterService - Initializing cluster service
2025-08-02 13:34:33,548 [main] INFO  com.polarion.platform.cluster.ClusterService - Cluster service is disabled.
2025-08-02 13:34:33,732 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-02 13:34:33,752 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Starting...
2025-08-02 13:34:33,795 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Start completed.
2025-08-02 13:34:33,846 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.114 s. ]
2025-08-02 13:34:33,846 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-02 13:34:33,855 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Starting...
2025-08-02 13:34:33,860 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Start completed.
2025-08-02 13:34:33,882 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.035 s. ]
2025-08-02 13:34:33,882 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot started
2025-08-02 13:34:33,934 [main] INFO  com.polarion.platform.repository.driver.svn.internal.security.SVNWatcher - SVN auth file watcher started with a period of 3000 milliseconds
2025-08-02 13:34:33,940 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-02 13:34:33,961 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion authenticated from system
2025-08-02 13:34:33,998 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion logged in from system
2025-08-02 13:34:34,004 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Created
2025-08-02 13:34:34,005 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Initialized
2025-08-02 13:34:34,010 [main | u:p] INFO  com.polarion.core.util.profiling.SimpleProfiler - Initialization
2025-08-02 13:34:34,021 [main | u:p] INFO  org.objectweb.jotm - JOTM started with a local transaction factory which is not bound.
2025-08-02 13:34:34,021 [main | u:p] INFO  org.objectweb.jotm - CAROL initialization
2025-08-02 13:34:34,026 [main | u:p] INFO  com.polarion.platform.internal.service.repository.listeners.job.PullingJob - lastFullyProcessedRevision [275]
2025-08-02 13:34:34,032 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - END initializeService
2025-08-02 13:34:34,037 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Polarion startup estimation:  [ TIME 18 s. ]
2025-08-02 13:34:34,037 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 18 s. ]
2025-08-02 13:34:34,043 [main | u:p] INFO  com.polarion.platform.monitoring - Full monitoring results are stored in file /opt/polarion/data/workspace/monitoring/results.txt
2025-08-02 13:34:34,073 [main | u:p] INFO  com.polarion.platform.monitoring - Executing actions from stage PREBOOT
2025-08-02 13:34:34,074 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 13:34:34,075 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,417.74 GB
 [Sat Aug 02 13:34:34 CST 2025]
2025-08-02 13:34:34,257 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.jvm.version'
2025-08-02 13:34:34,257 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.jvm.version (JVM Version) = JVM Version 
Microsoft OpenJDK 64-Bit Server VM 11.0.27+6-LTS
 [Sat Aug 02 13:34:34 CST 2025]
2025-08-02 13:34:34,260 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.os.version'
2025-08-02 13:34:34,260 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.os.version (OS Version) = OS Version 
Mac OS X 15.5 aarch64
 [Sat Aug 02 13:34:34 CST 2025]
2025-08-02 13:34:34,263 [main | u:p] INFO  com.polarion.platform.monitoring - Finished with actions from stage PREBOOT: {OK=3}
2025-08-02 13:34:34,263 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.64 s. ]
2025-08-02 13:34:34,263 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0373 s [61% update (144x), 38% query (12x)] (221x), svn: 0.0146 s [56% getLatestRevision (2x), 29% testConnection (1x)] (4x)
2025-08-02 13:34:34,263 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - calling ILowLevelPersistence.boot to start persistence
2025-08-02 13:34:34,272 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization started
2025-08-02 13:34:34,303 [main | u:p] INFO  com.polarion.subterra.base.internal.location.LocationCacheContext - Registered invalidationListener: com.polarion.platform.repository.internal.config.RepositoryConfigService$1@6395cf5a
2025-08-02 13:34:34,327 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: BaselineCollection
2025-08-02 13:34:34,327 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: TestRun
2025-08-02 13:34:34,327 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: Plan
2025-08-02 13:34:34,341 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition started
2025-08-02 13:34:34,341 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:34:34,341 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-02 13:34:34,363 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition finished [ TIME 0.0218 s. ]
2025-08-02 13:34:34,363 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context tree: 
ROOT_CTX_NAME (ContextNature[Root], ContextId[context [global]])
+-default (ContextNature[Repository], ContextId[cluster default, context [global]])
  +-WBS (ContextNature[Project], ContextId[cluster default, context WBS])
  +-WBSdev (ContextNature[Project], ContextId[cluster default, context WBSdev])
  +-Demo Projects (ContextNature[ProjectGroup], ContextId[cluster default, context --Demo Projects])
  | +-elibrary (ContextNature[Project], ContextId[cluster default, context elibrary])
  | +-drivepilot (ContextNature[Project], ContextId[cluster default, context drivepilot])
  +-library (ContextNature[Project], ContextId[cluster default, context library])
  +-hesai (ContextNature[Project], ContextId[cluster default, context hesai])
2025-08-02 13:34:34,363 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-08-02 13:34:34,363 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0285 s [56% getDir2 content (2x), 36% info (3x)] (6x)
2025-08-02 13:34:34,363 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:34:34,363 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-02 13:34:34,363 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Startup workers for phase 3: 6
2025-08-02 13:34:34,368 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-08-02 13:34:34,368 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-02 13:34:34,369 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBS] (4/9) ...
2025-08-02 13:34:34,369 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBSdev] (5/9) ...
2025-08-02 13:34:34,368 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-08-02 13:34:34,368 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-08-02 13:34:34,368 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-08-02 13:34:34,370 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[context [global]] (1/9) ...
2025-08-02 13:34:34,370 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context --Demo Projects] (3/9) ...
2025-08-02 13:34:34,368 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-08-02 13:34:34,369 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context [global]] (6/9) ...
2025-08-02 13:34:34,370 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context library] (2/9) ...
2025-08-02 13:34:34,380 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[context [global]] (1/9) TOOK  [ TIME 0.0101 s. ]
2025-08-02 13:34:34,381 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-02 13:34:34,381 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context hesai] (7/9) ...
2025-08-02 13:34:34,490 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/.polarion'
2025-08-02 13:34:34,499 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/modules'
2025-08-02 13:34:34,508 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/documents'
2025-08-02 13:34:34,515 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/_wiki'
2025-08-02 13:34:34,517 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context --Demo Projects contains 0 primary objects (work items+comments).
2025-08-02 13:34:34,517 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context --Demo Projects] (3/9) TOOK  [ TIME 0.147 s. ]
2025-08-02 13:34:34,517 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-02 13:34:34,517 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context elibrary] (8/9) ...
2025-08-02 13:34:34,541 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/library/documents'
2025-08-02 13:34:34,548 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBS/documents'
2025-08-02 13:34:34,599 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context library contains 288 primary objects (work items+comments).
2025-08-02 13:34:34,599 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context library] (2/9) TOOK  [ TIME 0.229 s. ]
2025-08-02 13:34:34,599 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-02 13:34:34,599 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context drivepilot] (9/9) ...
2025-08-02 13:34:34,626 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBS contains 344 primary objects (work items+comments).
2025-08-02 13:34:34,627 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBS] (4/9) TOOK  [ TIME 0.258 s. ]
2025-08-02 13:34:34,630 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/elibrary/documents'
2025-08-02 13:34:34,693 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context elibrary contains 334 primary objects (work items+comments).
2025-08-02 13:34:34,693 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context elibrary] (8/9) TOOK  [ TIME 0.176 s. ]
2025-08-02 13:34:34,728 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/drivepilot/documents'
2025-08-02 13:34:34,748 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/hesai/documents'
2025-08-02 13:34:34,770 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context drivepilot contains 461 primary objects (work items+comments).
2025-08-02 13:34:34,770 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context drivepilot] (9/9) TOOK  [ TIME 0.171 s. ]
2025-08-02 13:34:34,825 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context hesai contains 1148 primary objects (work items+comments).
2025-08-02 13:34:34,825 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context hesai] (7/9) TOOK  [ TIME 0.444 s. ]
2025-08-02 13:34:34,909 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context null contains 2214 primary objects (work items+comments).
2025-08-02 13:34:34,910 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context [global]] (6/9) TOOK  [ TIME 0.541 s. ]
2025-08-02 13:34:34,955 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBSdev/documents'
2025-08-02 13:34:35,075 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBSdev contains 3322 primary objects (work items+comments).
2025-08-02 13:34:35,076 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBSdev] (5/9) TOOK  [ TIME 0.707 s. ]
2025-08-02 13:34:35,077 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.711 s, CPU [user: 0.184 s, system: 0.3 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.113 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-02 13:34:35,077 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.711 s, CPU [user: 0.106 s, system: 0.24 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.0763 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-02 13:34:35,077 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.711 s, CPU [user: 0.221 s, system: 0.361 s], Allocated memory: 68.8 MB, transactions: 0, ObjectMaps: 0.12 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-02 13:34:35,077 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.71 s, CPU [user: 0.0661 s, system: 0.118 s], Allocated memory: 8.6 MB, transactions: 0, svn: 0.0835 s [80% log2 (10x), 11% getLatestRevision (2x)] (13x), ObjectMaps: 0.0633 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-02 13:34:35,077 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.711 s, CPU [user: 0.0488 s, system: 0.0961 s], Allocated memory: 7.1 MB, transactions: 0, ObjectMaps: 0.067 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-02 13:34:35,078 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.71 s, CPU [user: 0.1 s, system: 0.192 s], Allocated memory: 16.6 MB, transactions: 0, svn: 0.096 s [39% log2 (10x), 18% getLatestRevision (3x), 18% log (1x), 17% info (5x)] (24x), ObjectMaps: 0.0909 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-02 13:34:35,078 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.72 s. ]
2025-08-02 13:34:35,078 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.53 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.272 s [61% log2 (36x), 15% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-08-02 13:34:35,089 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [START].
2025-08-02 13:34:35,089 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:34:35,089 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-02 13:34:35,188 [main | u:p | u:p] ERROR com.polarion.platform.repository.internal.config.RepositoryConfigService$ConfigProblemCatcher - Failed to work with configuration from location /WBS/.polarion/repositories/repositories.xml:
[/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
com.polarion.platform.repository.config.RepositoryConfigurationException: [/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:87) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocations(AbstractDataHandler.java:61) ~[platform-repository.jar:?]
	at $IDataHandler_198694649fe.readLocations($IDataHandler_198694649fe.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readLocations(RepositoryConfigService.java:291) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$3.runImpl(RepositoryConfigService.java:328) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction$1.runWEx(RepositoryConfigService.java:113) ~[platform-repository.jar:?]
	at com.polarion.core.util.RunnableWEx.runWRet(RunnableWEx.java:61) ~[util.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction.run(RepositoryConfigService.java:123) ~[platform-repository.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:361) ~[?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:58) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:422) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:412) ~[platform.jar:?]
	at $ISecurityService_19869464819.doAsSystemUser($ISecurityService_19869464819.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readConfiguration(RepositoryConfigService.java:324) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getConfigurationImpl(RepositoryConfigService.java:239) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfigurationImpl(RepositoryConfigService.java:199) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:177) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.addConfigurationListener(RepositoryConfigService.java:263) ~[platform-repository.jar:?]
	at $IRepositoryConfigService_19869464827.addConfigurationListener($IRepositoryConfigService_19869464827.java) ~[?:?]
	at com.polarion.platform.repository.external.internal.ExternalRepositoryProviderRegistry.initialize(ExternalRepositoryProviderRegistry.java:146) ~[platform-repository.jar:?]
	at $IExternalRepositoryProviderRegistry_198694648e3.initialize($IExternalRepositoryProviderRegistry_198694648e3.java) ~[?:?]
	at $IExternalRepositoryProviderRegistry_198694648e2.initialize($IExternalRepositoryProviderRegistry_198694648e2.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule.initModule(RevisionsPersistenceModule.java:353) ~[platform-persistence.jar:?]
	at $IObjectPersistenceModule_198694649d2.initModule($IObjectPersistenceModule_198694649d2.java) ~[?:?]
	at com.polarion.subterra.persistence.internal.PersistenceEngine.initModule(PersistenceEngine.java:251) ~[subterra-uniform-persistence.jar:?]
	at $IPersistenceEngine_198694649ba.initModule($IPersistenceEngine_198694649ba.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.LowLevelDataService.boot(LowLevelDataService.java:352) ~[platform-persistence.jar:?]
	at $ILowLevelPersistence_198694648df.boot($ILowLevelPersistence_198694648df.java) ~[?:?]
	at $ILowLevelPersistence_198694648de.boot($ILowLevelPersistence_198694648de.java) ~[?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.lambda$0(PlatformService.java:294) ~[launcher.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:423) [?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:69) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:417) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:407) [platform.jar:?]
	at $ISecurityService_1986946481a.doAsSystemUser($ISecurityService_1986946481a.java) [?:?]
	at $ISecurityService_19869464819.doAsSystemUser($ISecurityService_19869464819.java) [?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.bootPlatform(PlatformService.java:286) [launcher.jar:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.start(PlatformService.java:92) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.runImpl(PolarionSVNApplication.java:139) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.run(PolarionSVNApplication.java:94) [launcher.jar:?]
	at com.polarion.core.boot.launchers.BasicAppLauncher.launch(BasicAppLauncher.java:53) [boot.jar:?]
	at com.polarion.core.boot.impl.AppLaunchersManager.start(AppLaunchersManager.java:184) [boot.jar:?]
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196) [org.eclipse.equinox.app_1.3.500.v20171221-2204.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:388) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:243) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:656) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:592) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.run(Main.java:1498) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.main(Main.java:1471) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
Caused by: com.thoughtworks.xstream.converters.ConversionException: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:77) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
Caused by: com.thoughtworks.xstream.mapper.CannotResolveClassException: AutoBranchGitLab
	at com.thoughtworks.xstream.mapper.DefaultMapper.realClass(DefaultMapper.java:81) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.DynamicProxyMapper.realClass(DynamicProxyMapper.java:55) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.PackageAliasingMapper.realClass(PackageAliasingMapper.java:88) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ClassAliasingMapper.realClass(ClassAliasingMapper.java:79) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ArrayMapper.realClass(ArrayMapper.java:74) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.SecurityMapper.realClass(SecurityMapper.java:71) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.CachingMapper.realClass(CachingMapper.java:47) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.util.HierarchicalStreams.readClassType(HierarchicalStreams.java:29) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readBareItem(AbstractCollectionConverter.java:131) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readItem(AbstractCollectionConverter.java:117) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readCompleteItem(AbstractCollectionConverter.java:147) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.ArrayConverter.unmarshal(ArrayConverter.java:54) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:72) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
2025-08-02 13:34:35,295 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-02 13:34:35,305 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions from repository default in context ContextId[context [global]] finished
2025-08-02 13:34:35,305 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [FINISHED].
2025-08-02 13:34:35,305 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-08-02 13:34:35,305 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.196 s [100% getReadConfiguration (48x)] (48x), svn: 0.0764 s [87% info (18x)] (38x)
2025-08-02 13:34:35,328 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:34:35,328 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-02 13:34:35,328 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting repository for build artifacts-related changes
2025-08-02 13:34:35,328 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[context [global]]
2025-08-02 13:34:35,328 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[context [global]]
2025-08-02 13:34:35,330 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[context [global]] has been successfully processed
2025-08-02 13:34:35,331 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[context [global]] finished [ TIME 0.00285 s. ]
2025-08-02 13:34:35,331 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-02 13:34:35,331 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context [global]]
2025-08-02 13:34:35,331 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context [global]]
2025-08-02 13:34:35,355 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context [global]] has been successfully processed
2025-08-02 13:34:35,380 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context [global]] finished [ TIME 0.0486 s. ]
2025-08-02 13:34:35,380 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-02 13:34:35,380 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBS]
2025-08-02 13:34:35,380 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBS]
2025-08-02 13:34:35,400 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBS] has been successfully processed
2025-08-02 13:34:35,425 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBS] finished [ TIME 0.0452 s. ]
2025-08-02 13:34:35,425 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-02 13:34:35,425 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBSdev]
2025-08-02 13:34:35,425 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBSdev]
2025-08-02 13:34:35,441 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBSdev] has been successfully processed
2025-08-02 13:34:35,459 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBSdev] finished [ TIME 0.0334 s. ]
2025-08-02 13:34:35,459 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-02 13:34:35,459 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context --Demo Projects]
2025-08-02 13:34:35,459 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context --Demo Projects]
2025-08-02 13:34:35,467 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context --Demo Projects] has been successfully processed
2025-08-02 13:34:35,476 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context --Demo Projects] finished [ TIME 0.0175 s. ]
2025-08-02 13:34:35,477 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-02 13:34:35,477 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context library]
2025-08-02 13:34:35,477 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context library]
2025-08-02 13:34:35,487 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context library] has been successfully processed
2025-08-02 13:34:35,497 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context library] finished [ TIME 0.0208 s. ]
2025-08-02 13:34:35,497 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-02 13:34:35,498 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context hesai]
2025-08-02 13:34:35,498 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context hesai]
2025-08-02 13:34:35,511 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context hesai] has been successfully processed
2025-08-02 13:34:35,527 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context hesai] finished [ TIME 0.0293 s. ]
2025-08-02 13:34:35,527 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-02 13:34:35,527 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context elibrary]
2025-08-02 13:34:35,527 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context elibrary]
2025-08-02 13:34:35,537 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context elibrary] has been successfully processed
2025-08-02 13:34:35,553 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context elibrary] finished [ TIME 0.0258 s. ]
2025-08-02 13:34:35,553 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-02 13:34:35,553 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context drivepilot]
2025-08-02 13:34:35,553 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context drivepilot]
2025-08-02 13:34:35,563 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context drivepilot] has been successfully processed
2025-08-02 13:34:35,582 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context drivepilot] finished [ TIME 0.0288 s. ]
2025-08-02 13:34:35,582 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-02 13:34:35,582 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... repository inspection finished [ TIME 0.254 s. ]
2025-08-02 13:34:35,582 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.28 s. ]
2025-08-02 13:34:35,582 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.216 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.164 s [100% getReadConfiguration (54x)] (54x)
2025-08-02 13:34:35,582 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:34:35,582 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-02 13:34:35,582 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting BIR for new or removed builds
2025-08-02 13:34:35,595 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were removed (including calculations from previous run)
2025-08-02 13:34:35,595 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were added or modified
2025-08-02 13:34:35,596 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... BIR inspection finished [ TIME 0.0135 s. ]
2025-08-02 13:34:35,596 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-02 13:34:35,596 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:34:35,596 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-02 13:34:35,596 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing startup index events, starting iterations.
2025-08-02 13:34:35,596 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Iteration 1 - processing 5 events
2025-08-02 13:34:35,602 [main | u:p] INFO  com.polarion.alm.tracker.internal.planning.PlanFieldsProvider - livePlanXMLLocation: Location[path /default/.reports/xml/live-plan.xml]
2025-08-02 13:34:35,621 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener -  - reindexing 1 existing objects and 0 deleted objects.
2025-08-02 13:34:35,691 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-02 13:34:35,693 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPage
2025-08-02 13:34:35,693 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-08-02 13:34:35,693 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPageAttachment
2025-08-02 13:34:35,694 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-02 13:34:35,694 [main | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-08-02 13:34:35,733 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPage
2025-08-02 13:34:35,733 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPageAttachment
2025-08-02 13:34:35,733 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Attachment
2025-08-02 13:34:35,734 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-08-02 13:34:35,734 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-08-02 13:34:35,736 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Attachment
2025-08-02 13:34:35,737 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-08-02 13:34:35,743 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem
2025-08-02 13:34:35,744 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: UserGroup
2025-08-02 13:34:35,744 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BaselineCollection
2025-08-02 13:34:35,745 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: UserGroup
2025-08-02 13:34:35,745 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRun
2025-08-02 13:34:35,751 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRun
2025-08-02 13:34:35,751 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BaselineCollection
2025-08-02 13:34:35,751 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Build
2025-08-02 13:34:35,752 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleComment
2025-08-02 13:34:35,752 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Build
2025-08-02 13:34:35,754 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Comment
2025-08-02 13:34:35,754 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleComment
2025-08-02 13:34:35,754 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BuildArtifact
2025-08-02 13:34:35,755 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: DocumentWorkflowSignature
2025-08-02 13:34:35,756 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BuildArtifact
2025-08-02 13:34:35,756 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPageAttachment
2025-08-02 13:34:35,757 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPageAttachment
2025-08-02 13:34:35,758 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: DocumentWorkflowSignature
2025-08-02 13:34:35,758 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Comment
2025-08-02 13:34:35,759 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-08-02 13:34:35,760 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-08-02 13:34:35,760 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Baseline
2025-08-02 13:34:35,761 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem
2025-08-02 13:34:35,763 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Baseline
2025-08-02 13:34:35,763 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Plan
2025-08-02 13:34:35,764 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkRecord
2025-08-02 13:34:35,766 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Plan
2025-08-02 13:34:35,766 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunAttachment
2025-08-02 13:34:35,767 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunAttachment
2025-08-02 13:34:35,767 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem-OutlineNumbers
2025-08-02 13:34:35,767 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkRecord
2025-08-02 13:34:35,767 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-08-02 13:34:35,768 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem-OutlineNumbers
2025-08-02 13:34:35,768 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleAttachment
2025-08-02 13:34:35,769 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunComment
2025-08-02 13:34:35,770 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunComment
2025-08-02 13:34:35,770 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-08-02 13:34:35,770 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-08-02 13:34:35,771 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPage
2025-08-02 13:34:35,773 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Module
2025-08-02 13:34:35,779 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPage
2025-08-02 13:34:35,781 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Revision
2025-08-02 13:34:35,781 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleAttachment
2025-08-02 13:34:35,782 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}272
2025-08-02 13:34:35,782 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}274
2025-08-02 13:34:35,782 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}273
2025-08-02 13:34:35,782 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}270
2025-08-02 13:34:35,782 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}271
2025-08-02 13:34:35,782 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}224
2025-08-02 13:34:35,782 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}225
2025-08-02 13:34:35,782 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}226
2025-08-02 13:34:35,782 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}228
2025-08-02 13:34:35,782 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}230
2025-08-02 13:34:35,782 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}231
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}232
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}233
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}234
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}235
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}239
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}240
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}241
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}242
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}243
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}245
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}246
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}248
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}249
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}250
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}251
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}253
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}254
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}255
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}256
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}257
2025-08-02 13:34:35,783 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}258
2025-08-02 13:34:35,784 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}259
2025-08-02 13:34:35,784 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}260
2025-08-02 13:34:35,784 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}261
2025-08-02 13:34:35,784 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}262
2025-08-02 13:34:35,784 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}263
2025-08-02 13:34:35,784 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}264
2025-08-02 13:34:35,784 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}265
2025-08-02 13:34:35,784 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}267
2025-08-02 13:34:35,784 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}268
2025-08-02 13:34:35,784 [PolarionDocIdCreator-6] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}269
2025-08-02 13:34:35,785 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-08-02 13:34:35,785 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.104 s, CPU [user: 0.0396 s, system: 0.00893 s], Allocated memory: 10.0 MB
2025-08-02 13:34:35,787 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-08-02 13:34:35,791 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Module
2025-08-02 13:34:35,796 [PolarionDocIdCreator-1] INFO  com.polarion.subterra.index.impl.lucene.baseline.PolarionDocIdCreator - Bloom filter loading for 28 indices took  [ TIME 0.116 s. ]
2025-08-02 13:34:35,806 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.DelegatingCalculatedFieldsListener - Calculated fields mode: async
2025-08-02 13:34:35,808 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing took  [ TIME 0.212 s. ]
2025-08-02 13:34:35,808 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.21 s. ]
2025-08-02 13:34:35,808 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.212 s [100% doFinishStartup (1x)] (1x), commit: 0.0519 s [100% Revision (1x)] (1x), Lucene: 0.0381 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0142 s [100% objectsToInv (1x)] (1x)
2025-08-02 13:34:35,808 [main | u:p] INFO  com.polarion.platform.internal.service.repository.ListenerManager - Starting the pulling job for repository: default
2025-08-02 13:34:35,808 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization finished [ TIME 1.54 s. ]
2025-08-02 13:34:35,808 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:34:35,808 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-02 13:34:35,809 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.CalculatedFieldsStorage - Checking integrity of calculated fields storage /opt/polarion/data/workspace/polarion-data/calculated-fields
2025-08-02 13:34:35,815 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-02 13:34:35,815 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:34:35,815 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-02 13:34:35,831 [main | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - Updating local scheduler state: start
2025-08-02 13:34:35,837 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-08-02 13:34:35,841 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-02 13:34:35,841 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-08-02 13:34:35,841 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-08-02 13:34:35,841 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutting down.
2025-08-02 13:34:35,841 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED paused.
2025-08-02 13:34:35,841 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-08-02 13:34:35,842 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-08-02 13:34:35,843 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-02 13:34:35,843 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-08-02 13:34:35,843 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-08-02 13:34:35,843 [main | u:p | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - 15 scheduled job(s) configured
2025-08-02 13:34:35,847 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
2025-08-02 13:34:35,977 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot finished
2025-08-02 13:34:35,977 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform started
2025-08-02 13:34:35,985 [main] INFO  com.polarion.portal.tomcat.TomcatPlugin - Tomcat home directory was set to /opt/polarion/data/workspace/.metadata/.plugins/com.polarion.portal.tomcat
2025-08-02 13:34:35,990 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Starting Tomcat...
2025-08-02 13:34:36,046 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: webui, contextRoot: webapp/webui, plugin: com.polarion.alm.ui, priority: -10]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,046 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion, contextRoot: webapp/authapp, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,046 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/.well-known, contextRoot: webapp/well-known, plugin: com.polarion.platform, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,046 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ProjectPlanGantt, contextRoot: webapp, plugin: com.polarion.alm.ProjectPlanGantt_new, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,046 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/activate, contextRoot: webapp/activation, plugin: com.polarion.psvn.launcher, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,046 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/announcements, contextRoot: webapp/announcements, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,047 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/bir, contextRoot: webapp/bir, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,047 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/checklist, contextRoot: src/main/webapp, plugin: com.fasnote.alm.checklist, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,047 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/codemirror-modes, contextRoot: webapp/codemirror-modes, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,047 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/defect, contextRoot: webapp, plugin: com.finething.hesai.defect, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,047 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/doorsconnector, contextRoot: webapp, plugin: com.polarion.synchronizer, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,047 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/export, contextRoot: webapp/export, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,047 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/fileupload, contextRoot: webapp/fileupload, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,047 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/gwt, contextRoot: war, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,047 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/hesai-ai, contextRoot: webapp, plugin: com.finething.hesai.ai, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,047 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/icons, contextRoot: webapp/icons, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,047 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/internal-login, contextRoot: webapp/internal-login, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,047 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/livechecklist, contextRoot: webapp, plugin: com.teamlive.livechecklist, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,047 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/module-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,048 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/modulehome, contextRoot: webapp/module-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,048 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/mxgraph, contextRoot: draw.io/war, plugin: com.polarion.alm.ui.diagrams.mxgraph, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,048 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/oauth-feishu, contextRoot: webapp, plugin: com.fasnote.alm.auth.feishu, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,048 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/page-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,048 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/perf-testing, contextRoot: webapp/perf-testing, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,048 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/plugin-manage, contextRoot: webapp, plugin: com.fasnote.alm.plugin.manage, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,048 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/print, contextRoot: webapp/print, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,048 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/register, contextRoot: webapp/register, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,048 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rest, contextRoot: webapp, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,048 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ria, contextRoot: webapp/ria, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,048 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/richpagehome, contextRoot: webapp/richpage-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,049 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt, contextRoot: src/main/webapp, plugin: com.siemens.polarion.rt, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,049 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt-connect, contextRoot: ws, plugin: com.siemens.polarion.rt.communication.polarion, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,049 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/scripting, contextRoot: webapp/scripting, plugin: com.polarion.scripting.servlet, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,049 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/sdk, contextRoot: webapp/sdk, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,049 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/svnwebclient, contextRoot: webapp, plugin: org.polarion.svnwebclient, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,049 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/swagger, contextRoot: webapp/swagger, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,049 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/synchronizer, contextRoot: webapp, plugin: com.polarion.synchronizer.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,049 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/template-download, contextRoot: webapp/project-template, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,049 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/testrun-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,049 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/tour, contextRoot: webapp/tour, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,049 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/watermark, contextRoot: webapp, plugin: com.fasnote.alm.watermark, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,049 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,049 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment-auth, contextRoot: webapp/wi-attachment-auth, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,049 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/widget-resource, contextRoot: webapp/widget-resource, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,050 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wiki, contextRoot: src/main/webapp, plugin: com.polarion.alm.wiki, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,050 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/workreport, contextRoot: webapp/workreport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,050 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ws, contextRoot: ws, plugin: com.polarion.alm.ws, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,050 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/xunitimport, contextRoot: webapp/xunitimport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,050 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/oslc, contextRoot: webapp, plugin: com.polarion.alm.oslc, priority: 1]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:34:36,095 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Initializing ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-08-02 13:34:36,101 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-02 13:34:36,101 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-08-02 13:34:36,120 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@65ab87e4] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,120 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7257cbce] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,120 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@32b92218] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,120 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3dcde74] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,120 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@dc0b733] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,120 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5cf44218] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,133 [Catalina-utility-1] INFO  org.apache.catalina.startup.ContextConfig - No global web.xml found
2025-08-02 13:34:36,140 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,141 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,141 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,142 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,142 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [admin] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,161 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@18a564a7] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,162 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@399a4cd1] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,162 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@18b47bc6] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,163 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@12019819] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,164 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,167 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6f8efe9d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,168 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,168 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,169 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3a4af9a7] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,170 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7ee39bbb] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,170 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@56b7fcf6] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,171 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@68f07ef9] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,172 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,174 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,175 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,176 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3e9298c5] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,178 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4e99e262] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,179 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7d700d35] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,179 [Catalina-utility-1] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 13:34:36,181 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,182 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,183 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@54415d35] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,188 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3774ea9a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,188 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@32bce8fe] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,188 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@ee92d4e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,192 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,192 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,193 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@53b6d9] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,196 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@20a2fe41] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,199 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,200 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2d2c00d6] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,200 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,201 [Catalina-utility-1] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 13:34:36,203 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@636671c1] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,203 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,206 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7ab324e6] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,206 [Catalina-utility-3] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/oauth-feishu] - For security constraints with URL pattern [/userinfo] only the HTTP methods [POST GET] are covered. All other methods are uncovered.
2025-08-02 13:34:36,209 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2ea45459] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,209 [Catalina-utility-3] INFO  com.fasnote.alm.injection.osgi.InjectionActivator - 启动ALM依赖注入框架
2025-08-02 13:34:36,215 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4fe01994] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,223 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@35f7eb4] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,227 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,228 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 创建OSGi感知的纯粹依赖注入器
2025-08-02 13:34:36,228 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 初始化包扫描提供者跟踪机制
2025-08-02 13:34:36,228 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 包扫描提供者跟踪机制初始化完成
2025-08-02 13:34:36,228 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 开始注册 OSGi 服务到依赖注入容器
2025-08-02 13:34:36,229 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: org.osgi.framework.BundleContext
2025-08-02 13:34:36,229 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - OSGi 服务注册完成，已注册 1 个 OSGi 服务提供者
2025-08-02 13:34:36,229 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.osgi.InjectionActivator - 启动Bundle跟踪机制
2025-08-02 13:34:36,229 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2459db3c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,230 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 包扫描提供者跟踪已启动
2025-08-02 13:34:36,230 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@bf43fd] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,230 [Catalina-utility-3] INFO  com.fasnote.alm.injection.osgi.InjectionActivator - 包扫描提供者跟踪机制启动成功
2025-08-02 13:34:36,230 [Catalina-utility-3] INFO  com.fasnote.alm.injection.osgi.InjectionActivator - ALM依赖注入框架启动成功
2025-08-02 13:34:36,231 [Catalina-utility-3] INFO  com.fasnote.alm.auth.feishu.Activator - Feishu Authentication Plugin starting...
2025-08-02 13:34:36,231 [Catalina-utility-3] INFO  com.fasnote.alm.auth.feishu.Activator - 注册 FeishuPackageScanProvider 为 OSGi 服务...
2025-08-02 13:34:36,231 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.auth.feishu 注册DI扫描包路径: [com.fasnote.alm.auth.feishu]
2025-08-02 13:34:36,231 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.auth.feishu 就绪，开始扫描服务 (状态: 8, 扫描包: [com.fasnote.alm.auth.feishu])
2025-08-02 13:34:36,231 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasnote.alm.auth.feishu
2025-08-02 13:34:36,231 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 开始扫描 Bundle 服务: com.fasnote.alm.auth.feishu (状态: 8, 扫描包: [com.fasnote.alm.auth.feishu])
2025-08-02 13:34:36,234 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.auth.feishu 的包 com.fasnote.alm.auth.feishu 中发现 19 个类文件
2025-08-02 13:34:36,235 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory$FeishuAuthenticatorManagerInvocationHandler (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,246 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuOAuth2LoginClient (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,247 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,247 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.config.FeishuConfigurationAdapter (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,247 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuOAuth2Authenticator (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,248 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,248 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,249 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,249 [Catalina-utility-3] INFO  com.fasnote.alm.plugin.manage.Activator - 启动许可证管理插件...
2025-08-02 13:34:36,249 [Catalina-utility-3] DEBUG com.fasnote.alm.plugin.manage.Activator - 初始化许可证配置...
2025-08-02 13:34:36,250 [Catalina-utility-3] DEBUG com.fasnote.alm.plugin.manage.Activator - 从环境变量加载配置完成
2025-08-02 13:34:36,250 [Catalina-utility-3] DEBUG com.fasnote.alm.plugin.manage.Activator - 许可证配置初始化完成
2025-08-02 13:34:36,250 [Catalina-utility-3] INFO  com.fasnote.alm.plugin.manage.Activator - 注册 LicensePackageScanProvider 为 OSGi 服务...
2025-08-02 13:34:36,251 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.plugin.manage 注册DI扫描包路径: [com.fasnote.alm.plugin.manage.injection.module]
2025-08-02 13:34:36,251 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.plugin.manage 就绪，开始扫描服务 (状态: 8, 扫描包: [com.fasnote.alm.plugin.manage.injection.module])
2025-08-02 13:34:36,251 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,251 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 开始扫描 Bundle 服务: com.fasnote.alm.plugin.manage (状态: 8, 扫描包: [com.fasnote.alm.plugin.manage.injection.module])
2025-08-02 13:34:36,251 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.plugin.manage 的包 com.fasnote.alm.plugin.manage.injection.module 中发现 5 个类文件
2025-08-02 13:34:36,252 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,252 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 发现 IModule 实现: com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule (Bundle: com.fasnote.alm.plugin.manage)
2025-08-02 13:34:36,252 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功使用无参构造函数创建模块: com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule
2025-08-02 13:34:36,253 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,256 [Catalina-utility-3] INFO  com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule - 配置插件管理主模块...
2025-08-02 13:34:36,257 [Catalina-utility-3] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseModule使用无参构造函数创建
2025-08-02 13:34:36,257 [Catalina-utility-3] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 配置统一许可证模块（重构后）...
2025-08-02 13:34:36,257 [Catalina-utility-3] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 注册核心组件...
2025-08-02 13:34:36,258 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: java.util.Map
2025-08-02 13:34:36,259 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,259 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.security.MachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,259 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,259 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.security.MachineCodeProvider, 实现: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,260 [Catalina-utility-5] INFO  org.apache.tomcat.dbcp.dbcp2.BasicDataSourceFactory - Name = XWikiDS Ignoring unknown property: value of "DB Connection" for "description" property
2025-08-02 13:34:36,262 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.security.MachineCodeProvider -> com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider  [LAZY_SINGLETON]
2025-08-02 13:34:36,264 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,264 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,264 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,264 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.config.LicenseConfiguration, 实例类: com.fasnote.alm.plugin.manage.config.LicenseConfiguration, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,264 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage.config.LicenseConfiguration, 实例类型: LicenseConfiguration
2025-08-02 13:34:36,264 [Catalina-utility-3] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 核心组件注册完成
2025-08-02 13:34:36,264 [Catalina-utility-3] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 注册管理器组件...
2025-08-02 13:34:36,265 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.BundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,265 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IBundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,265 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.BundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,265 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IBundleManager, 实现: com.fasnote.alm.plugin.manage.core.BundleManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,265 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IBundleManager -> com.fasnote.alm.plugin.manage.core.BundleManager  [LAZY_SINGLETON]
2025-08-02 13:34:36,266 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.ClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,266 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,266 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.ClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,266 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IClassLoaderManager, 实现: com.fasnote.alm.plugin.manage.core.ClassLoaderManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,266 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IClassLoaderManager -> com.fasnote.alm.plugin.manage.core.ClassLoaderManager  [LAZY_SINGLETON]
2025-08-02 13:34:36,266 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,266 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,266 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,266 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseValidator, 实现: com.fasnote.alm.plugin.manage.core.LicenseValidator, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,266 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseValidator -> com.fasnote.alm.plugin.manage.core.LicenseValidator  [LAZY_SINGLETON]
2025-08-02 13:34:36,267 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,267 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,267 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,267 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager, 实现: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,267 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager -> com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager  [LAZY_SINGLETON]
2025-08-02 13:34:36,267 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,267 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,267 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,267 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator, 实现: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,267 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator  [LAZY_SINGLETON]
2025-08-02 13:34:36,268 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,268 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,268 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,268 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager, 实现: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,268 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager  [LAZY_SINGLETON]
2025-08-02 13:34:36,269 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,269 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,269 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,269 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseFileManager, 实现: com.fasnote.alm.plugin.manage.core.LicenseFileManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,269 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseFileManager -> com.fasnote.alm.plugin.manage.core.LicenseFileManager  [LAZY_SINGLETON]
2025-08-02 13:34:36,269 [Catalina-utility-3] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 管理器组件注册完成
2025-08-02 13:34:36,270 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,270 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,270 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,270 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseManager, 实现: com.fasnote.alm.plugin.manage.core.LicenseManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,270 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseManager -> com.fasnote.alm.plugin.manage.core.LicenseManager  [LAZY_SINGLETON]
2025-08-02 13:34:36,270 [Catalina-utility-3] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseManager接口绑定配置完成
2025-08-02 13:34:36,270 [Catalina-utility-3] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseAwareServiceResolver 将通过自动扫描机制注册
2025-08-02 13:34:36,270 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: com.fasnote.alm.plugin.manage.api.LicenseAware
2025-08-02 13:34:36,270 [Catalina-utility-3] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 统一许可证模块配置完成（重构后）
2025-08-02 13:34:36,271 [Catalina-utility-3] INFO  com.fasnote.alm.plugin.manage.web.injection.WebModule - 配置Web层依赖注入模块...
2025-08-02 13:34:36,271 [Catalina-utility-3] DEBUG com.fasnote.alm.plugin.manage.web.injection.WebModule - 注册Web服务...
2025-08-02 13:34:36,272 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,272 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,272 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,272 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl, 实现: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,273 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl  [LAZY_SINGLETON]
2025-08-02 13:34:36,273 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,273 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.web.service.PluginManagementService -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,273 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,273 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.web.service.PluginManagementService, 实现: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,273 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.web.service.PluginManagementService -> com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl  [LAZY_SINGLETON]
2025-08-02 13:34:36,273 [Catalina-utility-3] DEBUG com.fasnote.alm.plugin.manage.web.injection.WebModule - Web服务注册完成
2025-08-02 13:34:36,273 [Catalina-utility-3] INFO  com.fasnote.alm.plugin.manage.web.injection.WebModule - Web层依赖注入模块配置完成
2025-08-02 13:34:36,273 [Catalina-utility-3] INFO  com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule - 插件管理主模块配置完成
2025-08-02 13:34:36,273 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 已安装模块: PluginManageMainModule (优先级: 0)
2025-08-02 13:34:36,273 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功安装模块: com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule
2025-08-02 13:34:36,273 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.LicensePackageScanProvider (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,273 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.LicenseModule$LicenseAwareProvider$1 (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,273 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.LicenseModule$LicenseAwareProvider (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,273 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.LicenseModule (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,273 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 发现 IModule 实现: com.fasnote.alm.plugin.manage.injection.module.LicenseModule (Bundle: com.fasnote.alm.plugin.manage)
2025-08-02 13:34:36,273 [Catalina-utility-3] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseModule使用无参构造函数创建
2025-08-02 13:34:36,273 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功使用无参构造函数创建模块: com.fasnote.alm.plugin.manage.injection.module.LicenseModule
2025-08-02 13:34:36,273 [Catalina-utility-3] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 配置统一许可证模块（重构后）...
2025-08-02 13:34:36,273 [Catalina-utility-3] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 注册核心组件...
2025-08-02 13:34:36,273 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: java.util.Map
2025-08-02 13:34:36,274 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,274 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.security.MachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,274 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,274 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.security.MachineCodeProvider, 实现: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.security.MachineCodeProvider -> com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider  [LAZY_SINGLETON]
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.config.LicenseConfiguration, 实例类: com.fasnote.alm.plugin.manage.config.LicenseConfiguration, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage.config.LicenseConfiguration, 实例类型: LicenseConfiguration
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 核心组件注册完成
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 注册管理器组件...
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.BundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IBundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.BundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IBundleManager, 实现: com.fasnote.alm.plugin.manage.core.BundleManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IBundleManager -> com.fasnote.alm.plugin.manage.core.BundleManager  [LAZY_SINGLETON]
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.ClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.ClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IClassLoaderManager, 实现: com.fasnote.alm.plugin.manage.core.ClassLoaderManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IClassLoaderManager -> com.fasnote.alm.plugin.manage.core.ClassLoaderManager  [LAZY_SINGLETON]
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseValidator, 实现: com.fasnote.alm.plugin.manage.core.LicenseValidator, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseValidator -> com.fasnote.alm.plugin.manage.core.LicenseValidator  [LAZY_SINGLETON]
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager, 实现: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager -> com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager  [LAZY_SINGLETON]
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator, 实现: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator  [LAZY_SINGLETON]
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,275 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,276 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager, 实现: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,276 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager  [LAZY_SINGLETON]
2025-08-02 13:34:36,276 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,276 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,276 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,276 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseFileManager, 实现: com.fasnote.alm.plugin.manage.core.LicenseFileManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,276 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseFileManager -> com.fasnote.alm.plugin.manage.core.LicenseFileManager  [LAZY_SINGLETON]
2025-08-02 13:34:36,276 [Catalina-utility-3] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 管理器组件注册完成
2025-08-02 13:34:36,276 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,276 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,276 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,276 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseManager, 实现: com.fasnote.alm.plugin.manage.core.LicenseManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:34:36,276 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseManager -> com.fasnote.alm.plugin.manage.core.LicenseManager  [LAZY_SINGLETON]
2025-08-02 13:34:36,276 [Catalina-utility-3] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseManager接口绑定配置完成
2025-08-02 13:34:36,276 [Catalina-utility-3] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseAwareServiceResolver 将通过自动扫描机制注册
2025-08-02 13:34:36,276 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: com.fasnote.alm.plugin.manage.api.LicenseAware
2025-08-02 13:34:36,276 [Catalina-utility-3] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 统一许可证模块配置完成（重构后）
2025-08-02 13:34:36,276 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 已安装模块: RefactoredLicenseModule (优先级: 5)
2025-08-02 13:34:36,276 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功安装模块: com.fasnote.alm.plugin.manage.injection.module.LicenseModule
2025-08-02 13:34:36,277 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle 服务扫描完成: com.fasnote.alm.plugin.manage - 耗时: 25ms, 模块: 2, 服务: 0, 错误: 0
2025-08-02 13:34:36,277 [Catalina-utility-3] INFO  com.fasnote.alm.plugin.manage.Activator - LicensePackageScanProvider OSGi 服务注册成功
2025-08-02 13:34:36,277 [Catalina-utility-3] INFO  com.fasnote.alm.plugin.manage.Activator - 扫描包路径: [com.fasnote.alm.plugin.manage.injection.module]
2025-08-02 13:34:36,277 [Catalina-utility-3] INFO  com.fasnote.alm.plugin.manage.Activator - 许可证管理插件启动成功
2025-08-02 13:34:36,278 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 在注解 com.fasnote.alm.plugin.manage.annotation.LicenseImplementation 中发现 @Service 元注解
2025-08-02 13:34:36,278 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 发现服务相关注解: LicenseImplementation 在类 com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer
2025-08-02 13:34:36,278 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 发现服务类: com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu)
2025-08-02 13:34:36,279 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 自动注册服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu)
2025-08-02 13:34:36,289 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 设置默认服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer
2025-08-02 13:34:36,289 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 添加服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer (总数: 1)
2025-08-02 13:34:36,289 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu
2025-08-02 13:34:36,289 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu
2025-08-02 13:34:36,290 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuStateUtils (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,290 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuUserInfoMapper (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,290 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuUserInfoMapper$FeishuUserData (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,291 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuApiClient (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,291 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuUserInfoMapper$FeishuApiResponse (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,292 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.OAuth2UrlHandler (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,292 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuOAuth2LoginClient$1 (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,292 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuOAuth2DefaultLoginHandler (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,292 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuUserInfoServlet (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,293 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,295 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 在注解 com.fasnote.alm.plugin.manage.annotation.FallbackImplementation 中发现 @Service 元注解
2025-08-02 13:34:36,295 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 发现服务相关注解: FallbackImplementation 在类 com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer
2025-08-02 13:34:36,295 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 发现服务类: com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu)
2025-08-02 13:34:36,295 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 自动注册服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu)
2025-08-02 13:34:36,295 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 添加服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer (总数: 2)
2025-08-02 13:34:36,295 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu
2025-08-02 13:34:36,295 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu
2025-08-02 13:34:36,295 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuPackageScanProvider (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,295 [Catalina-utility-3] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.Activator (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:34:36,295 [Catalina-utility-3] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle 服务扫描完成: com.fasnote.alm.auth.feishu - 耗时: 64ms, 模块: 0, 服务: 2, 错误: 0
2025-08-02 13:34:36,295 [Catalina-utility-3] INFO  com.fasnote.alm.auth.feishu.Activator - FeishuPackageScanProvider OSGi 服务注册成功
2025-08-02 13:34:36,296 [Catalina-utility-3] INFO  com.fasnote.alm.auth.feishu.Activator - 扫描包路径: [com.fasnote.alm.auth.feishu]
2025-08-02 13:34:36,296 [Catalina-utility-3] INFO  com.fasnote.alm.auth.feishu.Activator - Feishu Authentication Plugin started successfully
2025-08-02 13:34:36,296 [Catalina-utility-3] INFO  com.fasnote.alm.auth.feishu.Activator - 飞书插件实现类将通过 OSGi 服务自动扫描注册
2025-08-02 13:34:36,316 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6288fc60] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,328 [Catalina-utility-1] INFO  org.polarion.svncommons.commentscache.CommentsCache - Initializing comments cache. Id: http://localhost/repo, repository: http://localhost/repo/, url: http://localhost/repo/, cache directory: /opt/polarion/data/workspace/polarion-data/log-messages-cache, cache page size: 100
2025-08-02 13:34:36,342 [Catalina-utility-5] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 13:34:36,347 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@27446428] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,349 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,350 [Catalina-utility-1] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 13:34:36,410 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@495e3dd2] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,413 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,427 [Catalina-utility-5] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 13:34:36,431 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@67d7f791] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,433 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,440 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7aa5b370] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,444 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,447 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6ebf8a38] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,466 [Catalina-utility-5] INFO  com.polarion.portal.velocity.VelocityPathManager - VelocityTemplatesPath=/opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/authapp/, /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/, /opt/polarion/polarion/plugins/com.polarion.alm.wiki_3.22.1/src/main/webapp/, ., /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/
2025-08-02 13:34:36,587 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@53780aa3] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,591 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3e573440] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,611 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@21eb022] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,614 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,618 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@142c4dd6] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,621 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,624 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@46c22594] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,630 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@deaba61] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,631 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@115bb033] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,635 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,638 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@b056c4a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,640 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@27a99b1e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,640 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,644 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7b1e8d37] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,645 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:34:36,650 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3d024aff] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:36,651 [Catalina-utility-6] INFO  com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Spring context refreshed, scanning for prompt variables...
2025-08-02 13:34:36,655 [Catalina-utility-6] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: polarionTool, Class: com.finething.hesai.ai.util.VelocityPolarionTool, Methods found: 29
2025-08-02 13:34:36,655 [Catalina-utility-6] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: enumTool, Class: com.finething.hesai.ai.util.EnumUtil, Methods found: 1
2025-08-02 13:34:36,655 [Catalina-utility-6] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: linkWorkItemUtil, Class: com.finething.hesai.ai.util.LinkWorkItemUtil, Methods found: 6
2025-08-02 13:34:36,656 [Catalina-utility-4] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 13:34:36,657 [Catalina-utility-6] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: polarionContext, Class: com.finething.hesai.ai.service.impl.PolarionContextServiceImpl, Methods found: 4
2025-08-02 13:34:36,657 [Catalina-utility-6] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - No methods with @MethodDescription found for variable: polarionHelper, Class: com.finething.hesai.ai.service.impl.DefaultVariableDescriptionProvider$HelperMethods
2025-08-02 13:34:36,657 [Catalina-utility-6] INFO  com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Finished scanning. Found 4 prompt variables.
2025-08-02 13:34:36,711 [Catalina-utility-2] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-08-02 13:34:36,811 [Catalina-utility-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Started.
2025-08-02 13:34:37,537 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-08-02 13:34:37,540 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[90]')
2025-08-02 13:34:37,540 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[90]')
2025-08-02 13:34:37,542 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[96]')
2025-08-02 13:34:37,546 [Catalina-utility-2] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-08-02 13:34:37,546 [Catalina-utility-2] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[90]')
2025-08-02 13:34:37,546 [Catalina-utility-2] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[92]')
2025-08-02 13:34:40,752 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Starting ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-08-02 13:34:40,759 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Tomcat is listening on port 8889 using AJP/1.3 protocol with 600000 timeout in millis
2025-08-02 13:34:40,759 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Starting Help Service...
2025-08-02 13:34:40,765 [main] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@cab0abd] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:34:40,771 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Help Service started
2025-08-02 13:34:40,836 [main | u:p] INFO  com.xpn.xwiki.XWiki - xwiki.cfg taken from /WEB-INF/xwiki.cfg because the XWikiConfig variable is not set in the context
2025-08-02 13:34:41,261 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-08-02 13:34:41,265 [Thread-33] INFO  class com.polarion.alm.server.util.ChartExporterStartup - Chart renderer says: Server started on 127.0.0.1:34567
2025-08-02 13:34:41,374 [ajp-nio-127.0.0.1-8889-exec-1 | cID:694672ca-7f000001-1cbc3d6e-960e5c12] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-08-02 13:34:41,396 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-08-02 13:34:41,396 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
2025-08-02 13:34:41,417 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" has id 694672fb-7f000001-1cbc3d6e-5d64f79b
2025-08-02 13:34:41,417 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to UNSCHEDULED
2025-08-02 13:34:41,418 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "Attachment Indexer" is /opt/polarion/data/workspace/polarion-data/jobs/20250802-1334
2025-08-02 13:34:41,419 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" runs as user "polarion"
2025-08-02 13:34:41,419 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to ACTIVATING
2025-08-02 13:34:41,420 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to WAITING
2025-08-02 13:34:41,420 [main | u:p] INFO  com.polarion.platform.monitoring - Next slow periodic actions will be executed on Sun Aug 03 01:00:41 CST 2025
2025-08-02 13:34:41,420 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.tracker.internal.HttpsConfiguratorStartup successfully initialized
2025-08-02 13:34:41,421 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.ChartExporterStartup successfully initialized
2025-08-02 13:34:41,421 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.wiki.WikiPlugin successfully initialized
2025-08-02 13:34:41,421 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.qcentre.internal.QCentreStartup successfully initialized
2025-08-02 13:34:41,421 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.siemens.polarion.rt.communication.connection.RtCommunicationStartup successfully initialized
2025-08-02 13:34:41,421 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.internal.startup.NotificationServerStartup successfully initialized
2025-08-02 13:34:41,421 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.subterra.index.impl.IndexingJobsStartup successfully initialized
2025-08-02 13:34:41,421 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.ui.server.ServerStartup successfully initialized
2025-08-02 13:34:41,421 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.FormulaServerStartup successfully initialized
2025-08-02 13:34:41,421 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.monitoring.internal.MonitoringServiceStart successfully initialized
2025-08-02 13:34:41,422 [main] INFO  com.polarion.platform.monitoring - Executing actions from stage POSTBOOT
2025-08-02 13:34:41,423 [main] INFO  com.polarion.platform.monitoring - Executing action 'polarion.info.cache.statistics'
2025-08-02 13:34:41,432 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" has id 69467315-7f000001-1cbc3d6e-25479574
2025-08-02 13:34:41,432 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to UNSCHEDULED
2025-08-02 13:34:41,434 [main] INFO  com.polarion.platform.monitoring - polarion.info.cache.statistics (Statistics of caches) = Statistics of caches 
                         CACHE       HITS     MISSES  HIT_RATIO       GETS       PUTS    REMOVAL   EVICTION 
     attachments-content-cache          0          0          0%          0          0          0          0 
                baseline-cache          0          0          0%          0          0          0          0 
                            bq          0          0          0%          0          0          0          0 
                dao-attachment          0          0          0%          0          0          0          0 
                   dao-comment          0          0          0%          0          0          0          0 
                    dao-global          0          4          0%          4          0          0          0 
                    dao-module          0          0          0%          0          0          0          0 
          dao-moduleattachment          0          0          0%          0          0          0          0 
             dao-modulecomment          0          0          0%          0          0          0          0 
                      dao-plan          0          0          0%          0          0          0          0 
                   dao-project          0          0          0%          0          0          0          0 
              dao-projectgroup          0          6          0%          6          3          0          0 
                  dao-richpage          0          0          0%          0          0          0          0 
        dao-richpageattachment          0          0          0%          0          0          0          0 
                   dao-testrun          0          0          0%          0          0          0          0 
         dao-testrunattachment          0          0          0%          0          0          0          0 
                      dao-user          0          0          0%          0          0          0          0 
                  dao-wikipage          0          0          0%          0          0          0          0 
        dao-wikipageattachment          0          0          0%          0          0          0          0 
                  dao-workitem          0          0          0%          0          0          0          0 
      derived-linked-revisions          0          0          0%          0          0          0          0 
                  formulas-svg          0          0          0%          0          0          0          0 
                github-commits          0          0          0%          0          0          0          0 
            historical-queries          0          0          0%          0          0          0          0 
      historical-queries-sizes          0          0          0%          0          0          0          0 
        oslc-linked-item-cache          0          0          0%          0          0          0          0 
               plan-statistics          0          0          0%          0          0          0          0 
                   rmd-default          4          5         44%          9          7          0          0 
                   ss-combined          0          0          0%          0          0          0          0 
                    ss-context          0          0          0%          0          0          0          0 
                     wiki-page          2          8         20%         10          9          1          0 
              wiki-page-exists          0          0          0%          0          9          1          0 

 [Sat Aug 02 13:34:41 CST 2025]
2025-08-02 13:34:41,434 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to RUNNING
2025-08-02 13:34:41,434 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "DB History Creator" is /opt/polarion/data/workspace/polarion-data/jobs/20250802-1334_0
2025-08-02 13:34:41,435 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" runs as user "polarion"
2025-08-02 13:34:41,436 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to ACTIVATING
2025-08-02 13:34:41,437 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to WAITING
2025-08-02 13:34:41,439 [main] INFO  com.polarion.platform.monitoring - Finished with actions from stage POSTBOOT: {OK=1}
2025-08-02 13:34:41,441 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to RUNNING
2025-08-02 13:34:41,442 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 5.63 s. ]
2025-08-02 13:34:41,442 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.29 s [86% info (158x)] (170x)
2025-08-02 13:34:41,443 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:34:41,443 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11.8 s. ]
2025-08-02 13:34:41,443 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:34:41,446 [PreLoadDataService] INFO  com.polarion.psvn.launcher.internal.data.PreLoadDataService - Preloading data ...
2025-08-02 13:34:41,448 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to FINISHED
2025-08-02 13:34:41,449 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - Status of job "Attachment Indexer" is OK
2025-08-02 13:34:41,466 [Thread-36] INFO  com.polarion.core.util.process.JavaRunner - Executing /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/bin/java
  -- args [-jar, /opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar, --server.port=40608, --jwksUrl=http://localhost/polarion/.well-known/jwks.json]
  -- env null
  -- dir /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess626628337533572609.tmp
2025-08-02 13:34:41,511 [Notification-Worker-6 | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem
2025-08-02 13:34:41,526 [Notification-Worker-6 | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem
2025-08-02 13:34:41,635 [Activities-Bulk-Publisher] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Activities
2025-08-02 13:34:41,636 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661a519cca044_0_661a519cca044_0_: finished. Total: 0.187 s, CPU [user: 0.116 s, system: 0.0138 s], Allocated memory: 16.1 MB, resolve: 0.054 s [96% User (2x)] (4x), Lucene: 0.019 s [100% search (1x)] (1x), ObjectMaps: 0.0111 s [60% getPrimaryObjectLocation (1x), 22% getPrimaryObjectProperty (1x)] (6x), svn: 0.0103 s [50% getLatestRevision (2x), 35% testConnection (1x)] (5x)
2025-08-02 13:34:41,647 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-08-02 13:34:41,649 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-08-02 13:34:41,652 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-08-02 13:34:41,655 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-08-02 13:34:41,904 [Thread-43] INFO  class com.polarion.alm.server.util.FormulaServerStartup - Formula renderer says: Server started on 127.0.0.1:34568
2025-08-02 13:34:42,081 [Thread-40] INFO  NotificationService - 
2025-08-02 13:34:42,082 [Thread-40] INFO  NotificationService -   .   ____          _            __ _ _
2025-08-02 13:34:42,082 [Thread-40] INFO  NotificationService -  /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
2025-08-02 13:34:42,082 [Thread-40] INFO  NotificationService - ( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
2025-08-02 13:34:42,082 [Thread-40] INFO  NotificationService -  \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
2025-08-02 13:34:42,082 [Thread-40] INFO  NotificationService -   '  |____| .__|_| |_|_| |_\__, | / / / /
2025-08-02 13:34:42,082 [Thread-40] INFO  NotificationService -  =========|_|==============|___/=/_/_/_/
2025-08-02 13:34:42,083 [Thread-40] INFO  NotificationService -  :: Spring Boot ::                (v2.6.6)
2025-08-02 13:34:42,083 [Thread-40] INFO  NotificationService - 
2025-08-02 13:34:42,163 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Starting Application using Java 11.0.27 on zhangwentiandeMac-mini-2.local with PID 56940 (/opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar started by zhangwentian in /private/var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess626628337533572609.tmp)
2025-08-02 13:34:42,164 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - No active profile set, falling back to 1 default profile: "default"
2025-08-02 13:34:42,228 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 25, for prototypes: WorkItem; with days range: 180d, took  [ TIME 0.701 s. ] 
2025-08-02 13:34:42,229 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.718 s, CPU [user: 0.0059 s, system: 0.00151 s], Allocated memory: 356.7 kB, transactions: 1
2025-08-02 13:34:42,230 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, notification worker: 0.2 s [97% RevisionActivityCreator (2x)] (6x), resolve: 0.0691 s [93% User (3x)] (6x), Lucene: 0.0368 s [52% search (1x), 34% add (1x)] (3x), Incremental Baseline: 0.0325 s [100% WorkItem (24x)] (24x), persistence listener: 0.0146 s [86% indexRefreshPersistenceListener (1x)] (7x), svn: 0.0141 s [63% getLatestRevision (3x), 26% testConnection (1x)] (6x), ObjectMaps: 0.0138 s [68% getPrimaryObjectLocation (2x), 18% getPrimaryObjectProperty (1x)] (7x)
2025-08-02 13:34:42,230 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.788 s, CPU [user: 0.15 s, system: 0.0247 s], Allocated memory: 18.8 MB, transactions: 25, svn: 0.645 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0427 s [77% buildBaselineSnapshots (1x), 23% buildBaseline (25x)] (26x)
2025-08-02 13:34:42,232 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to FINISHED
2025-08-02 13:34:42,232 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - Status of job "DB History Creator" is OK
2025-08-02 13:34:42,573 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 25, for prototypes: WorkItem; with days range: 180d, took  [ TIME 0.345 s. ] 
2025-08-02 13:34:42,591 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a519cca043_0_661a519cca043_0_: finished. Total: 1.14 s, CPU [user: 0.344 s, system: 0.0904 s], Allocated memory: 51.6 MB, svn: 0.654 s [51% getDatedRevision (181x), 28% getDir2 content (25x), 18% getFile content (119x)] (328x), resolve: 0.41 s [100% Category (117x)] (117x), ObjectMaps: 0.151 s [41% getPrimaryObjectProperty (117x), 34% getPrimaryObjectLocation (117x), 25% getLastPromoted (117x)] (473x)
2025-08-02 13:34:42,595 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-08-02 13:34:42,597 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-08-02 13:34:42,622 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-08-02 13:34:42,624 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-08-02 13:34:42,629 [Thread-40] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 40608 (http)
2025-08-02 13:34:42,638 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-40608"]
2025-08-02 13:34:42,638 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-02 13:34:42,638 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-02 13:34:42,677 [Thread-40] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-02 13:34:42,677 [Thread-40] INFO  NotificationService - [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 472 ms
2025-08-02 13:34:42,679 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-08-02 13:34:42,681 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-08-02 13:34:42,693 [Thread-40] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing AtmosphereFramework
2025-08-02 13:34:42,858 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-40608"]
2025-08-02 13:34:42,895 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using org.atmosphere.cpr.DefaultAnnotationProcessor for processing annotation
2025-08-02 13:34:42,896 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.DefaultAnnotationProcessor - AnnotationProcessor class org.atmosphere.cpr.DefaultAnnotationProcessor$BytecodeBasedAnnotationProcessor being used
2025-08-02 13:34:42,909 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AnnotationHandler - Found Annotation in class com.siemens.polarion.service.notification.NotificationService being scanned: interface org.atmosphere.config.service.ManagedService
2025-08-02 13:34:42,912 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.AtmosphereResourceLifecycleInterceptor
2025-08-02 13:34:42,912 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.client.TrackMessageSizeInterceptor
2025-08-02 13:34:42,913 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.SuspendTrackerInterceptor
2025-08-02 13:34:42,913 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.config.managed.ManagedServiceInterceptor
2025-08-02 13:34:42,918 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class com.siemens.polarion.service.notification.JwtVerificationInterceptor
2025-08-02 13:34:42,921 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.util.ForkJoinPool - Using ForkJoinPool  java.util.concurrent.ForkJoinPool. Set the org.atmosphere.cpr.broadcaster.maxAsyncWriteThreads to -1 to fully use its power.
2025-08-02 13:34:42,924 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testtype) created
2025-08-02 13:34:42,924 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler mapped to context-path /notification and Broadcaster Class org.atmosphere.cpr.DefaultBroadcaster
2025-08-02 13:34:42,924 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor [Atmosphere LifeCycle,  Track Message Size Interceptor using |, UUID Tracking Interceptor, @ManagedService Interceptor, @Service Event Listeners, com.siemens.polarion.service.notification.JwtVerificationInterceptor] mapped to AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler
2025-08-02 13:34:42,931 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (subtype) created
2025-08-02 13:34:42,932 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Auto detecting WebSocketHandler in /WEB-INF/classes/
2025-08-02 13:34:42,934 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed WebSocketProtocol org.atmosphere.websocket.protocol.SimpleHttpProtocol 
2025-08-02 13:34:42,935 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.container.JSR356AsyncSupport - JSR 356 Mapping path /notification
2025-08-02 13:34:42,937 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a519e1cc4a_0_661a519e1cc4a_0_: finished. Total: 0.133 s, CPU [user: 0.0472 s, system: 0.00419 s], Allocated memory: 19.8 MB, svn: 0.1 s [68% getDir2 content (17x), 32% getFile content (44x)] (62x), RepositoryConfigService: 0.0503 s [98% getReadConfiguration (170x)] (192x)
2025-08-02 13:34:42,939 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installing Default AtmosphereInterceptors
2025-08-02 13:34:42,939 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CorsInterceptor : CORS Interceptor Support
2025-08-02 13:34:42,939 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CacheHeadersInterceptor : Default Response's Headers Interceptor
2025-08-02 13:34:42,940 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.PaddingAtmosphereInterceptor : Browser Padding Interceptor Support
2025-08-02 13:34:42,940 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.AndroidAtmosphereInterceptor : Android Interceptor Support
2025-08-02 13:34:42,940 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.HeartbeatInterceptor : Heartbeat Interceptor Support
2025-08-02 13:34:42,941 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.SSEAtmosphereInterceptor : SSE Interceptor Support
2025-08-02 13:34:42,941 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JSONPAtmosphereInterceptor : JSONP Interceptor Support
2025-08-02 13:34:42,941 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JavaScriptProtocol : Atmosphere JavaScript Protocol
2025-08-02 13:34:42,941 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor : org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor
2025-08-02 13:34:42,941 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.OnDisconnectInterceptor : Browser disconnection detection
2025-08-02 13:34:42,941 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.IdleResourceInterceptor : org.atmosphere.interceptor.IdleResourceInterceptor
2025-08-02 13:34:42,941 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Set org.atmosphere.cpr.AtmosphereInterceptor.disableDefaults to disable them.
2025-08-02 13:34:42,941 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor CORS Interceptor Support with priority FIRST_BEFORE_DEFAULT 
2025-08-02 13:34:42,943 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Default Response's Headers Interceptor with priority AFTER_DEFAULT 
2025-08-02 13:34:42,943 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser Padding Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 13:34:42,943 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Android Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 13:34:42,943 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.interceptor.HeartbeatInterceptor - HeartbeatInterceptor configured with padding value 'X', client frequency 30 seconds and server frequency 120 seconds
2025-08-02 13:34:42,943 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Heartbeat Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 13:34:42,943 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor SSE Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 13:34:42,943 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor JSONP Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 13:34:42,943 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Atmosphere JavaScript Protocol with priority AFTER_DEFAULT 
2025-08-02 13:34:42,943 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor with priority AFTER_DEFAULT 
2025-08-02 13:34:42,943 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser disconnection detection with priority AFTER_DEFAULT 
2025-08-02 13:34:42,943 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.IdleResourceInterceptor with priority BEFORE_DEFAULT 
2025-08-02 13:34:42,944 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using EndpointMapper class org.atmosphere.util.DefaultEndpointMapper
2025-08-02 13:34:42,944 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterCache: org.atmosphere.cache.UUIDBroadcasterCache
2025-08-02 13:34:42,944 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Default Broadcaster Class: org.atmosphere.cpr.DefaultBroadcaster
2025-08-02 13:34:42,944 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Shared List Resources: false
2025-08-02 13:34:42,944 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Polling Wait Time 100
2025-08-02 13:34:42,944 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Shared ExecutorService supported: true
2025-08-02 13:34:42,944 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Messaging ExecutorService Pool Size unavailable - Not instance of ThreadPoolExecutor
2025-08-02 13:34:42,944 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Async I/O Thread Pool Size: 200
2025-08-02 13:34:42,945 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterFactory: org.atmosphere.cpr.DefaultBroadcasterFactory
2025-08-02 13:34:42,945 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using AtmosphereResurceFactory: org.atmosphere.cpr.DefaultAtmosphereResourceFactory
2025-08-02 13:34:42,945 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using WebSocketProcessor: org.atmosphere.websocket.DefaultWebSocketProcessor
2025-08-02 13:34:42,947 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Invoke AtmosphereInterceptor on WebSocket message true
2025-08-02 13:34:42,947 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - HttpSession supported: false
2025-08-02 13:34:42,947 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using Spring Web ObjectFactory for dependency injection and object creation
2025-08-02 13:34:42,947 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using async support: org.atmosphere.container.JSR356AsyncSupport running under container: Apache Tomcat/9.0.60 using javax.servlet/3.0 and jsr356/WebSocket API
2025-08-02 13:34:42,948 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere Framework 2.6.4 started.
2025-08-02 13:34:42,948 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 
2025-08-02 13:34:42,948 [Thread-40] INFO  NotificationService - 
2025-08-02 13:34:42,948 [Thread-40] INFO  NotificationService - 	For Atmosphere Framework Commercial Support, visit 
2025-08-02 13:34:42,948 [Thread-40] INFO  NotificationService - 	http://www.async-io.org/ or send an <NAME_EMAIL>
2025-08-02 13:34:42,948 [Thread-40] INFO  NotificationService - 
2025-08-02 13:34:42,950 [Thread-40] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 40608 (http) with context path ''
2025-08-02 13:34:42,957 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Started Application in 1.133 seconds (JVM running for 1.475)
2025-08-02 13:34:43,167 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (YesNo) created
2025-08-02 13:34:43,172 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (software_VerificationMethod) created
2025-08-02 13:34:43,176 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checklist) created
2025-08-02 13:34:43,180 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonreqproperty) created
2025-08-02 13:34:43,184 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectoriented) created
2025-08-02 13:34:43,184 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submodule) created
2025-08-02 13:34:43,185 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (yesno) created
2025-08-02 13:34:43,186 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (cICategory) created
2025-08-02 13:34:43,186 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (wpFormat) created
2025-08-02 13:34:43,188 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (trigger) created
2025-08-02 13:34:43,192 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ASILLevel) created
2025-08-02 13:34:43,192 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CSRelated) created
2025-08-02 13:34:43,196 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_Module) created
2025-08-02 13:34:43,207 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (规格对象类型) created
2025-08-02 13:34:43,209 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (jenkins_job) created
2025-08-02 13:34:43,209 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (truefalse) created
2025-08-02 13:34:43,210 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (takeOnGroups) created
2025-08-02 13:34:43,215 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasetype) created
2025-08-02 13:34:43,219 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processGroup) created
2025-08-02 13:34:43,222 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeManagement) created
2025-08-02 13:34:43,228 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (seriousness) created
2025-08-02 13:34:43,231 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softReqClass) created
2025-08-02 13:34:43,235 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWDetailDesign) created
2025-08-02 13:34:43,237 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PhaseChecklists) created
2025-08-02 13:34:43,239 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PassNotpass) created
2025-08-02 13:34:43,242 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineType) created
2025-08-02 13:34:43,243 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (boolYesOrNo) created
2025-08-02 13:34:43,246 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testlevel) created
2025-08-02 13:34:43,248 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (source) created
2025-08-02 13:34:43,251 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectType) created
2025-08-02 13:34:43,253 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atppblversion) created
2025-08-02 13:34:43,256 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (aSIL) created
2025-08-02 13:34:43,259 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (EE) created
2025-08-02 13:34:43,261 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueType) created
2025-08-02 13:34:43,264 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SYS_reqClassification) created
2025-08-02 13:34:43,268 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (oem_2Status) created
2025-08-02 13:34:43,271 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (class) created
2025-08-02 13:34:43,273 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (promotionState) created
2025-08-02 13:34:43,276 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (git_project) created
2025-08-02 13:34:43,282 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (storageType) created
2025-08-02 13:34:43,288 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueproperty) created
2025-08-02 13:34:43,291 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonissueclass) created
2025-08-02 13:34:43,294 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (AgreeDisagree) created
2025-08-02 13:34:43,297 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SA_Category) created
2025-08-02 13:34:43,301 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relevance) created
2025-08-02 13:34:43,304 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (implementationPhase) created
2025-08-02 13:34:43,308 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplier_2Status) created
2025-08-02 13:34:43,317 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Testtype) created
2025-08-02 13:34:43,320 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (conf_baselineTime) created
2025-08-02 13:34:43,328 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (levelneed) created
2025-08-02 13:34:43,331 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (finalresult) created
2025-08-02 13:34:43,334 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testability) created
2025-08-02 13:34:43,339 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solution) created
2025-08-02 13:34:43,344 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Responsible) created
2025-08-02 13:34:43,387 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationstatus) created
2025-08-02 13:34:43,417 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsiassigngroup) created
2025-08-02 13:34:43,426 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqCategory) created
2025-08-02 13:34:43,432 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineName) created
2025-08-02 13:34:43,442 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskType) created
2025-08-02 13:34:43,465 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeReason) created
2025-08-02 13:34:43,473 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectmodule) created
2025-08-02 13:34:43,487 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseOutputMethod) created
2025-08-02 13:34:43,495 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SoftwareFeature) created
2025-08-02 13:34:43,544 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ResponsibleGroup) created
2025-08-02 13:34:43,568 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsifunctionmodule) created
2025-08-02 13:34:43,574 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (FwReqSource) created
2025-08-02 13:34:43,581 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (occurPhase) created
2025-08-02 13:34:43,587 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (compiletask) created
2025-08-02 13:34:43,592 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBVerificationMethod) created
2025-08-02 13:34:43,597 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (functionmodule) created
2025-08-02 13:34:43,602 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (variant) created
2025-08-02 13:34:43,604 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Fusatype) created
2025-08-02 13:34:43,608 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareversion) created
2025-08-02 13:34:43,612 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (appversion) created
2025-08-02 13:34:43,614 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casefirstmodule) created
2025-08-02 13:34:43,617 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditType) created
2025-08-02 13:34:43,621 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Samplestage) created
2025-08-02 13:34:43,624 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casesecondmodule) created
2025-08-02 13:34:43,628 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issue_source) created
2025-08-02 13:34:43,632 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ifNeedRegressionTesting) created
2025-08-02 13:34:43,636 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atpsfsversion) created
2025-08-02 13:34:43,639 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CustomerAllocation) created
2025-08-02 13:34:43,650 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issuesubclass) created
2025-08-02 13:34:43,655 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_importance) created
2025-08-02 13:34:43,659 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reviewMethod) created
2025-08-02 13:34:43,665 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_findType) created
2025-08-02 13:34:43,668 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (editType) created
2025-08-02 13:34:43,674 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testingobjects) created
2025-08-02 13:34:43,677 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcaselevel) created
2025-08-02 13:34:43,681 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplierproblem) created
2025-08-02 13:34:43,685 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqattribute) created
2025-08-02 13:34:43,689 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (fsigroup) created
2025-08-02 13:34:43,694 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_reqsource) created
2025-08-02 13:34:43,699 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (preset) created
2025-08-02 13:34:43,704 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Mechverificationmethod) created
2025-08-02 13:34:43,708 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CPMToTPM) created
2025-08-02 13:34:43,713 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBType) created
2025-08-02 13:34:43,719 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasesign) created
2025-08-02 13:34:43,725 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationphase) created
2025-08-02 13:34:43,738 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processArea) created
2025-08-02 13:34:43,744 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (artifactType) created
2025-08-02 13:34:43,757 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Classification) created
2025-08-02 13:34:43,765 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationmethod) created
2025-08-02 13:34:43,770 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeType) created
2025-08-02 13:34:43,773 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareAndSoftwareSubType) created
2025-08-02 13:34:43,778 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWIntegrationVerificationMethod) created
2025-08-02 13:34:43,783 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (category) created
2025-08-02 13:34:43,826 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBCategory) created
2025-08-02 13:34:43,839 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softreqclass) created
2025-08-02 13:34:43,845 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestMethod) created
2025-08-02 13:34:43,850 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reType) created
2025-08-02 13:34:43,855 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (VerificationCriteria) created
2025-08-02 13:34:43,861 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLinechecklist) created
2025-08-02 13:34:43,866 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Category) created
2025-08-02 13:34:43,873 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWUnitTestDerivingMethods) created
2025-08-02 13:34:43,878 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (firmware_Category) created
2025-08-02 13:34:43,884 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testMethod) created
2025-08-02 13:34:43,898 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QAPorcessAreas) created
2025-08-02 13:34:43,905 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (findSource) created
2025-08-02 13:34:43,912 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a519e3e44b_0_661a519e3e44b_0_: finished. Total: 0.975 s, CPU [user: 0.426 s, system: 0.0272 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.755 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.435 s [71% getFile content (412x), 29% getDir2 content (21x)] (434x), GC: 0.063 s [100% G1 Young Generation (4x)] (4x)
2025-08-02 13:34:44,064 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (tshirt-sizes) created
2025-08-02 13:34:44,067 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqtype) created
2025-08-02 13:34:44,069 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a519f3244c_0_661a519f3244c_0_: finished. Total: 0.156 s, CPU [user: 0.0334 s, system: 0.00478 s], Allocated memory: 18.2 MB, svn: 0.141 s [81% getDir2 content (18x)] (48x), RepositoryConfigService: 0.037 s [99% getReadConfiguration (124x)] (148x)
2025-08-02 13:34:44,304 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Feasibility) created
2025-08-02 13:34:44,306 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (locaMod) created
2025-08-02 13:34:44,308 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseType) created
2025-08-02 13:34:44,309 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ControlLevel) created
2025-08-02 13:34:44,311 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (NCitemSev) created
2025-08-02 13:34:44,312 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (signType) created
2025-08-02 13:34:44,315 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (WBSCategory) created
2025-08-02 13:34:44,318 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseEnv) created
2025-08-02 13:34:44,319 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verifiability) created
2025-08-02 13:34:44,327 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ProjectUser) created
2025-08-02 13:34:44,329 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (standardReq) created
2025-08-02 13:34:44,330 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (statusa) created
2025-08-02 13:34:44,331 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@WorkItems[type:configurationitemversion]) created
2025-08-02 13:34:44,332 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CIRevisionStatus) created
2025-08-02 13:34:44,335 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (dogTimeout) created
2025-08-02 13:34:44,336 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQCategory) created
2025-08-02 13:34:44,339 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proStage) created
2025-08-02 13:34:44,341 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (BaselineType) created
2025-08-02 13:34:44,343 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (custConfStat) created
2025-08-02 13:34:44,344 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sofReqVer) created
2025-08-02 13:34:44,347 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Source) created
2025-08-02 13:34:44,350 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scCategory) created
2025-08-02 13:34:44,351 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseType) created
2025-08-02 13:34:44,353 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solAdv) created
2025-08-02 13:34:44,356 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseMet) created
2025-08-02 13:34:44,360 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMeth) created
2025-08-02 13:34:44,363 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseMe) created
2025-08-02 13:34:44,366 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseEnv) created
2025-08-02 13:34:44,375 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTarget) created
2025-08-02 13:34:44,378 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ReviewForm) created
2025-08-02 13:34:44,379 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseType) created
2025-08-02 13:34:44,383 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@Collection) created
2025-08-02 13:34:44,384 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submissionStage) created
2025-08-02 13:34:44,387 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMet) created
2025-08-02 13:34:44,389 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandType) created
2025-08-02 13:34:44,392 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseMet) created
2025-08-02 13:34:44,394 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskUrgen) created
2025-08-02 13:34:44,396 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solveMethod) created
2025-08-02 13:34:44,398 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (audMethod) created
2025-08-02 13:34:44,401 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (desStat) created
2025-08-02 13:34:44,408 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scType) created
2025-08-02 13:34:44,410 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseType) created
2025-08-02 13:34:44,411 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (releaseType) created
2025-08-02 13:34:44,414 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseEnv) created
2025-08-02 13:34:44,417 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (targetStage) created
2025-08-02 13:34:44,419 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ClassificationType) created
2025-08-02 13:34:44,422 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testItem) created
2025-08-02 13:34:44,425 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (InfoSecurity) created
2025-08-02 13:34:44,428 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Verification) created
2025-08-02 13:34:44,431 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMod) created
2025-08-02 13:34:44,435 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verMethod) created
2025-08-02 13:34:44,437 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (diagramCategory) created
2025-08-02 13:34:44,441 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (assSubsystem) created
2025-08-02 13:34:44,444 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (OccurrenceProbability) created
2025-08-02 13:34:44,447 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (developmentMethod) created
2025-08-02 13:34:44,449 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (portType) created
2025-08-02 13:34:44,452 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checkType) created
2025-08-02 13:34:44,454 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandStatus) created
2025-08-02 13:34:44,458 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (important) created
2025-08-02 13:34:44,460 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMec) created
2025-08-02 13:34:44,463 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseTy) created
2025-08-02 13:34:44,465 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (recentPre) created
2025-08-02 13:34:44,467 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseDesignMethod) created
2025-08-02 13:34:44,470 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testCasePri) created
2025-08-02 13:34:44,476 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relObj) created
2025-08-02 13:34:44,480 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proSer) created
2025-08-02 13:34:44,484 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestProblemType) created
2025-08-02 13:34:44,487 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (chipName) created
2025-08-02 13:34:44,490 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTiming) created
2025-08-02 13:34:44,502 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a519f6a84e_0_661a519f6a84e_0_: finished. Total: 0.363 s, CPU [user: 0.15 s, system: 0.0105 s], Allocated memory: 388.9 MB, RepositoryConfigService: 0.245 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.219 s [56% getFile content (185x), 44% getDir2 content (21x)] (207x)
2025-08-02 13:34:44,572 [PreLoadDataService | u:p] ERROR com.polarion.subterra.base.data.model.CustomField - Unknown custom field type 'enum' - using 'string' - for field with id 'taskType' for 'WorkItem task /default/WBS'
2025-08-02 13:34:44,580 [PreLoadDataService] INFO  com.polarion.psvn.launcher.internal.data.PreLoadDataService - Preloading data FINISHED took  [ TIME 3.13 s. ]
2025-08-02 13:34:44,580 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.13 s, CPU [user: 1.14 s, system: 0.157 s], Allocated memory: 1.7 GB, transactions: 11, svn: 1.77 s [40% getDir2 content (133x), 38% getFile content (865x), 19% getDatedRevision (181x)] (1224x), RepositoryConfigService: 1.19 s [96% getReadConfiguration (12165x)] (12859x), resolve: 0.49 s [84% Category (117x)] (139x), ObjectMaps: 0.183 s [43% getPrimaryObjectProperty (131x), 34% getPrimaryObjectLocation (137x), 23% getLastPromoted (131x)] (536x)
2025-08-02 13:34:44,580 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 61, svn: 2.42 s [40% getDatedRevision (362x), 29% getDir2 content (133x), 28% getFile content (865x)] (1407x), RepositoryConfigService: 1.19 s [96% getReadConfiguration (12165x)] (12859x), resolve: 0.49 s [84% Category (117x)] (140x), ObjectMaps: 0.183 s [43% getPrimaryObjectProperty (131x), 34% getPrimaryObjectLocation (137x), 23% getLastPromoted (131x)] (536x)
2025-08-02 13:34:49,754 [ajp-nio-127.0.0.1-8889-exec-2 | cID:69468a59-7f000001-1cbc3d6e-62f7f4a7] ERROR com.checklist.exception.GlobalExceptionHandler - 未知异常: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: com/fasterxml/jackson/core/util/JacksonFeature
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: com/fasterxml/jackson/core/util/JacksonFeature
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1055) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634) [javax.servlet_4.0.0.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.SecurityCheckFilter.doFilter(SecurityCheckFilter.java:46) [portal-tomcat.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: java.lang.NoClassDefFoundError: com/fasterxml/jackson/core/util/JacksonFeature
	at com.checklist.util.JsonUtil.<clinit>(JsonUtil.java:21) ~[com.fasnote.alm.checklist/:?]
	at com.checklist.repository.BaseJsonFileRepository.findAll(BaseJsonFileRepository.java:311) ~[com.fasnote.alm.checklist/:?]
	at com.checklist.service.ChecklistTemplateService.getAllTemplates(ChecklistTemplateService.java:147) ~[com.fasnote.alm.checklist/:?]
	at com.checklist.controller.ChecklistTemplateController.getAllTemplates(ChecklistTemplateController.java:37) ~[com.fasnote.alm.checklist/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	... 33 more
Caused by: java.lang.ClassNotFoundException: com.fasterxml.jackson.core.util.JacksonFeature cannot be found by com.fasnote.alm.checklist_1.0.0.qualifier
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:508) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at java.lang.ClassLoader.loadClass(ClassLoader.java:527) ~[?:?]
	at com.checklist.util.JsonUtil.<clinit>(JsonUtil.java:21) ~[com.fasnote.alm.checklist/:?]
	at com.checklist.repository.BaseJsonFileRepository.findAll(BaseJsonFileRepository.java:311) ~[com.fasnote.alm.checklist/:?]
	at com.checklist.service.ChecklistTemplateService.getAllTemplates(ChecklistTemplateService.java:147) ~[com.fasnote.alm.checklist/:?]
	at com.checklist.controller.ChecklistTemplateController.getAllTemplates(ChecklistTemplateController.java:37) ~[com.fasnote.alm.checklist/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	... 33 more
2025-08-02 13:34:49,800 [ajp-nio-127.0.0.1-8889-exec-2 | cID:69468a59-7f000001-1cbc3d6e-62f7f4a7] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?_t=1754112887368': Total: 2.41 s, CPU [user: 0.0456 s, system: 0.00747 s], Allocated memory: 1.9 MB, transactions: 0
2025-08-02 13:34:50,765 [ajp-nio-127.0.0.1-8889-exec-3 | cID:69468a59-7f000001-1cbc3d6e-f8768a16] ERROR com.checklist.exception.GlobalExceptionHandler - 未知异常: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: Could not initialize class com.checklist.util.JsonUtil
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: Could not initialize class com.checklist.util.JsonUtil
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1055) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634) [javax.servlet_4.0.0.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.SecurityCheckFilter.doFilter(SecurityCheckFilter.java:46) [portal-tomcat.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: java.lang.NoClassDefFoundError: Could not initialize class com.checklist.util.JsonUtil
	at com.checklist.repository.BaseJsonFileRepository.findAll(BaseJsonFileRepository.java:311) ~[com.fasnote.alm.checklist/:?]
	at com.checklist.repository.BaseJsonFileRepository.findBy(BaseJsonFileRepository.java:428) ~[com.fasnote.alm.checklist/:?]
	at com.checklist.repository.ChecklistTemplateRepository.findByType(ChecklistTemplateRepository.java:58) ~[com.fasnote.alm.checklist/:?]
	at com.checklist.service.ChecklistTemplateService.getTemplatesByType(ChecklistTemplateService.java:162) ~[com.fasnote.alm.checklist/:?]
	at com.checklist.controller.ChecklistTemplateController.getTemplatesByType(ChecklistTemplateController.java:65) ~[com.fasnote.alm.checklist/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) ~[org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) ~[org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	... 33 more
2025-08-02 13:34:50,769 [ajp-nio-127.0.0.1-8889-exec-3 | cID:69468a59-7f000001-1cbc3d6e-f8768a16] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?_t=1754112887368': Total: 3.38 s, CPU [user: 0.019 s, system: 0.00352 s], Allocated memory: 924.8 kB, transactions: 0
2025-08-02 13:34:51,475 [Thread-36] INFO  NotificationService - Notification service was started successfully.
