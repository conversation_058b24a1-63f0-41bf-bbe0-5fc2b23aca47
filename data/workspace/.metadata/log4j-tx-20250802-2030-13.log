2025-08-02 20:30:18,668 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0441 s [59% update (144x), 41% query (12x)] (221x), svn: 0.0134 s [59% getLatestRevision (2x), 28% testConnection (1x)] (4x)
2025-08-02 20:30:18,805 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0422 s [57% getDir2 content (2x), 37% info (3x)] (6x)
2025-08-02 20:30:19,617 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.808 s, CPU [user: 0.067 s, system: 0.097 s], Allocated memory: 7.1 MB, transactions: 0, svn: 0.0764 s [82% log2 (5x)] (7x), ObjectMaps: 0.0551 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-02 20:30:19,617 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.807 s, CPU [user: 0.116 s, system: 0.166 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.103 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0958 s [85% log2 (10x)] (13x)
2025-08-02 20:30:19,617 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.807 s, CPU [user: 0.0793 s, system: 0.0865 s], Allocated memory: 8.8 MB, transactions: 0, svn: 0.0999 s [79% log2 (10x), 14% getLatestRevision (2x)] (13x), ObjectMaps: 0.0579 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-02 20:30:19,617 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.807 s, CPU [user: 0.272 s, system: 0.344 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.129 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-02 20:30:19,617 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.808 s, CPU [user: 0.169 s, system: 0.224 s], Allocated memory: 26.4 MB, transactions: 0, ObjectMaps: 0.0956 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0917 s [32% log2 (5x), 23% log (1x), 23% info (5x), 11% getLatestRevision (2x)] (18x)
2025-08-02 20:30:19,617 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.808 s, CPU [user: 0.244 s, system: 0.281 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.123 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-02 20:30:19,618 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.564 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.407 s [68% log2 (36x), 13% getLatestRevision (9x)] (61x)
2025-08-02 20:30:19,774 [main | u:p] INFO  TXLOGGER - Tx 661ab0be7ec01_0_661ab0be7ec01_0_: finished. Total: 0.129 s, CPU [user: 0.0868 s, system: 0.00547 s], Allocated memory: 21.8 MB
2025-08-02 20:30:19,934 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.276 s [100% getReadConfiguration (48x)] (48x), svn: 0.0865 s [84% info (18x)] (38x)
2025-08-02 20:30:20,470 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.419 s [71% info (94x), 21% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.321 s [100% getReadConfiguration (54x)] (54x)
2025-08-02 20:30:20,723 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.121 s, CPU [user: 0.0402 s, system: 0.0108 s], Allocated memory: 10.2 MB, GC: 0.014 s [100% G1 Young Generation (1x)] (1x)
2025-08-02 20:30:20,750 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.261 s [100% doFinishStartup (1x)] (1x), commit: 0.0658 s [100% Revision (1x)] (1x), Lucene: 0.032 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0178 s [100% objectsToInv (1x)] (1x)
2025-08-02 20:30:25,106 [ajp-nio-127.0.0.1-8889-exec-2 | cID:6ac30ea4-0ad33702-0876a3b3-f784ea10] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?_t=1754137823960': Total: 0.174 s, CPU [user: 0.0446 s, system: 0.00633 s], Allocated memory: 2.7 MB, transactions: 0
2025-08-02 20:30:25,118 [ajp-nio-127.0.0.1-8889-exec-1 | cID:6ac30ea4-0ad33702-0876a3b3-73ac3e82] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?_t=1754137823959': Total: 0.185 s, CPU [user: 0.106 s, system: 0.0111 s], Allocated memory: 5.6 MB, transactions: 0
2025-08-02 20:30:25,944 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.491 s [85% info (158x)] (170x)
2025-08-02 20:30:26,325 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661ab0c4abc45_0_661ab0c4abc45_0_: finished. Total: 0.357 s, CPU [user: 0.153 s, system: 0.02 s], Allocated memory: 16.1 MB, resolve: 0.101 s [97% User (2x)] (4x), Lucene: 0.0323 s [100% search (1x)] (1x), svn: 0.0257 s [56% getLatestRevision (2x), 31% testConnection (1x)] (5x)
2025-08-02 20:30:27,153 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.1 s, CPU [user: 0.00705 s, system: 0.00222 s], Allocated memory: 355.8 kB, transactions: 1
2025-08-02 20:30:27,155 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 30, notification worker: 0.372 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.161 s [96% User (4x)] (7x), Lucene: 0.0496 s [65% search (1x), 22% add (1x)] (3x), Incremental Baseline: 0.0427 s [100% WorkItem (24x)] (24x), svn: 0.0321 s [65% getLatestRevision (3x), 25% testConnection (1x)] (6x), ObjectMaps: 0.0287 s [80% getPrimaryObjectLocation (3x)] (8x), persistence listener: 0.0283 s [72% indexRefreshPersistenceListener (1x), 13% WorkItemActivityCreator (1x)] (7x)
2025-08-02 20:30:27,155 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.1 s, CPU [user: 0.197 s, system: 0.0351 s], Allocated memory: 19.2 MB, transactions: 26, svn: 0.961 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0802 s [68% buildBaselineSnapshots (1x), 32% buildBaseline (25x)] (26x)
2025-08-02 20:30:27,649 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661ab0c4a9040_0_661ab0c4a9040_0_: finished. Total: 1.69 s, CPU [user: 0.437 s, system: 0.115 s], Allocated memory: 51.6 MB, svn: 0.971 s [48% getDatedRevision (181x), 33% getDir2 content (25x)] (328x), resolve: 0.582 s [100% Category (117x)] (117x), ObjectMaps: 0.214 s [42% getPrimaryObjectProperty (117x), 36% getPrimaryObjectLocation (117x), 22% getLastPromoted (117x)] (473x)
2025-08-02 20:30:27,791 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661ab0c657847_0_661ab0c657847_0_: finished. Total: 0.113 s, CPU [user: 0.0399 s, system: 0.00902 s], Allocated memory: 3.8 MB, resolve: 0.0982 s [100% Project (6x)] (6x), svn: 0.0534 s [43% log (3x), 34% info (6x), 23% getFile content (6x)] (16x), ObjectMaps: 0.0404 s [74% getPrimaryObjectProperty (6x), 18% getPrimaryObjectLocation (6x)] (25x)
2025-08-02 20:30:28,026 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661ab0c673c48_0_661ab0c673c48_0_: finished. Total: 0.234 s, CPU [user: 0.0786 s, system: 0.0146 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0871 s [51% getReadUserConfiguration (10x), 49% getReadConfiguration (180x)] (190x), svn: 0.0835 s [54% info (21x), 38% getFile content (16x)] (39x), resolve: 0.0554 s [100% User (9x)] (9x), ObjectMaps: 0.0243 s [70% getPrimaryObjectProperty (8x), 15% getLastPromoted (8x)] (32x)
2025-08-02 20:30:28,213 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661ab0c6c104a_0_661ab0c6c104a_0_: finished. Total: 0.112 s, CPU [user: 0.0362 s, system: 0.00522 s], Allocated memory: 19.8 MB, svn: 0.0837 s [75% getDir2 content (17x), 25% getFile content (44x)] (62x), RepositoryConfigService: 0.034 s [98% getReadConfiguration (170x)] (192x)
2025-08-02 20:30:29,011 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661ab0c6dd44b_0_661ab0c6dd44b_0_: finished. Total: 0.798 s, CPU [user: 0.38 s, system: 0.0296 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.621 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.413 s [71% getFile content (412x), 29% getDir2 content (21x)] (434x)
2025-08-02 20:30:29,140 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661ab0c7a4c4c_0_661ab0c7a4c4c_0_: finished. Total: 0.128 s, CPU [user: 0.029 s, system: 0.00372 s], Allocated memory: 17.9 MB, svn: 0.113 s [85% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0267 s [97% getReadConfiguration (124x)] (148x)
2025-08-02 20:30:29,604 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661ab0c7cf44e_0_661ab0c7cf44e_0_: finished. Total: 0.423 s, CPU [user: 0.164 s, system: 0.0139 s], Allocated memory: 384.9 MB, RepositoryConfigService: 0.276 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.268 s [56% getFile content (185x), 44% getDir2 content (21x)] (207x)
2025-08-02 20:30:29,710 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661ab0c83904f_0_661ab0c83904f_0_: finished. Total: 0.105 s, CPU [user: 0.0285 s, system: 0.0035 s], Allocated memory: 13.1 MB, svn: 0.0884 s [67% getDir2 content (18x), 33% getFile content (33x)] (52x), RepositoryConfigService: 0.0421 s [98% getReadConfiguration (128x)] (150x)
2025-08-02 20:30:29,711 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.76 s, CPU [user: 1.26 s, system: 0.202 s], Allocated memory: 1.6 GB, transactions: 11, svn: 2.16 s [38% getDir2 content (133x), 36% getFile content (865x), 22% getDatedRevision (181x)] (1224x), RepositoryConfigService: 1.16 s [93% getReadConfiguration (12165x)] (12859x), resolve: 0.737 s [79% Category (117x), 13% Project (7x)] (139x), ObjectMaps: 0.279 s [49% getPrimaryObjectProperty (131x), 31% getPrimaryObjectLocation (137x)] (536x)
2025-08-02 20:30:29,711 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 62, svn: 3.12 s [45% getDatedRevision (362x), 26% getDir2 content (133x), 25% getFile content (865x)] (1407x), RepositoryConfigService: 1.16 s [93% getReadConfiguration (12165x)] (12859x), resolve: 0.738 s [79% Category (117x), 13% Project (7x)] (140x), ObjectMaps: 0.279 s [49% getPrimaryObjectProperty (131x), 31% getPrimaryObjectLocation (137x)] (536x)
2025-08-02 20:30:33,592 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.017529296875
2025-08-02 20:31:10,298 [ajp-nio-127.0.0.1-8889-exec-6 | cID:6ac3bf51-0ad33702-0876a3b3-f2942341] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/template_6c4b41a2ccd946f0b769317e89e5c5ca': Total: 0.135 s, CPU [user: 0.0308 s, system: 0.0507 s], Allocated memory: 1.3 MB, transactions: 0
