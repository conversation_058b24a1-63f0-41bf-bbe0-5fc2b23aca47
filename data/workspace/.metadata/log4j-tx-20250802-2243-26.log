2025-08-02 22:43:35,726 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.205 s [78% update (144x), 22% query (12x)] (221x), svn: 0.0402 s [58% getLatestRevision (2x), 31% testConnection (1x)] (4x)
2025-08-02 22:43:35,925 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0473 s [58% getDir2 content (2x), 35% info (3x)] (6x)
2025-08-02 22:43:36,647 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.28662109375
2025-08-02 22:43:36,902 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.971 s, CPU [user: 0.0679 s, system: 0.0662 s], Allocated memory: 7.4 MB, transactions: 0, svn: 0.069 s [79% log2 (5x), 11% testConnection (1x)] (7x), ObjectMaps: 0.0548 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-02 22:43:36,902 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.971 s, CPU [user: 0.277 s, system: 0.278 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.27 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-02 22:43:36,902 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.971 s, CPU [user: 0.0865 s, system: 0.0691 s], Allocated memory: 8.6 MB, transactions: 0, svn: 0.134 s [79% log2 (10x), 13% getLatestRevision (2x)] (13x)
2025-08-02 22:43:36,902 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.971 s, CPU [user: 0.312 s, system: 0.339 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.149 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-02 22:43:36,902 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.971 s, CPU [user: 0.144 s, system: 0.137 s], Allocated memory: 16.9 MB, transactions: 0, svn: 0.179 s [41% log2 (10x), 23% log (1x), 15% info (5x), 12% getLatestRevision (3x)] (24x), ObjectMaps: 0.0992 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-02 22:43:36,902 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.971 s, CPU [user: 0.153 s, system: 0.196 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.153 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0504 s [65% log2 (5x), 18% getLatestRevision (1x)] (7x)
2025-08-02 22:43:36,903 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.772 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.488 s [61% log2 (36x), 14% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-08-02 22:43:37,248 [main | u:p] INFO  TXLOGGER - Tx 661acf4077001_0_661acf4077001_0_: finished. Total: 0.193 s, CPU [user: 0.126 s, system: 0.0144 s], Allocated memory: 21.8 MB
2025-08-02 22:43:37,500 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.423 s [100% getReadConfiguration (48x)] (48x), svn: 0.122 s [81% info (18x)] (38x)
2025-08-02 22:43:38,319 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.607 s [70% info (94x), 22% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.449 s [100% getReadConfiguration (54x)] (54x)
2025-08-02 22:43:38,800 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [User]: finished. Total: 0.178 s, CPU [user: 0.0123 s, system: 0.00308 s], Allocated memory: 894.5 kB
2025-08-02 22:43:38,801 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [Attachment]: finished. Total: 0.18 s, CPU [user: 0.0417 s, system: 0.00874 s], Allocated memory: 2.5 MB
2025-08-02 22:43:38,809 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.188 s, CPU [user: 0.0417 s, system: 0.00928 s], Allocated memory: 2.4 MB
2025-08-02 22:43:38,810 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.19 s, CPU [user: 0.0281 s, system: 0.00617 s], Allocated memory: 1.9 MB
2025-08-02 22:43:38,878 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.256 s, CPU [user: 0.044 s, system: 0.00939 s], Allocated memory: 8.6 MB
2025-08-02 22:43:38,919 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.57 s [100% doFinishStartup (1x)] (1x), Lucene: 0.11 s [100% refresh (1x)] (1x), commit: 0.0904 s [100% Revision (1x)] (1x), derivedLinkedRevisionsContributor: 0.0315 s [100% objectsToInv (1x)] (1x)
2025-08-02 22:43:43,984 [ajp-nio-127.0.0.1-8889-exec-1 | cID:6b3d1c2d-0ad33702-58c74364-54efeec5] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?type=%E4%BB%A3%E7%A0%81-review&_t=1754145820146': Total: 0.194 s, CPU [user: 0.137 s, system: 0.0136 s], Allocated memory: 6.5 MB, transactions: 0
2025-08-02 22:43:44,162 [ajp-nio-127.0.0.1-8889-exec-2 | cID:6b3d1d20-0ad33702-58c74364-757d6c0e] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/reviews': Total: 0.128 s, CPU [user: 0.0526 s, system: 0.00644 s], Allocated memory: 1.8 MB, transactions: 0
2025-08-02 22:43:44,446 [main | u:p | u:p] INFO  TXLOGGER - Tx 661acf476d03f_0_661acf476d03f_0_: finished. Total: 0.265 s, CPU [user: 0.129 s, system: 0.0176 s], Allocated memory: 7.5 MB, GlobalHandler: 0.0309 s [90% applyTxChanges (1x)] (4x), resolve: 0.0162 s [100% ProjectGroup (3x)] (3x)
2025-08-02 22:43:44,496 [ajp-nio-127.0.0.1-8889-exec-4 | cID:6b3d1e61-0ad33702-58c74364-5760d8c3] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/template-version/review-versions': Total: 0.139 s, CPU [user: 0.0593 s, system: 0.0108 s], Allocated memory: 1.8 MB, transactions: 0
2025-08-02 22:43:45,832 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 1.14 s [87% info (158x)] (171x)
2025-08-02 22:43:46,526 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661acf490e845_0_661acf490e845_0_: finished. Total: 0.676 s, CPU [user: 0.22 s, system: 0.0298 s], Allocated memory: 16.1 MB, resolve: 0.247 s [98% User (2x)] (4x), Lucene: 0.0695 s [100% search (1x)] (1x), ObjectMaps: 0.05 s [72% getPrimaryObjectLocation (1x), 14% getPrimaryObjectProperty (1x)] (6x), svn: 0.0373 s [67% getLatestRevision (2x), 21% testConnection (1x)] (5x)
2025-08-02 22:43:46,648 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.359716796875
2025-08-02 22:43:47,616 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.66 s, CPU [user: 0.00927 s, system: 0.00305 s], Allocated memory: 355.9 kB, transactions: 1
2025-08-02 22:43:47,620 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, notification worker: 0.703 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.297 s [97% User (4x)] (7x), Lucene: 0.157 s [50% add (2x), 44% search (1x)] (4x), Incremental Baseline: 0.0743 s [100% WorkItem (24x)] (24x), ObjectMaps: 0.0665 s [79% getPrimaryObjectLocation (3x), 10% getPrimaryObjectProperty (1x)] (8x), persistence listener: 0.0497 s [87% indexRefreshPersistenceListener (1x)] (7x), svn: 0.0373 s [67% getLatestRevision (2x), 21% testConnection (1x)] (5x)
2025-08-02 22:43:47,622 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.67 s, CPU [user: 0.249 s, system: 0.0442 s], Allocated memory: 18.8 MB, transactions: 25, svn: 1.48 s [99% getDatedRevision (181x)] (183x), Lucene: 0.105 s [74% buildBaselineSnapshots (1x), 26% buildBaseline (25x)] (26x)
2025-08-02 22:43:48,858 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661acf490d841_0_661acf490d841_0_: finished. Total: 3.01 s, CPU [user: 0.712 s, system: 0.179 s], Allocated memory: 51.6 MB, svn: 1.82 s [62% getDatedRevision (181x), 23% getDir2 content (25x)] (328x), resolve: 1.01 s [100% Category (117x)] (117x), ObjectMaps: 0.42 s [41% getPrimaryObjectLocation (117x), 36% getPrimaryObjectProperty (117x), 23% getLastPromoted (117x)] (473x)
2025-08-02 22:43:49,109 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661acf4c11c47_0_661acf4c11c47_0_: finished. Total: 0.174 s, CPU [user: 0.0583 s, system: 0.0158 s], Allocated memory: 3.8 MB, resolve: 0.132 s [100% Project (6x)] (6x), svn: 0.064 s [38% log (3x), 34% info (6x), 28% getFile content (6x)] (16x), ObjectMaps: 0.0485 s [73% getPrimaryObjectProperty (6x), 16% getPrimaryObjectLocation (6x)] (25x), GlobalHandler: 0.025 s [98% applyTxChanges (1x)] (7x)
2025-08-02 22:43:49,490 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661acf4c3ec48_0_661acf4c3ec48_0_: finished. Total: 0.375 s, CPU [user: 0.115 s, system: 0.021 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.24 s [75% getReadConfiguration (180x), 25% getReadUserConfiguration (10x)] (190x), svn: 0.125 s [61% info (21x), 31% getFile content (16x)] (39x), GC: 0.096 s [100% G1 Young Generation (1x)] (1x), resolve: 0.0722 s [100% User (9x)] (9x), ObjectMaps: 0.0295 s [60% getPrimaryObjectProperty (8x), 23% getPrimaryObjectLocation (8x)] (32x)
2025-08-02 22:43:49,714 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661acf4c9c849_0_661acf4c9c849_0_: finished. Total: 0.223 s, CPU [user: 0.0619 s, system: 0.00549 s], Allocated memory: 4.0 MB, RepositoryConfigService: 0.162 s [98% getReadConfiguration (54x)] (77x), svn: 0.0925 s [100% getFile content (13x)] (14x)
2025-08-02 22:43:50,244 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661acf4cd484a_0_661acf4cd484a_0_: finished. Total: 0.529 s, CPU [user: 0.157 s, system: 0.0217 s], Allocated memory: 19.8 MB, svn: 0.339 s [62% getDir2 content (17x), 38% getFile content (44x)] (62x), RepositoryConfigService: 0.258 s [99% getReadConfiguration (170x)] (192x)
2025-08-02 22:43:52,020 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661acf4d5944b_0_661acf4d5944b_0_: finished. Total: 1.77 s, CPU [user: 0.785 s, system: 0.0919 s], Allocated memory: 1.1 GB, RepositoryConfigService: 1.33 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.914 s [64% getFile content (412x), 36% getDir2 content (21x)] (434x)
2025-08-02 22:43:52,171 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661acf4f1504c_0_661acf4f1504c_0_: finished. Total: 0.15 s, CPU [user: 0.0356 s, system: 0.0034 s], Allocated memory: 17.9 MB, svn: 0.132 s [82% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0375 s [98% getReadConfiguration (124x)] (148x)
2025-08-02 22:43:52,684 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661acf4f4604e_0_661acf4f4604e_0_: finished. Total: 0.468 s, CPU [user: 0.195 s, system: 0.0137 s], Allocated memory: 384.8 MB, RepositoryConfigService: 0.338 s [95% getReadConfiguration (2787x)] (3025x), svn: 0.266 s [64% getFile content (185x), 36% getDir2 content (21x)] (207x)
2025-08-02 22:43:52,847 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661acf4fbb04f_0_661acf4fbb04f_0_: finished. Total: 0.163 s, CPU [user: 0.0378 s, system: 0.00429 s], Allocated memory: 13.1 MB, svn: 0.147 s [83% getDir2 content (18x)] (52x), RepositoryConfigService: 0.0363 s [98% getReadConfiguration (128x)] (150x)
2025-08-02 22:43:52,848 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 7 s, CPU [user: 2.21 s, system: 0.366 s], Allocated memory: 1.6 GB, transactions: 11, svn: 3.98 s [34% getDir2 content (133x), 34% getFile content (865x), 28% getDatedRevision (181x)] (1224x), RepositoryConfigService: 2.44 s [95% getReadConfiguration (12165x)] (12859x), resolve: 1.21 s [83% Category (117x)] (139x), ObjectMaps: 0.499 s [41% getPrimaryObjectProperty (131x), 38% getPrimaryObjectLocation (137x), 22% getLastPromoted (131x)] (536x)
2025-08-02 22:43:52,848 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 61, svn: 5.47 s [47% getDatedRevision (362x), 25% getDir2 content (133x), 24% getFile content (865x)] (1409x), RepositoryConfigService: 2.44 s [95% getReadConfiguration (12165x)] (12859x), resolve: 1.22 s [83% Category (117x)] (140x), ObjectMaps: 0.499 s [41% getPrimaryObjectProperty (131x), 38% getPrimaryObjectLocation (137x), 22% getLastPromoted (131x)] (536x)
2025-08-02 22:43:56,642 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.227490234375
2025-08-02 22:44:06,644 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.11748046875
2025-08-02 22:50:52,612 [ajp-nio-127.0.0.1-8889-exec-6 | cID:6b43a6bf-0ad33702-58c74364-3f916097] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?type=%E4%BB%A3%E7%A0%81-review&_t=1754146252419': Total: 0.13 s, CPU [user: 0.0133 s, system: 0.0285 s], Allocated memory: 582.2 kB, transactions: 0
