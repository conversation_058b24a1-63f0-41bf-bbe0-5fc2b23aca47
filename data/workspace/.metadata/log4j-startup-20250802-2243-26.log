2025-08-02 22:43:26,647 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 22:43:26,648 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-02 22:43:26,648 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 22:43:26,648 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-02 22:43:26,648 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-02 22:43:26,648 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 22:43:26,648 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-02 22:43:34,074 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-02 22:43:34,469 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.395 s. ]
2025-08-02 22:43:34,470 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-02 22:43:34,658 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.188 s. ]
2025-08-02 22:43:34,843 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-02 22:43:35,175 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 16 s. ]
2025-08-02 22:43:35,725 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 9.09 s. ]
2025-08-02 22:43:35,887 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 22:43:35,887 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-02 22:43:35,925 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.2 s. ]
2025-08-02 22:43:35,926 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 22:43:35,926 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-02 22:43:35,935 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-08-02 22:43:35,935 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-08-02 22:43:35,935 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-08-02 22:43:35,935 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-08-02 22:43:35,934 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-08-02 22:43:35,935 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-08-02 22:43:35,954 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-02 22:43:36,169 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-02 22:43:36,250 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-02 22:43:36,903 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.98 s. ]
2025-08-02 22:43:37,015 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 22:43:37,015 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-02 22:43:37,477 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-02 22:43:37,499 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.6 s. ]
2025-08-02 22:43:37,578 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 22:43:37,578 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-02 22:43:37,585 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-02 22:43:37,703 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-02 22:43:37,796 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-02 22:43:37,863 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-02 22:43:37,900 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-02 22:43:37,962 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-02 22:43:38,074 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-02 22:43:38,199 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-02 22:43:38,319 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-02 22:43:38,319 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.82 s. ]
2025-08-02 22:43:38,319 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 22:43:38,319 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-02 22:43:38,347 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.03 s. ]
2025-08-02 22:43:38,348 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 22:43:38,348 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-02 22:43:38,646 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-02 22:43:38,653 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-02 22:43:38,918 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.57 s. ]
2025-08-02 22:43:38,920 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 22:43:38,920 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-02 22:43:38,939 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.02 s. ]
2025-08-02 22:43:38,939 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 22:43:38,939 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-02 22:43:45,832 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 6.89 s. ]
2025-08-02 22:43:45,832 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 22:43:45,832 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 19.2 s. ]
2025-08-02 22:43:45,832 [main] INFO  com.polarion.platform.startup - ****************************************************************
