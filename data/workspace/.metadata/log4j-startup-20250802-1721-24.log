2025-08-02 17:21:24,956 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 17:21:24,956 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-02 17:21:24,956 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 17:21:24,957 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-02 17:21:24,957 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-02 17:21:24,957 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 17:21:24,957 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-02 17:21:29,156 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-02 17:21:29,286 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.13 s. ]
2025-08-02 17:21:29,286 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-02 17:21:29,329 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0434 s. ]
2025-08-02 17:21:29,378 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-02 17:21:29,492 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 17 s. ]
2025-08-02 17:21:29,713 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.76 s. ]
2025-08-02 17:21:29,795 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 17:21:29,795 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-02 17:21:29,820 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-02 17:21:29,821 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 17:21:29,821 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-02 17:21:29,826 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-08-02 17:21:29,826 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-02 17:21:29,826 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-08-02 17:21:29,826 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-08-02 17:21:29,826 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-08-02 17:21:29,826 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-08-02 17:21:29,833 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-02 17:21:29,962 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-02 17:21:30,065 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-02 17:21:30,506 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.69 s. ]
2025-08-02 17:21:30,518 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 17:21:30,518 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-02 17:21:30,708 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-02 17:21:30,719 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.21 s. ]
2025-08-02 17:21:30,742 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 17:21:30,742 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-02 17:21:30,746 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-02 17:21:30,794 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-02 17:21:30,843 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-02 17:21:30,888 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-02 17:21:30,912 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-02 17:21:30,934 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-02 17:21:30,971 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-02 17:21:31,009 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-02 17:21:31,033 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-02 17:21:31,033 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.31 s. ]
2025-08-02 17:21:31,034 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 17:21:31,034 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-02 17:21:31,046 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-02 17:21:31,047 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 17:21:31,047 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-02 17:21:31,149 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-02 17:21:31,151 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-02 17:21:31,279 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-08-02 17:21:31,279 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 17:21:31,279 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-02 17:21:31,286 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-02 17:21:31,286 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 17:21:31,286 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-02 17:21:33,716 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.43 s. ]
2025-08-02 17:21:33,717 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 17:21:33,717 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.76 s. ]
2025-08-02 17:21:33,717 [main] INFO  com.polarion.platform.startup - ****************************************************************
