2025-08-02 13:19:09,559 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using logging context STANDALONE
2025-08-02 13:19:09,561 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Launchers manager started...
2025-08-02 13:19:09,561 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using home directory /opt/polarion/polarion
2025-08-02 13:19:09,561 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using root directory /opt/polarion
2025-08-02 13:19:09,561 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using workspace directory /opt/polarion/data/workspace
2025-08-02 13:19:09,562 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using config directory /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-08-02 13:19:09,562 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading external properties ...
2025-08-02 13:19:09,562 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using external property file /opt/polarion/etc/polarion.properties
2025-08-02 13:19:09,562 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading internal properties ...
2025-08-02 13:19:09,565 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Host: zhangwendeMini2.lan (**************)
2025-08-02 13:19:09,570 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Product: com.polarion.alm
2025-08-02 13:19:09,570 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Version: 3.22.1
2025-08-02 13:19:09,570 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Build: 20220419-1528-22_R1-be3adceb
2025-08-02 13:19:09,571 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/fasnote
2025-08-02 13:19:09,571 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/2404
2025-08-02 13:19:09,571 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Extensions: [exts]
2025-08-02 13:19:09,576 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace location: /opt/polarion/data/workspace
2025-08-02 13:19:09,577 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace lock acquired
2025-08-02 13:19:09,577 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Found applications: [polarion.server, polarion.coordinator, polarion.rt]
2025-08-02 13:19:09,577 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Starting application: polarion.server
2025-08-02 13:19:09,585 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Application extension successfully read
2025-08-02 13:19:09,591 [main] INFO  com.polarion.platform.internal.SystemStatistics - Initializing monitoring, isThreadCpuTimeSupported: true, isThreadContentionMonitoringSupported: true, isThreadAllocatedMemorySupported: true
2025-08-02 13:19:09,591 [main] INFO  com.polarion.platform.internal.SystemStatistics - State before enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-08-02 13:19:09,591 [main] INFO  com.polarion.platform.internal.SystemStatistics - State after enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-08-02 13:19:09,594 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:19:09,594 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-02 13:19:09,594 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:19:09,594 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-02 13:19:09,595 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-02 13:19:09,595 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:19:09,595 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-02 13:19:09,596 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** Java system properties listing: 
2025-08-02 13:19:09,626 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminPasswd = admin
2025-08-02 13:19:09,626 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminUser = admin
2025-08-02 13:19:09,626 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.auth = false
2025-08-02 13:19:09,626 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.host = 
2025-08-02 13:19:09,626 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.password = **PASSWORD**HIDDEN**
2025-08-02 13:19:09,626 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.port = 25
2025-08-02 13:19:09,626 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.user = 
2025-08-02 13:19:09,626 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - awt.toolkit = sun.lwawt.macosx.LWCToolkit
2025-08-02 13:19:09,626 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - base.url = http://localhost
2025-08-02 13:19:09,626 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - bfh.jobs.workdir = /opt/polarion/data/workspace/polarion-data/jobs
2025-08-02 13:19:09,626 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - BIRDir = /opt/polarion/data/BIR
2025-08-02 13:19:09,626 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - calculated.fields.mode = async
2025-08-02 13:19:09,626 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.activationHelpLink = https://polarion.plm.automation.siemens.com/getlicense
2025-08-02 13:19:09,626 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.server = https://license.polarion.com/licenseGenerator/generator/generate
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.enabled = false
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.url = http://www.gravatar.com/avatar/$emailHash$?d=identicon&s=50
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.application = polarion.server
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.config = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.data = /opt/polarion/data
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.eclipse = /opt/polarion/polarion
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.home = /opt/polarion/polarion
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.licenseDir = /opt/polarion/polarion/license
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.internalPG = polarion:polarion@localhost:5434
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.disabled = true
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.receivers = 
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.sender = 
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.subject.prefix = 
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.persistence.notifications.disabled = true
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.propertyFile = /opt/polarion/etc/polarion.properties
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.root = /opt/polarion
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.workspace = /opt/polarion/data/workspace
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.collaborationNotifications.enabled = true
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.document.listStyle = 1ai
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.loggingContext = STANDALONE
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.preview.thumbnailsDataDir = /opt/polarion/data/workspace/previews-data/thumbnails
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.cors.allowedOrigins = *
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.enabled = true
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.swaggerUi.enabled = true
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.accEndpointUrl = https://acc.collab.sws.siemens.com
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.baseDomain = sws.siemens.com
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.shareEndpointUrl = https://share.sws.siemens.com
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - content.types.user.table = /opt/polarion/polarion/plugins/com.polarion.core.boot_3.22.1/content-types.properties
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlHostname = localhost
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlPort = 8887
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.location = Sandbox/
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.useUserId = true
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.limitedAccessMessage = You may create a project in the Sandbox project group (only). Please fill in the required properties below. For example:<br/><table><tr><td>Location:</td><td>Sandbox/MyFirstProject</td></tr><tr><td>ID:</td><td>MyFirstProject</td></tr></table><br/>Or use the suggested defaults.
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug = false
2025-08-02 13:19:09,627 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.license.validation = true
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.machine.code.generation = true
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.security.validation = true
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.ALM = alm_vmodel
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Pro = alm_vmodel
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.QA = qa_vmodel
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Requirements = req_vmodel
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XBase = alm_vmodel
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XEnterprise = alm_vmodel
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XPro = alm_vmodel
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - derby.system.home = /opt/polarion/data/logs/derby
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.application = com.polarion.core.boot.app
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.commands = -application
com.polarion.core.boot.app
-data
/opt/polarion/data/workspace
-configuration
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
-dev
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
-os
linux
-ws
linux
-arch
arm64
-appId
polarion.server

2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.home.location = file:/opt/polarion/polarion/plugins/
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.p2.data.area = @config.dir/.p2
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.pde.launch = true
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.startTime = *************
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.stateSaveDelayInterval = 30000
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - enableCreateAccountForm = false
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - equinox.init.uuid = true
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - error.report.email = 
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.encoding = UTF-8
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.separator = /
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ftp.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gopherProxySet = false
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gosh.args = --nointeractive
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - htpasswd.path = htpasswd
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - http.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - index.activities = /opt/polarion/data/workspace/polarion-data/index
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.graphicsenv = sun.awt.CGraphicsEnvironment
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.headless = true
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.printerjob = sun.lwawt.macosx.CPrinterJob
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.path = /opt/polarion/polarion/plugins/org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.version = 55.0
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.home = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.io.tmpdir = /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.library.path = /Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.name = OpenJDK Runtime Environment
2025-08-02 13:19:09,628 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.version = 11.0.27+6-LTS
2025-08-02 13:19:09,629 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.security.policy = /opt/polarion/polarion/policy
2025-08-02 13:19:09,629 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.maintenance.version = 3
2025-08-02 13:19:09,629 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.name = Java Platform API Specification
2025-08-02 13:19:09,629 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.vendor = Oracle Corporation
2025-08-02 13:19:09,629 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.version = 11
2025-08-02 13:19:09,629 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor = Microsoft
2025-08-02 13:19:09,629 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url = https://www.microsoft.com
2025-08-02 13:19:09,629 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url.bug = https://github.com/microsoft/openjdk/issues
2025-08-02 13:19:09,629 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.version = Microsoft-11367290
2025-08-02 13:19:09,629 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version = 11.0.27
2025-08-02 13:19:09,629 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version.date = 2025-04-15
2025-08-02 13:19:09,629 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.compressedOopsMode = Zero based
2025-08-02 13:19:09,629 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.info = mixed mode
2025-08-02 13:19:09,629 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.name = OpenJDK 64-Bit Server VM
2025-08-02 13:19:09,629 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.name = Java Virtual Machine Specification
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.vendor = Oracle Corporation
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.version = 11
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.vendor = Microsoft
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.version = 11.0.27+6-LTS
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - javasvn.timeout = 10000
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - jdk.debug = release
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ldap.bind.password = **PASSWORD**HIDDEN**
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.audit.enabled = true
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.auto.scan.enabled = true
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.size = 100
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.ttl = 1800
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.check.interval = 0
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.expired = true
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.local.files = true
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.features = all
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.max.users = 10
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.plugin.id = com.fasnote.alm.plugin.manage
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.mode = true
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.show.machine.code = true
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.machine.binding = true
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.network.validation = true
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.directory = dev-licenses
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.encryption.enabled = false
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.hot.reload.enabled = true
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.log.level = DEBUG
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.machine.binding.enabled = false
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.max.plugins = 1000
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.scan.interval = 60
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.signature.validation.enabled = false
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.validation.timeout = 1000
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - licenseForNewUserAccount = 
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - line.separator = 

2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.contextSelector = org.apache.logging.log4j.core.selector.BasicContextSelector
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.loggerContextFactory = org.apache.logging.log4j.core.impl.Log4jContextFactory
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - logDir = /opt/polarion/data/workspace/.metadata/
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - login = polarion
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - mavenConfigDir = /opt/polarion/polarion/../maven
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - minimalPasswordLength = **PASSWORD**HIDDEN**
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.equinox.simpleconfigurator.configUrl = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/org.eclipse.equinox.simpleconfigurator/bundles.info
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.lyo.oslc4j.strictDatatypes = false
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.executionenvironment = OSGi/Minimum-1.0, OSGi/Minimum-1.1, OSGi/Minimum-1.2, JavaSE/compact1-1.8, JavaSE/compact2-1.8, JavaSE/compact3-1.8, JRE-1.1, J2SE-1.2, J2SE-1.3, J2SE-1.4, J2SE-1.5, JavaSE-1.6, JavaSE-1.7, JavaSE-1.8, JavaSE-9, JavaSE-10, JavaSE-11
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.language = zh
2025-08-02 13:19:09,630 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.name = MacOSX
2025-08-02 13:19:09,631 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.version = 15.5.0
2025-08-02 13:19:09,631 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.processor = aarch64
2025-08-02 13:19:09,631 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.storage = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-08-02 13:19:09,631 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.capabilities = osgi.ee; osgi.ee="OSGi/Minimum"; version:List<Version>="1.0, 1.1, 1.2", osgi.ee; osgi.ee="JRE"; version:List<Version>="1.0, 1.1", osgi.ee; osgi.ee="JavaSE"; version:List<Version>="1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact1"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact2"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact3"; version:List<Version>="1.8, 9.0, 10.0, 11.0"
2025-08-02 13:19:09,631 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.packages = com.sun.jarsigner, com.sun.java.accessibility.util, com.sun.javadoc, com.sun.jdi, com.sun.jdi.connect, com.sun.jdi.connect.spi, com.sun.jdi.event, com.sun.jdi.request, com.sun.jndi.ldap.spi, com.sun.management, com.sun.net.httpserver, com.sun.net.httpserver.spi, com.sun.nio.file, com.sun.nio.sctp, com.sun.security.auth, com.sun.security.auth.callback, com.sun.security.auth.login, com.sun.security.auth.module, com.sun.security.jgss, com.sun.source.doctree, com.sun.source.tree, com.sun.source.util, com.sun.tools.attach, com.sun.tools.attach.spi, com.sun.tools.javac, com.sun.tools.javadoc, com.sun.tools.jconsole, java.applet, java.awt, java.awt.color, java.awt.datatransfer, java.awt.desktop, java.awt.dnd, java.awt.event, java.awt.font, java.awt.geom, java.awt.im, java.awt.im.spi, java.awt.image, java.awt.image.renderable, java.awt.print, java.beans, java.beans.beancontext, java.io, java.lang, java.lang.annotation, java.lang.instrument, java.lang.invoke, java.lang.management, java.lang.module, java.lang.ref, java.lang.reflect, java.math, java.net, java.net.http, java.net.spi, java.nio, java.nio.channels, java.nio.channels.spi, java.nio.charset, java.nio.charset.spi, java.nio.file, java.nio.file.attribute, java.nio.file.spi, java.rmi, java.rmi.activation, java.rmi.dgc, java.rmi.registry, java.rmi.server, java.security, java.security.acl, java.security.cert, java.security.interfaces, java.security.spec, java.sql, java.text, java.text.spi, java.time, java.time.chrono, java.time.format, java.time.temporal, java.time.zone, java.util, java.util.concurrent, java.util.concurrent.atomic, java.util.concurrent.locks, java.util.function, java.util.jar, java.util.logging, java.util.prefs, java.util.regex, java.util.spi, java.util.stream, java.util.zip, javax.accessibility, javax.annotation.processing, javax.crypto, javax.crypto.interfaces, javax.crypto.spec, javax.imageio, javax.imageio.event, javax.imageio.metadata, javax.imageio.plugins.bmp, javax.imageio.plugins.jpeg, javax.imageio.plugins.tiff, javax.imageio.spi, javax.imageio.stream, javax.lang.model, javax.lang.model.element, javax.lang.model.type, javax.lang.model.util, javax.management, javax.management.loading, javax.management.modelmbean, javax.management.monitor, javax.management.openmbean, javax.management.relation, javax.management.remote, javax.management.remote.rmi, javax.management.timer, javax.naming, javax.naming.directory, javax.naming.event, javax.naming.ldap, javax.naming.spi, javax.net, javax.net.ssl, javax.print, javax.print.attribute, javax.print.attribute.standard, javax.print.event, javax.rmi.ssl, javax.script, javax.security.auth, javax.security.auth.callback, javax.security.auth.kerberos, javax.security.auth.login, javax.security.auth.spi, javax.security.auth.x500, javax.security.cert, javax.security.sasl, javax.smartcardio, javax.sound.midi, javax.sound.midi.spi, javax.sound.sampled, javax.sound.sampled.spi, javax.sql, javax.sql.rowset, javax.sql.rowset.serial, javax.sql.rowset.spi, javax.swing, javax.swing.border, javax.swing.colorchooser, javax.swing.event, javax.swing.filechooser, javax.swing.plaf, javax.swing.plaf.basic, javax.swing.plaf.metal, javax.swing.plaf.multi, javax.swing.plaf.nimbus, javax.swing.plaf.synth, javax.swing.table, javax.swing.text, javax.swing.text.html, javax.swing.text.html.parser, javax.swing.text.rtf, javax.swing.tree, javax.swing.undo, javax.tools, javax.transaction.xa, javax.xml, javax.xml.catalog, javax.xml.crypto, javax.xml.crypto.dom, javax.xml.crypto.dsig, javax.xml.crypto.dsig.dom, javax.xml.crypto.dsig.keyinfo, javax.xml.crypto.dsig.spec, javax.xml.datatype, javax.xml.namespace, javax.xml.parsers, javax.xml.stream, javax.xml.stream.events, javax.xml.stream.util, javax.xml.transform, javax.xml.transform.dom, javax.xml.transform.sax, javax.xml.transform.stax, javax.xml.transform.stream, javax.xml.validation, javax.xml.xpath, jdk.dynalink, jdk.dynalink.beans, jdk.dynalink.linker, jdk.dynalink.linker.support, jdk.dynalink.support, jdk.javadoc.doclet, jdk.jfr, jdk.jfr.consumer, jdk.jshell, jdk.jshell.execution, jdk.jshell.spi, jdk.jshell.tool, jdk.management.jfr, jdk.nashorn.api.scripting, jdk.nashorn.api.tree, jdk.net, jdk.nio, jdk.security.jarsigner, jdk.swing.interop, netscape.javascript, org.ietf.jgss, org.w3c.dom, org.w3c.dom.bootstrap, org.w3c.dom.css, org.w3c.dom.events, org.w3c.dom.html, org.w3c.dom.ls, org.w3c.dom.ranges, org.w3c.dom.stylesheets, org.w3c.dom.traversal, org.w3c.dom.views, org.w3c.dom.xpath, org.xml.sax, org.xml.sax.ext, org.xml.sax.helpers, sun.misc, sun.reflect
2025-08-02 13:19:09,631 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.uuid = a175a155-0914-4b51-bcb6-b49184ac7a14
2025-08-02 13:19:09,631 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.vendor = Eclipse
2025-08-02 13:19:09,631 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.version = 1.9.0
2025-08-02 13:19:09,631 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.extension = true
2025-08-02 13:19:09,631 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.fragment = true
2025-08-02 13:19:09,631 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.requirebundle = true
2025-08-02 13:19:09,631 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.client.readbuffer.usedirect = true
2025-08-02 13:19:09,631 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.server.readbuffer.usedirect = true
2025-08-02 13:19:09,631 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.arch = aarch64
2025-08-02 13:19:09,631 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.name = Mac OS X
2025-08-02 13:19:09,631 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.version = 15.5
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.arch = arm64
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles = reference:file:/opt/polarion/polarion/plugins/org.eclipse.equinox.simpleconfigurator_1.3.0.v20180502-1828.jar@1:start
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles.defaultStartLevel = 4
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.checkConfiguration = true
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation = true
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation.default = true
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.area = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.cascaded = false
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.dev = file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework = file:/opt/polarion/polarion/plugins/org.eclipse.osgi_3.13.0.v20180409-1500.jar
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.shape = jar
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.useSystemProperties = true
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.install.area = file:/opt/polarion/polarion/plugins/
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.instance.area = file:/opt/polarion/data/workspace/
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.logfile = /opt/polarion/data/workspace/.metadata/.log
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.nl = zh_CN_#Hans
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.os = linux
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.syspath = /opt/polarion/polarion/plugins
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.tracefile = /opt/polarion/data/workspace/.metadata/trace.log
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.ws = linux
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - password = **PASSWORD**HIDDEN**
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - path.separator = :
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfbox.fontcache = /opt/polarion/data/workspace/polarion-data
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfexport.config = /opt/polarion/polarion/configuration/pdfexport.xml
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.id = polarion-shared
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.url = file:///opt/polarion/data/shared-maven-repo
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.maven.location.maven2 = /opt/polarion/polarion/../maven/distribution
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.size = 100
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.with.history = false
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.tx.doc.cache.size = 100
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.alm = https://polarion.plm.automation.siemens.com/products/polarion-alm
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.qa = https://polarion.plm.automation.siemens.com/products/polarion-qa
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/polarion-requirements
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - repo = http://localhost/repo
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - rolesForNewUserAccount = user
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - RRDir = /opt/polarion/data/RR
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - SDKDir = /opt/polarion/polarion/SDK
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - secure.approvals = false
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - shutdownCatchPhrase = shutdown
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - simple.profiler.enabled = false
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - skip.data.preloading = false
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - socksNonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stderr.encoding = UTF-8
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stdout.encoding = UTF-8
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - storeUrl.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/licensing?product=REQUIREMENTS
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.arch.data.model = 64
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.boot.library.path = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/lib
2025-08-02 13:19:09,632 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.endian = little
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.isalist = 
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.io.unicode.encoding = UnicodeBig
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.command = org.eclipse.equinox.launcher.Main -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -configuration file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/ -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.launcher = SUN_STANDARD
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.jnu.encoding = UTF-8
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.management.compiler = HotSpot 64-Bit Tiered Compilers
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.os.patch.level = unknown
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.contact = https://polarion.plm.automation.siemens.com/techsupport/resources
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.license.email = <EMAIL>
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.sales.email = <EMAIL>
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.access.file = /opt/polarion/data/svn/access
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.passwd.file = /opt/polarion/data/svn/passwd
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.http.encoding = UTF-8
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.library.gnome-keyring.enabled = false
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.ajp13-port = 8889
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.request.safeListedHosts = 0.0.0.0
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.country = CN
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.dir = /Applications/Eclipse JEE.app/Contents/MacOS
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.home = /Users/<USER>
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.language = zh
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.name = zhangwentian
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.script = Hans
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.timezone = Asia/Shanghai
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - userAccountVault = /opt/polarion/data/workspace/user-account-vault
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - workDir = /opt/polarion/data/workspace/polarion-data
2025-08-02 13:19:09,633 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** END of Java system properties
2025-08-02 13:19:09,634 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - XML parsers factory: com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl
2025-08-02 13:19:09,635 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Starting Platform...
2025-08-02 13:19:09,644 [main] INFO  PolarionLicensing - Searching for valid license file in /opt/polarion/polarion/license
2025-08-02 13:19:09,646 [main] INFO  PolarionLicensing - Trying to load license file polarion.lic
2025-08-02 13:19:09,648 [main] INFO  PolarionLicensing - The license file contains the following fields:
2025-08-02 13:19:09,648 [main] INFO  PolarionLicensing - *** License fields ***
2025-08-02 13:19:09,648 [main] INFO  PolarionLicensing - VariantsNamedUsers = 3
2025-08-02 13:19:09,648 [main] INFO  PolarionLicensing - almNamedUsers = 3
2025-08-02 13:19:09,648 [main] INFO  PolarionLicensing - dateCreated = 23.07.2025
2025-08-02 13:19:09,648 [main] INFO  PolarionLicensing - expirationDate = 21.08.2025
2025-08-02 13:19:09,648 [main] INFO  PolarionLicensing - hardwareKey = 8AG9-261C-1962
2025-08-02 13:19:09,648 [main] INFO  PolarionLicensing - licenseFormat = 2022
2025-08-02 13:19:09,648 [main] INFO  PolarionLicensing - licenseType = EVAL
2025-08-02 13:19:09,648 [main] INFO  PolarionLicensing - multiInstanceRunningInstances = 3
2025-08-02 13:19:09,648 [main] INFO  PolarionLicensing - userCompany = Polarion Eval
2025-08-02 13:19:09,648 [main] INFO  PolarionLicensing - *** License fields END ***
2025-08-02 13:19:09,667 [main] INFO  PolarionLicensing - Removing allocations by null
2025-08-02 13:19:09,668 [main] INFO  PolarionLicensing - STATS:concurrentVariantsUser,current:0,peak:0,limit:0
2025-08-02 13:19:09,669 [main] INFO  PolarionLicensing - 0 namedReviewerUser assignments (out of 0) loaded: []
2025-08-02 13:19:09,669 [main] INFO  PolarionLicensing - 0 concurrentReviewerUser assignments (out of 0) loaded: []
2025-08-02 13:19:09,669 [main] INFO  PolarionLicensing - STATS:concurrentReviewerUser,current:0,peak:0,limit:0
2025-08-02 13:19:09,669 [main] INFO  PolarionLicensing - 0 namedXBaseUser assignments (out of 0) loaded: []
2025-08-02 13:19:09,669 [main] INFO  PolarionLicensing - 0 concurrentXBaseUser assignments (out of 0) loaded: []
2025-08-02 13:19:09,669 [main] INFO  PolarionLicensing - STATS:concurrentXBaseUser,current:0,peak:0,limit:0
2025-08-02 13:19:09,669 [main] INFO  PolarionLicensing - 0 namedXProUser assignments (out of 0) loaded: []
2025-08-02 13:19:09,669 [main] INFO  PolarionLicensing - 0 concurrentXProUser assignments (out of 0) loaded: []
2025-08-02 13:19:09,669 [main] INFO  PolarionLicensing - STATS:concurrentXProUser,current:0,peak:0,limit:0
2025-08-02 13:19:09,669 [main] INFO  PolarionLicensing - 0 namedXEnterpriseUser assignments (out of 0) loaded: []
2025-08-02 13:19:09,669 [main] INFO  PolarionLicensing - 0 concurrentXEnterpriseUser assignments (out of 0) loaded: []
2025-08-02 13:19:09,669 [main] INFO  PolarionLicensing - STATS:concurrentXEnterpriseUser,current:0,peak:0,limit:0
2025-08-02 13:19:09,669 [main] INFO  PolarionLicensing - 0 namedProUser assignments (out of 0) loaded: []
2025-08-02 13:19:09,669 [main] INFO  PolarionLicensing - 0 concurrentProUser assignments (out of 0) loaded: []
2025-08-02 13:19:09,669 [main] INFO  PolarionLicensing - STATS:concurrentProUser,current:0,peak:0,limit:0
2025-08-02 13:19:09,669 [main] INFO  PolarionLicensing - 0 namedRequirementsUser assignments (out of 0) loaded: []
2025-08-02 13:19:09,670 [main] INFO  PolarionLicensing - 0 concurrentRequirementsUser assignments (out of 0) loaded: []
2025-08-02 13:19:09,670 [main] INFO  PolarionLicensing - STATS:concurrentRequirementsUser,current:0,peak:0,limit:0
2025-08-02 13:19:09,670 [main] INFO  PolarionLicensing - 0 namedQAUser assignments (out of 0) loaded: []
2025-08-02 13:19:09,670 [main] INFO  PolarionLicensing - 0 concurrentQAUser assignments (out of 0) loaded: []
2025-08-02 13:19:09,670 [main] INFO  PolarionLicensing - STATS:concurrentQAUser,current:0,peak:0,limit:0
2025-08-02 13:19:09,670 [main] INFO  PolarionLicensing - 3 namedALMUser assignments (out of 3) loaded: [admin, ou_d6f3139d36fb2978b33a8f870096b9e3, mTest]
2025-08-02 13:19:09,671 [main] INFO  PolarionLicensing - 0 concurrentALMUser assignments (out of 0) loaded: []
2025-08-02 13:19:09,671 [main] INFO  PolarionLicensing - STATS:concurrentALMUser,current:0,peak:0,limit:0
2025-08-02 13:19:09,671 [main] INFO  PolarionLicensing - 
*******************************************************************
 Polarion successfully activated
*******************************************************************
2025-08-02 13:19:09,730 [main] INFO  com.polarion.platform.internal.i18n.LanguageContributor - Localization file /META-INF/messages_en.properties read successfully (7789 messages)
2025-08-02 13:19:09,750 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Processing bundles:
2025-08-02 13:19:09,750 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [0] - org.eclipse.osgi
2025-08-02 13:19:09,750 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [1] - org.eclipse.equinox.simpleconfigurator
2025-08-02 13:19:09,750 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [2] - antlr
2025-08-02 13:19:09,750 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [3] - antlr4
2025-08-02 13:19:09,750 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [4] - antlr4-runtime
2025-08-02 13:19:09,750 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [5] - bcprov
2025-08-02 13:19:09,751 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [6] - com.auth0.java-jwt
2025-08-02 13:19:09,751 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [7] - com.fasnote.alm.auth.feishu
2025-08-02 13:19:09,753 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.fasnote.alm.auth.feishu to HiveMind
2025-08-02 13:19:09,753 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [8] - com.fasnote.alm.checklist
2025-08-02 13:19:09,753 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [9] - com.fasnote.alm.injection
2025-08-02 13:19:09,753 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [10] - com.fasnote.alm.plugin.manage
2025-08-02 13:19:09,753 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [11] - com.fasnote.alm.test
2025-08-02 13:19:09,755 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [12] - com.fasnote.alm.watermark
2025-08-02 13:19:09,755 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [13] - com.fasterxml.classmate
2025-08-02 13:19:09,755 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [14] - com.fasterxml.jackson
2025-08-02 13:19:09,755 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [15] - com.fasterxml.jackson.dataformat.jackson-dataformat-yaml
2025-08-02 13:19:09,756 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [16] - com.fasterxml.jackson.jaxrs
2025-08-02 13:19:09,756 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [17] - com.fasterxml.woodstox
2025-08-02 13:19:09,756 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [18] - com.finething.hesai.ai
2025-08-02 13:19:09,758 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.finething.hesai.ai to HiveMind
2025-08-02 13:19:09,758 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [19] - com.finething.hesai.defect
2025-08-02 13:19:09,758 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.finething.hesai.defect to HiveMind
2025-08-02 13:19:09,758 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [20] - com.google.gson
2025-08-02 13:19:09,758 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [21] - com.google.guava
2025-08-02 13:19:09,759 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [22] - com.google.guava.failureaccess
2025-08-02 13:19:09,759 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [23] - com.ibm.icu.icu4j
2025-08-02 13:19:09,759 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [24] - com.icl.saxon
2025-08-02 13:19:09,759 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [25] - com.jayway.jsonpath.json-path
2025-08-02 13:19:09,759 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [26] - com.jcraft.jsch
2025-08-02 13:19:09,759 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [27] - com.networknt.json-schema-validator
2025-08-02 13:19:09,759 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [28] - com.nimbusds.content-type
2025-08-02 13:19:09,759 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [29] - com.nimbusds.nimbus-jose-jwt
2025-08-02 13:19:09,759 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [30] - com.opensymphony.quartz
2025-08-02 13:19:09,760 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [31] - com.polarion.alm.ProjectPlanGantt_new
2025-08-02 13:19:09,761 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.ProjectPlanGantt_new to HiveMind
2025-08-02 13:19:09,761 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [32] - com.polarion.alm.builder
2025-08-02 13:19:09,761 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.builder to HiveMind
2025-08-02 13:19:09,761 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [33] - com.polarion.alm.checker
2025-08-02 13:19:09,761 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.checker to HiveMind
2025-08-02 13:19:09,761 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [34] - com.polarion.alm.extension.vcontext
2025-08-02 13:19:09,762 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.extension.vcontext to HiveMind
2025-08-02 13:19:09,762 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [35] - com.polarion.alm.impex
2025-08-02 13:19:09,763 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.impex to HiveMind
2025-08-02 13:19:09,763 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [36] - com.polarion.alm.install
2025-08-02 13:19:09,763 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [37] - com.polarion.alm.oslc
2025-08-02 13:19:09,764 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.oslc to HiveMind
2025-08-02 13:19:09,764 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [38] - com.polarion.alm.projects
2025-08-02 13:19:09,764 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.projects to HiveMind
2025-08-02 13:19:09,764 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [39] - com.polarion.alm.qcentre
2025-08-02 13:19:09,764 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.qcentre to HiveMind
2025-08-02 13:19:09,764 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [40] - com.polarion.alm.tracker
2025-08-02 13:19:09,765 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.tracker to HiveMind
2025-08-02 13:19:09,765 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [41] - com.polarion.alm.ui
2025-08-02 13:19:09,768 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.ui to HiveMind
2025-08-02 13:19:09,768 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [42] - com.polarion.alm.ui.diagrams.mxgraph
2025-08-02 13:19:09,768 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [43] - com.polarion.alm.wiki
2025-08-02 13:19:09,770 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.wiki to HiveMind
2025-08-02 13:19:09,770 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [44] - com.polarion.alm.ws
2025-08-02 13:19:09,770 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [45] - com.polarion.alm.ws.client
2025-08-02 13:19:09,771 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [46] - com.polarion.cluster
2025-08-02 13:19:09,771 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.cluster to HiveMind
2025-08-02 13:19:09,771 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [47] - com.polarion.core.boot
2025-08-02 13:19:09,772 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [48] - com.polarion.core.util
2025-08-02 13:19:09,772 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [49] - com.polarion.fop
2025-08-02 13:19:09,772 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [50] - com.polarion.platform
2025-08-02 13:19:09,773 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform to HiveMind
2025-08-02 13:19:09,773 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [51] - com.polarion.platform.guice
2025-08-02 13:19:09,773 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [52] - com.polarion.platform.hivemind
2025-08-02 13:19:09,773 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.hivemind to HiveMind
2025-08-02 13:19:09,773 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [53] - com.polarion.platform.jobs
2025-08-02 13:19:09,773 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.jobs to HiveMind
2025-08-02 13:19:09,773 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [54] - com.polarion.platform.monitoring
2025-08-02 13:19:09,774 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.monitoring to HiveMind
2025-08-02 13:19:09,774 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [55] - com.polarion.platform.persistence
2025-08-02 13:19:09,774 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.persistence to HiveMind
2025-08-02 13:19:09,774 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [56] - com.polarion.platform.repository
2025-08-02 13:19:09,774 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository to HiveMind
2025-08-02 13:19:09,774 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [57] - com.polarion.platform.repository.driver.svn
2025-08-02 13:19:09,774 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.driver.svn to HiveMind
2025-08-02 13:19:09,774 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [58] - com.polarion.platform.repository.external
2025-08-02 13:19:09,774 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external to HiveMind
2025-08-02 13:19:09,774 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [59] - com.polarion.platform.repository.external.git
2025-08-02 13:19:09,774 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.git to HiveMind
2025-08-02 13:19:09,774 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [60] - com.polarion.platform.repository.external.svn
2025-08-02 13:19:09,775 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.svn to HiveMind
2025-08-02 13:19:09,775 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [61] - com.polarion.platform.sql
2025-08-02 13:19:09,775 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [62] - com.polarion.portal.tomcat
2025-08-02 13:19:09,782 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [63] - com.polarion.psvn.launcher
2025-08-02 13:19:09,783 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.psvn.launcher to HiveMind
2025-08-02 13:19:09,783 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [64] - com.polarion.psvn.translations.en
2025-08-02 13:19:09,783 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [65] - com.polarion.purevariants
2025-08-02 13:19:09,783 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.purevariants to HiveMind
2025-08-02 13:19:09,783 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [66] - com.polarion.qcentre
2025-08-02 13:19:09,785 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [67] - com.polarion.scripting
2025-08-02 13:19:09,794 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.scripting to HiveMind
2025-08-02 13:19:09,794 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [68] - com.polarion.scripting.servlet
2025-08-02 13:19:09,794 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [69] - com.polarion.subterra.base
2025-08-02 13:19:09,794 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [70] - com.polarion.subterra.index
2025-08-02 13:19:09,795 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.index to HiveMind
2025-08-02 13:19:09,795 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [71] - com.polarion.subterra.persistence
2025-08-02 13:19:09,795 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence to HiveMind
2025-08-02 13:19:09,796 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [72] - com.polarion.subterra.persistence.document
2025-08-02 13:19:09,796 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence.document to HiveMind
2025-08-02 13:19:09,796 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [73] - com.polarion.synchronizer
2025-08-02 13:19:09,796 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.synchronizer to HiveMind
2025-08-02 13:19:09,796 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [74] - com.polarion.synchronizer.proxy.feishu
2025-08-02 13:19:09,798 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [75] - com.polarion.synchronizer.proxy.hpalm
2025-08-02 13:19:09,802 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [76] - com.polarion.synchronizer.proxy.jira
2025-08-02 13:19:09,803 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [77] - com.polarion.synchronizer.proxy.polarion
2025-08-02 13:19:09,804 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [78] - com.polarion.synchronizer.proxy.reqif
2025-08-02 13:19:09,816 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [79] - com.polarion.synchronizer.ui
2025-08-02 13:19:09,816 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [80] - com.polarion.usdp.persistence
2025-08-02 13:19:09,817 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.usdp.persistence to HiveMind
2025-08-02 13:19:09,817 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [81] - com.polarion.xray.doc.user
2025-08-02 13:19:09,817 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [82] - com.siemens.des.logger.api
2025-08-02 13:19:09,817 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [83] - com.siemens.plm.bitools.analytics
2025-08-02 13:19:09,818 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [84] - com.siemens.polarion.ct.collectors.git
2025-08-02 13:19:09,819 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.ct.collectors.git to HiveMind
2025-08-02 13:19:09,819 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [85] - com.siemens.polarion.eclipse.configurator
2025-08-02 13:19:09,820 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [86] - com.siemens.polarion.integration.ci
2025-08-02 13:19:09,821 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.integration.ci to HiveMind
2025-08-02 13:19:09,821 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [87] - com.siemens.polarion.previewer
2025-08-02 13:19:09,821 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer to HiveMind
2025-08-02 13:19:09,821 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [88] - com.siemens.polarion.previewer.external
2025-08-02 13:19:09,822 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer.external to HiveMind
2025-08-02 13:19:09,822 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [89] - com.siemens.polarion.rest
2025-08-02 13:19:09,822 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [90] - com.siemens.polarion.rt
2025-08-02 13:19:09,823 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [91] - com.siemens.polarion.rt.api
2025-08-02 13:19:09,823 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [92] - com.siemens.polarion.rt.collectors.git
2025-08-02 13:19:09,824 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [93] - com.siemens.polarion.rt.communication.common
2025-08-02 13:19:09,824 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [94] - com.siemens.polarion.rt.communication.polarion
2025-08-02 13:19:09,825 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.rt.communication.polarion to HiveMind
2025-08-02 13:19:09,825 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [95] - com.siemens.polarion.rt.communication.rt
2025-08-02 13:19:09,826 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [96] - com.siemens.polarion.rt.parsers.c
2025-08-02 13:19:09,826 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [97] - com.siemens.polarion.rt.ui
2025-08-02 13:19:09,826 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [98] - com.siemens.polarion.synchronizer.proxy.tfs
2025-08-02 13:19:09,827 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [99] - com.sun.activation.javax.activation
2025-08-02 13:19:09,828 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [100] - com.sun.istack.commons-runtime
2025-08-02 13:19:09,828 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [101] - com.sun.jna
2025-08-02 13:19:09,828 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [102] - com.sun.jna.platform
2025-08-02 13:19:09,828 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [103] - com.sun.xml.bind.jaxb-impl
2025-08-02 13:19:09,828 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [104] - com.teamlive.hozon.expcounter
2025-08-02 13:19:09,829 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.teamlive.hozon.expcounter to HiveMind
2025-08-02 13:19:09,829 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [105] - com.teamlive.livechecklist
2025-08-02 13:19:09,830 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.teamlive.livechecklist to HiveMind
2025-08-02 13:19:09,830 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [106] - com.trilead.ssh2
2025-08-02 13:19:09,830 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [107] - com.zaxxer.hikariCP
2025-08-02 13:19:09,830 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [108] - des-sdk-core
2025-08-02 13:19:09,830 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [109] - des-sdk-dss
2025-08-02 13:19:09,830 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [110] - io.github.resilience4j.circuitbreaker
2025-08-02 13:19:09,831 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [111] - io.github.resilience4j.core
2025-08-02 13:19:09,831 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [112] - io.github.resilience4j.retry
2025-08-02 13:19:09,831 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [113] - io.swagger
2025-08-02 13:19:09,831 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [114] - io.vavr
2025-08-02 13:19:09,831 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [115] - jakaroma
2025-08-02 13:19:09,831 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [116] - jakarta.validation.validation-api
2025-08-02 13:19:09,831 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [117] - javassist
2025-08-02 13:19:09,832 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [118] - javax.annotation-api
2025-08-02 13:19:09,832 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [119] - javax.cache
2025-08-02 13:19:09,832 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [120] - javax.el
2025-08-02 13:19:09,832 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [121] - javax.inject
2025-08-02 13:19:09,832 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [122] - javax.servlet
2025-08-02 13:19:09,832 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [123] - javax.servlet.jsp
2025-08-02 13:19:09,832 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [124] - javax.transaction
2025-08-02 13:19:09,832 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [125] - jaxb-api
2025-08-02 13:19:09,833 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [126] - jcip-annotations
2025-08-02 13:19:09,833 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [127] - jcl.over.slf4j
2025-08-02 13:19:09,833 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [128] - jul.to.slf4j
2025-08-02 13:19:09,833 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [129] - kuromoji-core
2025-08-02 13:19:09,833 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [130] - kuromoji-ipadic
2025-08-02 13:19:09,833 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [131] - lang-tag
2025-08-02 13:19:09,833 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [132] - net.htmlparser.jericho
2025-08-02 13:19:09,833 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [133] - net.java.dev.jna
2025-08-02 13:19:09,833 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [134] - net.minidev.accessors-smart
2025-08-02 13:19:09,833 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [135] - net.minidev.asm
2025-08-02 13:19:09,833 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [136] - net.minidev.json-smart
2025-08-02 13:19:09,834 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [137] - net.n3.nanoxml
2025-08-02 13:19:09,834 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [138] - net.sourceforge.cssparser
2025-08-02 13:19:09,834 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [139] - nu.xom
2025-08-02 13:19:09,834 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [140] - oauth2-oidc-sdk
2025-08-02 13:19:09,834 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [141] - org.apache.ant
2025-08-02 13:19:09,843 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [142] - org.apache.avro
2025-08-02 13:19:09,843 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [143] - org.apache.axis
2025-08-02 13:19:09,849 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [144] - org.apache.batik
2025-08-02 13:19:09,852 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [145] - org.apache.commons.codec
2025-08-02 13:19:09,852 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [146] - org.apache.commons.collections
2025-08-02 13:19:09,853 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [147] - org.apache.commons.commons-beanutils
2025-08-02 13:19:09,853 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [148] - org.apache.commons.commons-collections4
2025-08-02 13:19:09,854 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [149] - org.apache.commons.commons-compress
2025-08-02 13:19:09,855 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [150] - org.apache.commons.commons-fileupload
2025-08-02 13:19:09,855 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [151] - org.apache.commons.digester
2025-08-02 13:19:09,856 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [152] - org.apache.commons.exec
2025-08-02 13:19:09,856 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [153] - org.apache.commons.io
2025-08-02 13:19:09,857 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [154] - org.apache.commons.lang
2025-08-02 13:19:09,860 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [155] - org.apache.commons.lang3
2025-08-02 13:19:09,861 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [156] - org.apache.commons.logging
2025-08-02 13:19:09,861 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [157] - org.apache.curator
2025-08-02 13:19:09,873 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [158] - org.apache.fop
2025-08-02 13:19:09,874 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [159] - org.apache.hivemind
2025-08-02 13:19:09,875 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle org.apache.hivemind to HiveMind
2025-08-02 13:19:09,876 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [160] - org.apache.httpcomponents.httpclient
2025-08-02 13:19:09,876 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [161] - org.apache.httpcomponents.httpcore
2025-08-02 13:19:09,877 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [162] - org.apache.jasper.glassfish
2025-08-02 13:19:09,877 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [163] - org.apache.kafka.clients
2025-08-02 13:19:09,879 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [164] - org.apache.kafka.streams
2025-08-02 13:19:09,881 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [165] - org.apache.logging.log4j.1.2-api
2025-08-02 13:19:09,882 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [166] - org.apache.logging.log4j.api
2025-08-02 13:19:09,883 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [167] - org.apache.logging.log4j.apiconf
2025-08-02 13:19:09,883 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [168] - org.apache.logging.log4j.core
2025-08-02 13:19:09,883 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [169] - org.apache.logging.log4j.slf4j-impl
2025-08-02 13:19:09,883 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [170] - org.apache.lucene.analyzers-common
2025-08-02 13:19:09,883 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [171] - org.apache.lucene.analyzers-common
2025-08-02 13:19:09,889 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [172] - org.apache.lucene.analyzers-smartcn
2025-08-02 13:19:09,889 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [173] - org.apache.lucene.core
2025-08-02 13:19:09,890 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [174] - org.apache.lucene.core
2025-08-02 13:19:09,896 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [175] - org.apache.lucene.grouping
2025-08-02 13:19:09,897 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [176] - org.apache.lucene.queryparser
2025-08-02 13:19:09,897 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [177] - org.apache.oro
2025-08-02 13:19:09,897 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [178] - org.apache.pdfbox.fontbox
2025-08-02 13:19:09,897 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [179] - org.apache.poi
2025-08-02 13:19:09,901 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [180] - org.apache.tika
2025-08-02 13:19:10,430 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [181] - org.apache.xalan
2025-08-02 13:19:10,431 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [182] - org.apache.xercesImpl
2025-08-02 13:19:10,431 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [183] - org.apache.xml.serializer
2025-08-02 13:19:10,431 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [184] - org.apache.xmlgraphics.commons
2025-08-02 13:19:10,431 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [185] - org.apache.zookeeper
2025-08-02 13:19:10,433 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [186] - org.codehaus.groovy
2025-08-02 13:19:10,434 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [187] - org.codehaus.jettison
2025-08-02 13:19:10,434 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [188] - org.dom4j
2025-08-02 13:19:10,434 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [189] - org.eclipse.core.contenttype
2025-08-02 13:19:10,434 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [190] - org.eclipse.core.expressions
2025-08-02 13:19:10,434 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [191] - org.eclipse.core.filesystem
2025-08-02 13:19:10,434 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [192] - org.eclipse.core.jobs
2025-08-02 13:19:10,435 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [193] - org.eclipse.core.net
2025-08-02 13:19:10,435 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [194] - org.eclipse.core.resources
2025-08-02 13:19:10,435 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [195] - org.eclipse.core.runtime
2025-08-02 13:19:10,435 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [196] - org.eclipse.equinox.app
2025-08-02 13:19:10,436 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [197] - org.eclipse.equinox.common
2025-08-02 13:19:10,436 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [198] - org.eclipse.equinox.event
2025-08-02 13:19:10,436 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [199] - org.eclipse.equinox.http.registry
2025-08-02 13:19:10,436 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [200] - org.eclipse.equinox.http.servlet
2025-08-02 13:19:10,436 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [201] - org.eclipse.equinox.jsp.jasper
2025-08-02 13:19:10,436 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [202] - org.eclipse.equinox.jsp.jasper.registry
2025-08-02 13:19:10,436 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [203] - org.eclipse.equinox.launcher
2025-08-02 13:19:10,436 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [204] - org.eclipse.equinox.preferences
2025-08-02 13:19:10,436 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [205] - org.eclipse.equinox.registry
2025-08-02 13:19:10,437 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [206] - org.eclipse.equinox.security
2025-08-02 13:19:10,437 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [207] - org.eclipse.help
2025-08-02 13:19:10,437 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [208] - org.eclipse.help.base
2025-08-02 13:19:10,437 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [209] - org.eclipse.help.webapp
2025-08-02 13:19:10,437 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [210] - org.eclipse.jgit
2025-08-02 13:19:10,437 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [211] - org.eclipse.osgi.services
2025-08-02 13:19:10,437 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [212] - org.eclipse.osgi.util
2025-08-02 13:19:10,437 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [213] - org.ehcache
2025-08-02 13:19:10,439 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [214] - org.gitlab.java-gitlab-api
2025-08-02 13:19:10,439 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [215] - org.glassfish.jersey
2025-08-02 13:19:10,440 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [216] - org.hibernate.annotations
2025-08-02 13:19:10,440 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [217] - org.hibernate.core
2025-08-02 13:19:10,440 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [218] - org.hibernate.entitymanager
2025-08-02 13:19:10,440 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [219] - org.hibernate.hikaricp
2025-08-02 13:19:10,441 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [220] - org.hibernate.jpa.2.1.api
2025-08-02 13:19:10,441 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [221] - org.jboss.logging
2025-08-02 13:19:10,441 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [222] - org.jvnet.mimepull
2025-08-02 13:19:10,441 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [223] - org.objectweb.asm
2025-08-02 13:19:10,441 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [224] - org.objectweb.jotm
2025-08-02 13:19:10,441 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [225] - org.opensaml
2025-08-02 13:19:10,455 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [226] - org.polarion.svncommons
2025-08-02 13:19:10,455 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [227] - org.polarion.svnwebclient
2025-08-02 13:19:10,456 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [228] - org.postgesql
2025-08-02 13:19:10,456 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [229] - org.projectlombok.lombok
2025-08-02 13:19:10,468 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [230] - org.rocksdb.rocksdbjni
2025-08-02 13:19:10,468 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [231] - org.springframework.data.core
2025-08-02 13:19:10,468 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [232] - org.springframework.data.jpa
2025-08-02 13:19:10,468 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [233] - org.springframework.spring-aop
2025-08-02 13:19:10,468 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [234] - org.springframework.spring-beans
2025-08-02 13:19:10,469 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [235] - org.springframework.spring-context
2025-08-02 13:19:10,469 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [236] - org.springframework.spring-core
2025-08-02 13:19:10,470 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [237] - org.springframework.spring-expression
2025-08-02 13:19:10,470 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [238] - org.springframework.spring-jdbc
2025-08-02 13:19:10,470 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [239] - org.springframework.spring-orm
2025-08-02 13:19:10,471 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [240] - org.springframework.spring-test
2025-08-02 13:19:10,471 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [241] - org.springframework.spring-tx
2025-08-02 13:19:10,471 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [242] - org.springframework.spring-web
2025-08-02 13:19:10,472 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [243] - org.springframework.spring-webmvc
2025-08-02 13:19:10,472 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [244] - org.tmatesoft.sqljet
2025-08-02 13:19:10,472 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [245] - org.tmatesoft.svnkit
2025-08-02 13:19:10,473 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [246] - saaj-api
2025-08-02 13:19:10,473 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [247] - sdk-lifecycle-collab
2025-08-02 13:19:10,473 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [248] - sdk-lifecycle-docmgmt
2025-08-02 13:19:10,473 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [249] - siemens.des.clientsecurity
2025-08-02 13:19:10,473 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [250] - slf4j.api
2025-08-02 13:19:10,474 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [251] - xml-apis
2025-08-02 13:19:10,474 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [252] - xml.apis.ext
2025-08-02 13:19:10,474 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [253] - xstream
2025-08-02 13:19:10,745 [main] INFO  com.polarion.core.util.remote.server.SocketRemoteControlServer - Remote control server socket is ready to listen on localhost/127.0.0.1:8887
2025-08-02 13:19:10,745 [xServer:8887] INFO  org.xsocket.connection.Server - server listening on localhost:8887 (xSocket 2.5.3)
2025-08-02 13:19:11,047 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database...
2025-08-02 13:19:11,111 [main] INFO  com.polarion.platform.sql.internal.PgServerInfo - PG server listening on localhost:5435
2025-08-02 13:19:13,274 [main] INFO  com.polarion.platform.internal.cache.CacheConfigurator - EHCache uses internal configuration
2025-08-02 13:19:13,619 [main] WARN  org.ehcache.impl.internal.executor.PooledExecutionService - No default pool configured, services requiring thread pools must be configured explicitly using named thread pools
2025-08-02 13:19:13,715 [main] INFO  org.ehcache.sizeof.filters.AnnotationSizeOfFilter - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-02 13:19:13,722 [main] INFO  org.ehcache.sizeof.impl.JvmInformation - Detected JVM data model settings of: 64-Bit OpenJDK JVM with Compressed OOPs
2025-08-02 13:19:13,738 [main] INFO  org.ehcache.sizeof.impl.AgentLoader - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-02 13:19:13,914 [main] INFO  com.polarion.platform.internal.cache.CachingProviderHandler - All the caches have been destroyed because of not clean shutdown. You can ignore this message if Polarion started in reindex mode.
2025-08-02 13:19:13,980 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-08-02 13:19:13,986 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-08-02 13:19:13,992 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion_history' is: *****************************************_history
2025-08-02 13:19:14,095 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database finished [ TIME 3.05 s. ]
2025-08-02 13:19:14,391 [main] INFO  com.polarion.platform.cluster.ClusterService - Initializing cluster service
2025-08-02 13:19:14,391 [main] INFO  com.polarion.platform.cluster.ClusterService - Cluster service is disabled.
2025-08-02 13:19:14,644 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-02 13:19:14,667 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Starting...
2025-08-02 13:19:14,725 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Start completed.
2025-08-02 13:19:14,808 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.164 s. ]
2025-08-02 13:19:14,808 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-02 13:19:14,820 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Starting...
2025-08-02 13:19:14,833 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Start completed.
2025-08-02 13:19:14,896 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0878 s. ]
2025-08-02 13:19:14,897 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot started
2025-08-02 13:19:14,956 [main] INFO  com.polarion.platform.repository.driver.svn.internal.security.SVNWatcher - SVN auth file watcher started with a period of 3000 milliseconds
2025-08-02 13:19:14,968 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-02 13:19:15,018 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion authenticated from system
2025-08-02 13:19:15,080 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion logged in from system
2025-08-02 13:19:15,086 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Created
2025-08-02 13:19:15,087 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Initialized
2025-08-02 13:19:15,094 [main | u:p] INFO  com.polarion.core.util.profiling.SimpleProfiler - Initialization
2025-08-02 13:19:15,106 [main | u:p] INFO  org.objectweb.jotm - JOTM started with a local transaction factory which is not bound.
2025-08-02 13:19:15,107 [main | u:p] INFO  org.objectweb.jotm - CAROL initialization
2025-08-02 13:19:15,119 [main | u:p] INFO  com.polarion.platform.internal.service.repository.listeners.job.PullingJob - lastFullyProcessedRevision [275]
2025-08-02 13:19:15,132 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - END initializeService
2025-08-02 13:19:15,140 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Polarion startup estimation:  [ TIME 14 s. ]
2025-08-02 13:19:15,140 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 14 s. ]
2025-08-02 13:19:15,157 [main | u:p] INFO  com.polarion.platform.monitoring - Full monitoring results are stored in file /opt/polarion/data/workspace/monitoring/results.txt
2025-08-02 13:19:15,204 [main | u:p] INFO  com.polarion.platform.monitoring - Executing actions from stage PREBOOT
2025-08-02 13:19:15,207 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 13:19:15,208 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,420.2 GB
 [Sat Aug 02 13:19:15 CST 2025]
2025-08-02 13:19:15,424 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.jvm.version'
2025-08-02 13:19:15,424 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.jvm.version (JVM Version) = JVM Version 
Microsoft OpenJDK 64-Bit Server VM 11.0.27+6-LTS
 [Sat Aug 02 13:19:15 CST 2025]
2025-08-02 13:19:15,427 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.os.version'
2025-08-02 13:19:15,427 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.os.version (OS Version) = OS Version 
Mac OS X 15.5 aarch64
 [Sat Aug 02 13:19:15 CST 2025]
2025-08-02 13:19:15,431 [main | u:p] INFO  com.polarion.platform.monitoring - Finished with actions from stage PREBOOT: {OK=3}
2025-08-02 13:19:15,431 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.84 s. ]
2025-08-02 13:19:15,431 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.087 s [64% update (144x), 36% query (12x)] (221x), svn: 0.023 s [71% getLatestRevision (2x), 18% testConnection (1x)] (4x)
2025-08-02 13:19:15,432 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - calling ILowLevelPersistence.boot to start persistence
2025-08-02 13:19:15,442 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization started
2025-08-02 13:19:15,480 [main | u:p] INFO  com.polarion.subterra.base.internal.location.LocationCacheContext - Registered invalidationListener: com.polarion.platform.repository.internal.config.RepositoryConfigService$1@558e3324
2025-08-02 13:19:15,506 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: BaselineCollection
2025-08-02 13:19:15,506 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: TestRun
2025-08-02 13:19:15,506 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: Plan
2025-08-02 13:19:15,524 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition started
2025-08-02 13:19:15,524 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:19:15,525 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-02 13:19:15,562 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition finished [ TIME 0.0373 s. ]
2025-08-02 13:19:15,562 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context tree: 
ROOT_CTX_NAME (ContextNature[Root], ContextId[context [global]])
+-default (ContextNature[Repository], ContextId[cluster default, context [global]])
  +-WBS (ContextNature[Project], ContextId[cluster default, context WBS])
  +-WBSdev (ContextNature[Project], ContextId[cluster default, context WBSdev])
  +-Demo Projects (ContextNature[ProjectGroup], ContextId[cluster default, context --Demo Projects])
  | +-elibrary (ContextNature[Project], ContextId[cluster default, context elibrary])
  | +-drivepilot (ContextNature[Project], ContextId[cluster default, context drivepilot])
  +-library (ContextNature[Project], ContextId[cluster default, context library])
  +-hesai (ContextNature[Project], ContextId[cluster default, context hesai])
2025-08-02 13:19:15,562 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-08-02 13:19:15,562 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0441 s [66% getDir2 content (2x), 28% info (3x)] (6x)
2025-08-02 13:19:15,562 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:19:15,562 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-02 13:19:15,562 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Startup workers for phase 3: 6
2025-08-02 13:19:15,568 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-08-02 13:19:15,568 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-08-02 13:19:15,568 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-08-02 13:19:15,568 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-02 13:19:15,568 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context library] (4/9) ...
2025-08-02 13:19:15,568 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context [global]] (3/9) ...
2025-08-02 13:19:15,568 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-08-02 13:19:15,568 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBS] (6/9) ...
2025-08-02 13:19:15,568 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[context [global]] (1/9) ...
2025-08-02 13:19:15,568 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBSdev] (2/9) ...
2025-08-02 13:19:15,568 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-08-02 13:19:15,569 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context --Demo Projects] (5/9) ...
2025-08-02 13:19:15,576 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[context [global]] (1/9) TOOK  [ TIME 0.0077 s. ]
2025-08-02 13:19:15,576 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-02 13:19:15,576 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context hesai] (7/9) ...
2025-08-02 13:19:15,717 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/.polarion'
2025-08-02 13:19:15,740 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/modules'
2025-08-02 13:19:15,750 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/documents'
2025-08-02 13:19:15,761 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/_wiki'
2025-08-02 13:19:15,765 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context --Demo Projects contains 0 primary objects (work items+comments).
2025-08-02 13:19:15,766 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context --Demo Projects] (5/9) TOOK  [ TIME 0.197 s. ]
2025-08-02 13:19:15,766 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-02 13:19:15,766 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context elibrary] (8/9) ...
2025-08-02 13:19:15,801 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/library/documents'
2025-08-02 13:19:15,804 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBS/documents'
2025-08-02 13:19:15,857 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context library contains 288 primary objects (work items+comments).
2025-08-02 13:19:15,857 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context library] (4/9) TOOK  [ TIME 0.289 s. ]
2025-08-02 13:19:15,857 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-02 13:19:15,857 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context drivepilot] (9/9) ...
2025-08-02 13:19:15,886 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBS contains 344 primary objects (work items+comments).
2025-08-02 13:19:15,887 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBS] (6/9) TOOK  [ TIME 0.318 s. ]
2025-08-02 13:19:15,917 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/elibrary/documents'
2025-08-02 13:19:15,962 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context elibrary contains 334 primary objects (work items+comments).
2025-08-02 13:19:15,963 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context elibrary] (8/9) TOOK  [ TIME 0.197 s. ]
2025-08-02 13:19:15,993 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/hesai/documents'
2025-08-02 13:19:15,993 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/drivepilot/documents'
2025-08-02 13:19:16,043 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context drivepilot contains 461 primary objects (work items+comments).
2025-08-02 13:19:16,043 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context drivepilot] (9/9) TOOK  [ TIME 0.186 s. ]
2025-08-02 13:19:16,095 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context hesai contains 1148 primary objects (work items+comments).
2025-08-02 13:19:16,095 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context hesai] (7/9) TOOK  [ TIME 0.519 s. ]
2025-08-02 13:19:16,199 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context null contains 2214 primary objects (work items+comments).
2025-08-02 13:19:16,200 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context [global]] (3/9) TOOK  [ TIME 0.631 s. ]
2025-08-02 13:19:16,293 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBSdev/documents'
2025-08-02 13:19:16,455 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBSdev contains 3322 primary objects (work items+comments).
2025-08-02 13:19:16,455 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBSdev] (2/9) TOOK  [ TIME 0.887 s. ]
2025-08-02 13:19:16,457 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.891 s, CPU [user: 0.137 s, system: 0.19 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.102 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-02 13:19:16,457 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.891 s, CPU [user: 0.255 s, system: 0.269 s], Allocated memory: 53.3 MB, transactions: 0, ObjectMaps: 0.153 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-02 13:19:16,457 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.891 s, CPU [user: 0.0605 s, system: 0.0649 s], Allocated memory: 7.1 MB, transactions: 0, svn: 0.0562 s [69% log2 (5x), 22% getLatestRevision (1x)] (7x), ObjectMaps: 0.0534 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-02 13:19:16,457 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.891 s, CPU [user: 0.108 s, system: 0.133 s], Allocated memory: 14.2 MB, transactions: 0, svn: 0.0949 s [80% log2 (10x)] (13x), ObjectMaps: 0.0827 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-02 13:19:16,457 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.891 s, CPU [user: 0.078 s, system: 0.0723 s], Allocated memory: 8.6 MB, transactions: 0, svn: 0.141 s [81% log2 (10x)] (13x), ObjectMaps: 0.0471 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-02 13:19:16,457 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.891 s, CPU [user: 0.336 s, system: 0.348 s], Allocated memory: 71.1 MB, transactions: 0, ObjectMaps: 0.162 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.141 s [28% log2 (5x), 24% info (5x), 21% getLatestRevision (2x), 18% log (1x)] (18x)
2025-08-02 13:19:16,458 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.9 s. ]
2025-08-02 13:19:16,458 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.599 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.493 s [62% log2 (36x), 18% getLatestRevision (9x), 7% testConnection (6x)] (61x)
2025-08-02 13:19:16,476 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [START].
2025-08-02 13:19:16,476 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:19:16,476 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-02 13:19:16,611 [main | u:p] INFO  TXLOGGER - Tx 661a4e1583801_0_661a4e1583801_0_: finished. Total: 0.115 s, CPU [user: 0.0908 s, system: 0.00454 s], Allocated memory: 21.8 MB
2025-08-02 13:19:16,630 [main | u:p | u:p] ERROR com.polarion.platform.repository.internal.config.RepositoryConfigService$ConfigProblemCatcher - Failed to work with configuration from location /WBS/.polarion/repositories/repositories.xml:
[/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
com.polarion.platform.repository.config.RepositoryConfigurationException: [/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:87) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocations(AbstractDataHandler.java:61) ~[platform-repository.jar:?]
	at $IDataHandler_198693840e9.readLocations($IDataHandler_198693840e9.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readLocations(RepositoryConfigService.java:291) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$3.runImpl(RepositoryConfigService.java:328) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction$1.runWEx(RepositoryConfigService.java:113) ~[platform-repository.jar:?]
	at com.polarion.core.util.RunnableWEx.runWRet(RunnableWEx.java:61) ~[util.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction.run(RepositoryConfigService.java:123) ~[platform-repository.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:361) ~[?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:58) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:422) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:412) ~[platform.jar:?]
	at $ISecurityService_19869383f04.doAsSystemUser($ISecurityService_19869383f04.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readConfiguration(RepositoryConfigService.java:324) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getConfigurationImpl(RepositoryConfigService.java:239) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfigurationImpl(RepositoryConfigService.java:199) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:177) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.addConfigurationListener(RepositoryConfigService.java:263) ~[platform-repository.jar:?]
	at $IRepositoryConfigService_19869383f12.addConfigurationListener($IRepositoryConfigService_19869383f12.java) ~[?:?]
	at com.polarion.platform.repository.external.internal.ExternalRepositoryProviderRegistry.initialize(ExternalRepositoryProviderRegistry.java:146) ~[platform-repository.jar:?]
	at $IExternalRepositoryProviderRegistry_19869383fce.initialize($IExternalRepositoryProviderRegistry_19869383fce.java) ~[?:?]
	at $IExternalRepositoryProviderRegistry_19869383fcd.initialize($IExternalRepositoryProviderRegistry_19869383fcd.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule.initModule(RevisionsPersistenceModule.java:353) ~[platform-persistence.jar:?]
	at $IObjectPersistenceModule_198693840bd.initModule($IObjectPersistenceModule_198693840bd.java) ~[?:?]
	at com.polarion.subterra.persistence.internal.PersistenceEngine.initModule(PersistenceEngine.java:251) ~[subterra-uniform-persistence.jar:?]
	at $IPersistenceEngine_198693840a5.initModule($IPersistenceEngine_198693840a5.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.LowLevelDataService.boot(LowLevelDataService.java:352) ~[platform-persistence.jar:?]
	at $ILowLevelPersistence_19869383fca.boot($ILowLevelPersistence_19869383fca.java) ~[?:?]
	at $ILowLevelPersistence_19869383fc9.boot($ILowLevelPersistence_19869383fc9.java) ~[?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.lambda$0(PlatformService.java:294) ~[launcher.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:423) [?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:69) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:417) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:407) [platform.jar:?]
	at $ISecurityService_19869383f05.doAsSystemUser($ISecurityService_19869383f05.java) [?:?]
	at $ISecurityService_19869383f04.doAsSystemUser($ISecurityService_19869383f04.java) [?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.bootPlatform(PlatformService.java:286) [launcher.jar:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.start(PlatformService.java:92) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.runImpl(PolarionSVNApplication.java:139) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.run(PolarionSVNApplication.java:94) [launcher.jar:?]
	at com.polarion.core.boot.launchers.BasicAppLauncher.launch(BasicAppLauncher.java:53) [boot.jar:?]
	at com.polarion.core.boot.impl.AppLaunchersManager.start(AppLaunchersManager.java:184) [boot.jar:?]
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196) [org.eclipse.equinox.app_1.3.500.v20171221-2204.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:388) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:243) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:656) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:592) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.run(Main.java:1498) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.main(Main.java:1471) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
Caused by: com.thoughtworks.xstream.converters.ConversionException: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:77) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
Caused by: com.thoughtworks.xstream.mapper.CannotResolveClassException: AutoBranchGitLab
	at com.thoughtworks.xstream.mapper.DefaultMapper.realClass(DefaultMapper.java:81) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.DynamicProxyMapper.realClass(DynamicProxyMapper.java:55) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.PackageAliasingMapper.realClass(PackageAliasingMapper.java:88) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ClassAliasingMapper.realClass(ClassAliasingMapper.java:79) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ArrayMapper.realClass(ArrayMapper.java:74) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.SecurityMapper.realClass(SecurityMapper.java:71) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.CachingMapper.realClass(CachingMapper.java:47) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.util.HierarchicalStreams.readClassType(HierarchicalStreams.java:29) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readBareItem(AbstractCollectionConverter.java:131) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readItem(AbstractCollectionConverter.java:117) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readCompleteItem(AbstractCollectionConverter.java:147) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.ArrayConverter.unmarshal(ArrayConverter.java:54) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:72) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
2025-08-02 13:19:16,811 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-02 13:19:16,832 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions from repository default in context ContextId[context [global]] finished
2025-08-02 13:19:16,833 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [FINISHED].
2025-08-02 13:19:16,833 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.38 s. ]
2025-08-02 13:19:16,833 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.315 s [100% getReadConfiguration (48x)] (48x), svn: 0.123 s [83% info (18x)] (38x)
2025-08-02 13:19:16,907 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:19:16,907 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-02 13:19:16,907 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting repository for build artifacts-related changes
2025-08-02 13:19:16,907 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[context [global]]
2025-08-02 13:19:16,907 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[context [global]]
2025-08-02 13:19:16,910 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[context [global]] has been successfully processed
2025-08-02 13:19:16,917 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[context [global]] finished [ TIME 0.00933 s. ]
2025-08-02 13:19:16,917 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-02 13:19:16,917 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context [global]]
2025-08-02 13:19:16,917 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context [global]]
2025-08-02 13:19:16,945 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context [global]] has been successfully processed
2025-08-02 13:19:16,986 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context [global]] finished [ TIME 0.0695 s. ]
2025-08-02 13:19:16,986 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-02 13:19:16,986 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBS]
2025-08-02 13:19:16,986 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBS]
2025-08-02 13:19:17,019 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBS] has been successfully processed
2025-08-02 13:19:17,057 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBS] finished [ TIME 0.07 s. ]
2025-08-02 13:19:17,057 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-02 13:19:17,057 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBSdev]
2025-08-02 13:19:17,057 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBSdev]
2025-08-02 13:19:17,088 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBSdev] has been successfully processed
2025-08-02 13:19:17,122 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBSdev] finished [ TIME 0.0657 s. ]
2025-08-02 13:19:17,123 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-02 13:19:17,123 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context --Demo Projects]
2025-08-02 13:19:17,123 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context --Demo Projects]
2025-08-02 13:19:17,142 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context --Demo Projects] has been successfully processed
2025-08-02 13:19:17,166 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context --Demo Projects] finished [ TIME 0.0433 s. ]
2025-08-02 13:19:17,166 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-02 13:19:17,166 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context library]
2025-08-02 13:19:17,166 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context library]
2025-08-02 13:19:17,187 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context library] has been successfully processed
2025-08-02 13:19:17,212 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context library] finished [ TIME 0.0453 s. ]
2025-08-02 13:19:17,212 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-02 13:19:17,212 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context hesai]
2025-08-02 13:19:17,212 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context hesai]
2025-08-02 13:19:17,238 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context hesai] has been successfully processed
2025-08-02 13:19:17,270 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context hesai] finished [ TIME 0.0578 s. ]
2025-08-02 13:19:17,270 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-02 13:19:17,270 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context elibrary]
2025-08-02 13:19:17,270 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context elibrary]
2025-08-02 13:19:17,304 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context elibrary] has been successfully processed
2025-08-02 13:19:17,344 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context elibrary] finished [ TIME 0.0745 s. ]
2025-08-02 13:19:17,344 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-02 13:19:17,344 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context drivepilot]
2025-08-02 13:19:17,344 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context drivepilot]
2025-08-02 13:19:17,368 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context drivepilot] has been successfully processed
2025-08-02 13:19:17,410 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context drivepilot] finished [ TIME 0.0659 s. ]
2025-08-02 13:19:17,411 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-02 13:19:17,411 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... repository inspection finished [ TIME 0.504 s. ]
2025-08-02 13:19:17,411 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.58 s. ]
2025-08-02 13:19:17,411 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.424 s [77% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.328 s [100% getReadConfiguration (54x)] (54x)
2025-08-02 13:19:17,411 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:19:17,411 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-02 13:19:17,411 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting BIR for new or removed builds
2025-08-02 13:19:17,432 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were removed (including calculations from previous run)
2025-08-02 13:19:17,432 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were added or modified
2025-08-02 13:19:17,432 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... BIR inspection finished [ TIME 0.0208 s. ]
2025-08-02 13:19:17,432 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-02 13:19:17,433 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:19:17,433 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-02 13:19:17,433 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing startup index events, starting iterations.
2025-08-02 13:19:17,433 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Iteration 1 - processing 5 events
2025-08-02 13:19:17,443 [main | u:p] INFO  com.polarion.alm.tracker.internal.planning.PlanFieldsProvider - livePlanXMLLocation: Location[path /default/.reports/xml/live-plan.xml]
2025-08-02 13:19:17,481 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener -  - reindexing 1 existing objects and 0 deleted objects.
2025-08-02 13:19:17,587 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-02 13:19:17,588 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPage
2025-08-02 13:19:17,588 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPageAttachment
2025-08-02 13:19:17,588 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-08-02 13:19:17,595 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-02 13:19:17,595 [main | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-08-02 13:19:17,630 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-08-02 13:19:17,630 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPageAttachment
2025-08-02 13:19:17,630 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPage
2025-08-02 13:19:17,630 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-08-02 13:19:17,630 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Attachment
2025-08-02 13:19:17,634 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Attachment
2025-08-02 13:19:17,636 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-08-02 13:19:17,647 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: UserGroup
2025-08-02 13:19:17,647 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BaselineCollection
2025-08-02 13:19:17,647 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem
2025-08-02 13:19:17,648 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: UserGroup
2025-08-02 13:19:17,649 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRun
2025-08-02 13:19:17,655 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BaselineCollection
2025-08-02 13:19:17,656 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleComment
2025-08-02 13:19:17,658 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRun
2025-08-02 13:19:17,658 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Build
2025-08-02 13:19:17,661 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Build
2025-08-02 13:19:17,661 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Comment
2025-08-02 13:19:17,663 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleComment
2025-08-02 13:19:17,663 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BuildArtifact
2025-08-02 13:19:17,663 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: DocumentWorkflowSignature
2025-08-02 13:19:17,664 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BuildArtifact
2025-08-02 13:19:17,664 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPageAttachment
2025-08-02 13:19:17,665 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Comment
2025-08-02 13:19:17,666 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPageAttachment
2025-08-02 13:19:17,666 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-08-02 13:19:17,666 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: DocumentWorkflowSignature
2025-08-02 13:19:17,670 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem
2025-08-02 13:19:17,670 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-08-02 13:19:17,671 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Baseline
2025-08-02 13:19:17,675 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Baseline
2025-08-02 13:19:17,676 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Plan
2025-08-02 13:19:17,677 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkRecord
2025-08-02 13:19:17,682 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Plan
2025-08-02 13:19:17,682 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-08-02 13:19:17,683 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunAttachment
2025-08-02 13:19:17,682 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem-OutlineNumbers
2025-08-02 13:19:17,684 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem-OutlineNumbers
2025-08-02 13:19:17,684 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkRecord
2025-08-02 13:19:17,685 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleAttachment
2025-08-02 13:19:17,685 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunAttachment
2025-08-02 13:19:17,685 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunComment
2025-08-02 13:19:17,686 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-08-02 13:19:17,686 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPage
2025-08-02 13:19:17,687 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunComment
2025-08-02 13:19:17,687 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-08-02 13:19:17,687 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Module
2025-08-02 13:19:17,690 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.125 s, CPU [user: 0.0386 s, system: 0.0126 s], Allocated memory: 9.5 MB
2025-08-02 13:19:17,691 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-08-02 13:19:17,694 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-08-02 13:19:17,699 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPage
2025-08-02 13:19:17,703 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Revision
2025-08-02 13:19:17,703 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}272
2025-08-02 13:19:17,703 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}274
2025-08-02 13:19:17,703 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}273
2025-08-02 13:19:17,703 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}270
2025-08-02 13:19:17,703 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}271
2025-08-02 13:19:17,703 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}224
2025-08-02 13:19:17,703 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}225
2025-08-02 13:19:17,703 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}226
2025-08-02 13:19:17,703 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}228
2025-08-02 13:19:17,703 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}230
2025-08-02 13:19:17,703 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}231
2025-08-02 13:19:17,703 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}232
2025-08-02 13:19:17,704 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}233
2025-08-02 13:19:17,704 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}234
2025-08-02 13:19:17,704 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}235
2025-08-02 13:19:17,704 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}239
2025-08-02 13:19:17,704 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}240
2025-08-02 13:19:17,704 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}241
2025-08-02 13:19:17,704 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}242
2025-08-02 13:19:17,704 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}243
2025-08-02 13:19:17,705 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}245
2025-08-02 13:19:17,705 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}246
2025-08-02 13:19:17,705 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}248
2025-08-02 13:19:17,705 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}249
2025-08-02 13:19:17,705 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}250
2025-08-02 13:19:17,705 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}251
2025-08-02 13:19:17,705 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}253
2025-08-02 13:19:17,705 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}254
2025-08-02 13:19:17,705 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}255
2025-08-02 13:19:17,705 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}256
2025-08-02 13:19:17,705 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}257
2025-08-02 13:19:17,705 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}258
2025-08-02 13:19:17,705 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}259
2025-08-02 13:19:17,705 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}260
2025-08-02 13:19:17,705 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}261
2025-08-02 13:19:17,705 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}262
2025-08-02 13:19:17,705 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}263
2025-08-02 13:19:17,705 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}264
2025-08-02 13:19:17,707 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}265
2025-08-02 13:19:17,707 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}267
2025-08-02 13:19:17,708 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}268
2025-08-02 13:19:17,708 [PolarionDocIdCreator-2] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}269
2025-08-02 13:19:17,710 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleAttachment
2025-08-02 13:19:17,720 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Module
2025-08-02 13:19:17,721 [PolarionDocIdCreator-1] INFO  com.polarion.subterra.index.impl.lucene.baseline.PolarionDocIdCreator - Bloom filter loading for 28 indices took  [ TIME 0.158 s. ]
2025-08-02 13:19:17,727 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.DelegatingCalculatedFieldsListener - Calculated fields mode: async
2025-08-02 13:19:17,729 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing took  [ TIME 0.296 s. ]
2025-08-02 13:19:17,730 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.3 s. ]
2025-08-02 13:19:17,730 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.296 s [100% doFinishStartup (1x)] (1x), commit: 0.0689 s [100% Revision (1x)] (1x), Lucene: 0.0346 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0309 s [100% objectsToInv (1x)] (1x)
2025-08-02 13:19:17,730 [main | u:p] INFO  com.polarion.platform.internal.service.repository.ListenerManager - Starting the pulling job for repository: default
2025-08-02 13:19:17,730 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization finished [ TIME 2.29 s. ]
2025-08-02 13:19:17,730 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:19:17,730 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-02 13:19:17,731 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.CalculatedFieldsStorage - Checking integrity of calculated fields storage /opt/polarion/data/workspace/polarion-data/calculated-fields
2025-08-02 13:19:17,741 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-02 13:19:17,741 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:19:17,741 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-02 13:19:17,770 [main | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - Updating local scheduler state: start
2025-08-02 13:19:17,780 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-08-02 13:19:17,786 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-02 13:19:17,786 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-08-02 13:19:17,786 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-08-02 13:19:17,786 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutting down.
2025-08-02 13:19:17,787 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED paused.
2025-08-02 13:19:17,787 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-08-02 13:19:17,788 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-08-02 13:19:17,789 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-02 13:19:17,789 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-08-02 13:19:17,789 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-08-02 13:19:17,790 [main | u:p | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - 15 scheduled job(s) configured
2025-08-02 13:19:17,795 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
2025-08-02 13:19:18,019 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot finished
2025-08-02 13:19:18,019 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform started
2025-08-02 13:19:18,031 [main] INFO  com.polarion.portal.tomcat.TomcatPlugin - Tomcat home directory was set to /opt/polarion/data/workspace/.metadata/.plugins/com.polarion.portal.tomcat
2025-08-02 13:19:18,040 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Starting Tomcat...
2025-08-02 13:19:18,125 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: webui, contextRoot: webapp/webui, plugin: com.polarion.alm.ui, priority: -10]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,126 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion, contextRoot: webapp/authapp, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,126 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/.well-known, contextRoot: webapp/well-known, plugin: com.polarion.platform, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,126 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ProjectPlanGantt, contextRoot: webapp, plugin: com.polarion.alm.ProjectPlanGantt_new, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,126 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/activate, contextRoot: webapp/activation, plugin: com.polarion.psvn.launcher, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,126 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/announcements, contextRoot: webapp/announcements, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,126 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/bir, contextRoot: webapp/bir, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,126 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/checklist, contextRoot: src/main/webapp, plugin: com.fasnote.alm.checklist, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,126 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/codemirror-modes, contextRoot: webapp/codemirror-modes, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,126 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/defect, contextRoot: webapp, plugin: com.finething.hesai.defect, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,126 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/doorsconnector, contextRoot: webapp, plugin: com.polarion.synchronizer, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,126 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/export, contextRoot: webapp/export, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,127 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/fileupload, contextRoot: webapp/fileupload, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,127 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/gwt, contextRoot: war, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,127 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/hesai-ai, contextRoot: webapp, plugin: com.finething.hesai.ai, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,127 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/icons, contextRoot: webapp/icons, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,127 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/internal-login, contextRoot: webapp/internal-login, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,127 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/livechecklist, contextRoot: webapp, plugin: com.teamlive.livechecklist, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,127 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/module-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,128 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/modulehome, contextRoot: webapp/module-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,128 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/mxgraph, contextRoot: draw.io/war, plugin: com.polarion.alm.ui.diagrams.mxgraph, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,128 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/oauth-feishu, contextRoot: webapp, plugin: com.fasnote.alm.auth.feishu, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,128 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/page-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,128 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/perf-testing, contextRoot: webapp/perf-testing, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,128 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/plugin-manage, contextRoot: webapp, plugin: com.fasnote.alm.plugin.manage, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,128 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/print, contextRoot: webapp/print, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,128 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/register, contextRoot: webapp/register, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,128 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rest, contextRoot: webapp, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,128 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ria, contextRoot: webapp/ria, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,128 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/richpagehome, contextRoot: webapp/richpage-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,129 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt, contextRoot: src/main/webapp, plugin: com.siemens.polarion.rt, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,129 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt-connect, contextRoot: ws, plugin: com.siemens.polarion.rt.communication.polarion, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,130 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/scripting, contextRoot: webapp/scripting, plugin: com.polarion.scripting.servlet, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,130 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/sdk, contextRoot: webapp/sdk, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,130 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/svnwebclient, contextRoot: webapp, plugin: org.polarion.svnwebclient, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,130 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/swagger, contextRoot: webapp/swagger, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,130 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/synchronizer, contextRoot: webapp, plugin: com.polarion.synchronizer.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,130 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/template-download, contextRoot: webapp/project-template, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,130 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/testrun-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,130 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/tour, contextRoot: webapp/tour, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,130 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/watermark, contextRoot: webapp, plugin: com.fasnote.alm.watermark, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,130 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,130 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment-auth, contextRoot: webapp/wi-attachment-auth, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,130 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/widget-resource, contextRoot: webapp/widget-resource, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,131 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wiki, contextRoot: src/main/webapp, plugin: com.polarion.alm.wiki, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,131 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/workreport, contextRoot: webapp/workreport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,131 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ws, contextRoot: ws, plugin: com.polarion.alm.ws, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,131 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/xunitimport, contextRoot: webapp/xunitimport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,131 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/oslc, contextRoot: webapp, plugin: com.polarion.alm.oslc, priority: 1]'; protocol: AJP/1.3, port: 8889
2025-08-02 13:19:18,194 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Initializing ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-08-02 13:19:18,205 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-02 13:19:18,205 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-08-02 13:19:18,226 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@53b60254] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,226 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3bccdd96] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,226 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6ec5c03] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,226 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@65edb1b0] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,226 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7e128b54] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,226 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4b6be6fc] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,246 [Catalina-utility-3] INFO  org.apache.catalina.startup.ContextConfig - No global web.xml found
2025-08-02 13:19:18,259 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,260 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,261 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,271 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [admin] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,271 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,308 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6ae66f52] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,311 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6ac301e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,313 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@590b7982] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,314 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,317 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@536889a8] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,320 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6a29807f] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,320 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@44c262d0] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,321 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,324 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@59f52bca] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,324 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1d0a0d4e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,327 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,329 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,331 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,332 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,336 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@23159091] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,339 [Catalina-utility-5] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 13:19:18,341 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@194c02c3] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,342 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4ff25596] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,345 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@24b535c9] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,348 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,349 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,355 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@df0a356] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,357 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2568d45a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,358 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2a45c12] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,358 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@e3ce861] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,364 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,368 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@79db9c25] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,369 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,373 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1a334847] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,373 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1b3ea198] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,376 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,377 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,377 [Catalina-utility-1] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 13:19:18,377 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,384 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6b4483a4] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,384 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@67b8b180] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,390 [Catalina-utility-6] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/oauth-feishu] - For security constraints with URL pattern [/userinfo] only the HTTP methods [POST GET] are covered. All other methods are uncovered.
2025-08-02 13:19:18,393 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5e51a73c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,396 [Catalina-utility-6] INFO  com.fasnote.alm.injection.osgi.InjectionActivator - 启动ALM依赖注入框架
2025-08-02 13:19:18,404 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1777a76a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,416 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@374a49be] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,426 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@52f6f7c0] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,426 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,432 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@35730a4d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,432 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 创建OSGi感知的纯粹依赖注入器
2025-08-02 13:19:18,432 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 初始化包扫描提供者跟踪机制
2025-08-02 13:19:18,432 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 包扫描提供者跟踪机制初始化完成
2025-08-02 13:19:18,432 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 开始注册 OSGi 服务到依赖注入容器
2025-08-02 13:19:18,437 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: org.osgi.framework.BundleContext
2025-08-02 13:19:18,437 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - OSGi 服务注册完成，已注册 1 个 OSGi 服务提供者
2025-08-02 13:19:18,437 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.osgi.InjectionActivator - 启动Bundle跟踪机制
2025-08-02 13:19:18,441 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,443 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 包扫描提供者跟踪已启动
2025-08-02 13:19:18,443 [Catalina-utility-6] INFO  com.fasnote.alm.injection.osgi.InjectionActivator - 包扫描提供者跟踪机制启动成功
2025-08-02 13:19:18,443 [Catalina-utility-6] INFO  com.fasnote.alm.injection.osgi.InjectionActivator - ALM依赖注入框架启动成功
2025-08-02 13:19:18,444 [Catalina-utility-6] INFO  com.fasnote.alm.auth.feishu.Activator - Feishu Authentication Plugin starting...
2025-08-02 13:19:18,446 [Catalina-utility-6] INFO  com.fasnote.alm.auth.feishu.Activator - 注册 FeishuPackageScanProvider 为 OSGi 服务...
2025-08-02 13:19:18,447 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,448 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.auth.feishu 注册DI扫描包路径: [com.fasnote.alm.auth.feishu]
2025-08-02 13:19:18,448 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.auth.feishu 就绪，开始扫描服务 (状态: 8, 扫描包: [com.fasnote.alm.auth.feishu])
2025-08-02 13:19:18,448 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasnote.alm.auth.feishu
2025-08-02 13:19:18,448 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 开始扫描 Bundle 服务: com.fasnote.alm.auth.feishu (状态: 8, 扫描包: [com.fasnote.alm.auth.feishu])
2025-08-02 13:19:18,452 [Catalina-utility-1] INFO  org.apache.tomcat.dbcp.dbcp2.BasicDataSourceFactory - Name = XWikiDS Ignoring unknown property: value of "DB Connection" for "description" property
2025-08-02 13:19:18,456 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.auth.feishu 的包 com.fasnote.alm.auth.feishu 中发现 19 个类文件
2025-08-02 13:19:18,457 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory$FeishuAuthenticatorManagerInvocationHandler (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,458 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuOAuth2LoginClient (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,458 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,459 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.config.FeishuConfigurationAdapter (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,473 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuOAuth2Authenticator (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,474 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,474 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,476 [Catalina-utility-6] INFO  com.fasnote.alm.plugin.manage.Activator - 启动许可证管理插件...
2025-08-02 13:19:18,477 [Catalina-utility-6] DEBUG com.fasnote.alm.plugin.manage.Activator - 初始化许可证配置...
2025-08-02 13:19:18,478 [Catalina-utility-6] DEBUG com.fasnote.alm.plugin.manage.Activator - 从环境变量加载配置完成
2025-08-02 13:19:18,478 [Catalina-utility-6] DEBUG com.fasnote.alm.plugin.manage.Activator - 许可证配置初始化完成
2025-08-02 13:19:18,478 [Catalina-utility-6] INFO  com.fasnote.alm.plugin.manage.Activator - 注册 LicensePackageScanProvider 为 OSGi 服务...
2025-08-02 13:19:18,479 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.plugin.manage 注册DI扫描包路径: [com.fasnote.alm.plugin.manage.injection.module]
2025-08-02 13:19:18,479 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.plugin.manage 就绪，开始扫描服务 (状态: 8, 扫描包: [com.fasnote.alm.plugin.manage.injection.module])
2025-08-02 13:19:18,479 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,479 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 开始扫描 Bundle 服务: com.fasnote.alm.plugin.manage (状态: 8, 扫描包: [com.fasnote.alm.plugin.manage.injection.module])
2025-08-02 13:19:18,480 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.plugin.manage 的包 com.fasnote.alm.plugin.manage.injection.module 中发现 5 个类文件
2025-08-02 13:19:18,481 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,481 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 发现 IModule 实现: com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule (Bundle: com.fasnote.alm.plugin.manage)
2025-08-02 13:19:18,481 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功使用无参构造函数创建模块: com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule
2025-08-02 13:19:18,506 [Catalina-utility-6] INFO  com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule - 配置插件管理主模块...
2025-08-02 13:19:18,509 [Catalina-utility-6] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseModule使用无参构造函数创建
2025-08-02 13:19:18,509 [Catalina-utility-6] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 配置统一许可证模块（重构后）...
2025-08-02 13:19:18,509 [Catalina-utility-6] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 注册核心组件...
2025-08-02 13:19:18,511 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: java.util.Map
2025-08-02 13:19:18,527 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,527 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.security.MachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,527 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,527 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.security.MachineCodeProvider, 实现: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,556 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.security.MachineCodeProvider -> com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider  [LAZY_SINGLETON]
2025-08-02 13:19:18,561 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,561 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,561 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,561 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.config.LicenseConfiguration, 实例类: com.fasnote.alm.plugin.manage.config.LicenseConfiguration, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,561 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage.config.LicenseConfiguration, 实例类型: LicenseConfiguration
2025-08-02 13:19:18,561 [Catalina-utility-6] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 核心组件注册完成
2025-08-02 13:19:18,561 [Catalina-utility-6] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 注册管理器组件...
2025-08-02 13:19:18,565 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.BundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,565 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IBundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,566 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.BundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,566 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IBundleManager, 实现: com.fasnote.alm.plugin.manage.core.BundleManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,566 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IBundleManager -> com.fasnote.alm.plugin.manage.core.BundleManager  [LAZY_SINGLETON]
2025-08-02 13:19:18,567 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.ClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,567 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,567 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.ClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,567 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IClassLoaderManager, 实现: com.fasnote.alm.plugin.manage.core.ClassLoaderManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,568 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IClassLoaderManager -> com.fasnote.alm.plugin.manage.core.ClassLoaderManager  [LAZY_SINGLETON]
2025-08-02 13:19:18,568 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,568 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,568 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,568 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseValidator, 实现: com.fasnote.alm.plugin.manage.core.LicenseValidator, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,569 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseValidator -> com.fasnote.alm.plugin.manage.core.LicenseValidator  [LAZY_SINGLETON]
2025-08-02 13:19:18,570 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,570 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,570 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,570 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager, 实现: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,570 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager -> com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager  [LAZY_SINGLETON]
2025-08-02 13:19:18,570 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,571 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,571 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,571 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator, 实现: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,571 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator  [LAZY_SINGLETON]
2025-08-02 13:19:18,573 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,573 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,573 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,573 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager, 实现: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,573 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager  [LAZY_SINGLETON]
2025-08-02 13:19:18,574 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,575 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,575 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,575 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseFileManager, 实现: com.fasnote.alm.plugin.manage.core.LicenseFileManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,575 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseFileManager -> com.fasnote.alm.plugin.manage.core.LicenseFileManager  [LAZY_SINGLETON]
2025-08-02 13:19:18,575 [Catalina-utility-6] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 管理器组件注册完成
2025-08-02 13:19:18,577 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,577 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,577 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,577 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseManager, 实现: com.fasnote.alm.plugin.manage.core.LicenseManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,578 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseManager -> com.fasnote.alm.plugin.manage.core.LicenseManager  [LAZY_SINGLETON]
2025-08-02 13:19:18,578 [Catalina-utility-6] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseManager接口绑定配置完成
2025-08-02 13:19:18,578 [Catalina-utility-6] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseAwareServiceResolver 将通过自动扫描机制注册
2025-08-02 13:19:18,579 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: com.fasnote.alm.plugin.manage.api.LicenseAware
2025-08-02 13:19:18,580 [Catalina-utility-6] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 统一许可证模块配置完成（重构后）
2025-08-02 13:19:18,583 [Catalina-utility-6] INFO  com.fasnote.alm.plugin.manage.web.injection.WebModule - 配置Web层依赖注入模块...
2025-08-02 13:19:18,583 [Catalina-utility-6] DEBUG com.fasnote.alm.plugin.manage.web.injection.WebModule - 注册Web服务...
2025-08-02 13:19:18,585 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,585 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,585 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,585 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl, 实现: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,585 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl  [LAZY_SINGLETON]
2025-08-02 13:19:18,585 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,585 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.web.service.PluginManagementService -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,585 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,585 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.web.service.PluginManagementService, 实现: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,585 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.web.service.PluginManagementService -> com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl  [LAZY_SINGLETON]
2025-08-02 13:19:18,585 [Catalina-utility-6] DEBUG com.fasnote.alm.plugin.manage.web.injection.WebModule - Web服务注册完成
2025-08-02 13:19:18,585 [Catalina-utility-6] INFO  com.fasnote.alm.plugin.manage.web.injection.WebModule - Web层依赖注入模块配置完成
2025-08-02 13:19:18,585 [Catalina-utility-6] INFO  com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule - 插件管理主模块配置完成
2025-08-02 13:19:18,585 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 已安装模块: PluginManageMainModule (优先级: 0)
2025-08-02 13:19:18,585 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功安装模块: com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule
2025-08-02 13:19:18,585 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.LicensePackageScanProvider (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,586 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.LicenseModule$LicenseAwareProvider$1 (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,586 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.LicenseModule$LicenseAwareProvider (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,586 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.LicenseModule (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,586 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 发现 IModule 实现: com.fasnote.alm.plugin.manage.injection.module.LicenseModule (Bundle: com.fasnote.alm.plugin.manage)
2025-08-02 13:19:18,586 [Catalina-utility-6] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseModule使用无参构造函数创建
2025-08-02 13:19:18,586 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功使用无参构造函数创建模块: com.fasnote.alm.plugin.manage.injection.module.LicenseModule
2025-08-02 13:19:18,586 [Catalina-utility-6] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 配置统一许可证模块（重构后）...
2025-08-02 13:19:18,586 [Catalina-utility-6] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 注册核心组件...
2025-08-02 13:19:18,586 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: java.util.Map
2025-08-02 13:19:18,586 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,586 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.security.MachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,586 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,587 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.security.MachineCodeProvider, 实现: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.security.MachineCodeProvider -> com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider  [LAZY_SINGLETON]
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.config.LicenseConfiguration, 实例类: com.fasnote.alm.plugin.manage.config.LicenseConfiguration, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage.config.LicenseConfiguration, 实例类型: LicenseConfiguration
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 核心组件注册完成
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 注册管理器组件...
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.BundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IBundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.BundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IBundleManager, 实现: com.fasnote.alm.plugin.manage.core.BundleManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IBundleManager -> com.fasnote.alm.plugin.manage.core.BundleManager  [LAZY_SINGLETON]
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.ClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.ClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IClassLoaderManager, 实现: com.fasnote.alm.plugin.manage.core.ClassLoaderManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IClassLoaderManager -> com.fasnote.alm.plugin.manage.core.ClassLoaderManager  [LAZY_SINGLETON]
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseValidator, 实现: com.fasnote.alm.plugin.manage.core.LicenseValidator, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseValidator -> com.fasnote.alm.plugin.manage.core.LicenseValidator  [LAZY_SINGLETON]
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager, 实现: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager -> com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager  [LAZY_SINGLETON]
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator, 实现: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator  [LAZY_SINGLETON]
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager, 实现: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager  [LAZY_SINGLETON]
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseFileManager, 实现: com.fasnote.alm.plugin.manage.core.LicenseFileManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseFileManager -> com.fasnote.alm.plugin.manage.core.LicenseFileManager  [LAZY_SINGLETON]
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 管理器组件注册完成
2025-08-02 13:19:18,589 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,590 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,590 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,590 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseManager, 实现: com.fasnote.alm.plugin.manage.core.LicenseManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 13:19:18,590 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseManager -> com.fasnote.alm.plugin.manage.core.LicenseManager  [LAZY_SINGLETON]
2025-08-02 13:19:18,590 [Catalina-utility-6] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseManager接口绑定配置完成
2025-08-02 13:19:18,590 [Catalina-utility-6] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseAwareServiceResolver 将通过自动扫描机制注册
2025-08-02 13:19:18,590 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: com.fasnote.alm.plugin.manage.api.LicenseAware
2025-08-02 13:19:18,590 [Catalina-utility-6] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 统一许可证模块配置完成（重构后）
2025-08-02 13:19:18,590 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 已安装模块: RefactoredLicenseModule (优先级: 5)
2025-08-02 13:19:18,590 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功安装模块: com.fasnote.alm.plugin.manage.injection.module.LicenseModule
2025-08-02 13:19:18,590 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle 服务扫描完成: com.fasnote.alm.plugin.manage - 耗时: 111ms, 模块: 2, 服务: 0, 错误: 0
2025-08-02 13:19:18,590 [Catalina-utility-6] INFO  com.fasnote.alm.plugin.manage.Activator - LicensePackageScanProvider OSGi 服务注册成功
2025-08-02 13:19:18,590 [Catalina-utility-6] INFO  com.fasnote.alm.plugin.manage.Activator - 扫描包路径: [com.fasnote.alm.plugin.manage.injection.module]
2025-08-02 13:19:18,590 [Catalina-utility-6] INFO  com.fasnote.alm.plugin.manage.Activator - 许可证管理插件启动成功
2025-08-02 13:19:18,591 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 在注解 com.fasnote.alm.plugin.manage.annotation.LicenseImplementation 中发现 @Service 元注解
2025-08-02 13:19:18,591 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 发现服务相关注解: LicenseImplementation 在类 com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer
2025-08-02 13:19:18,591 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 发现服务类: com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu)
2025-08-02 13:19:18,593 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 自动注册服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu)
2025-08-02 13:19:18,600 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 设置默认服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer
2025-08-02 13:19:18,600 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 添加服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer (总数: 1)
2025-08-02 13:19:18,600 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu
2025-08-02 13:19:18,600 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu
2025-08-02 13:19:18,601 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuStateUtils (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,602 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuUserInfoMapper (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,602 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuUserInfoMapper$FeishuUserData (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,603 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuApiClient (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,603 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuUserInfoMapper$FeishuApiResponse (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,603 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.OAuth2UrlHandler (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,604 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuOAuth2LoginClient$1 (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,606 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuOAuth2DefaultLoginHandler (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,606 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuUserInfoServlet (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,606 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,609 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 在注解 com.fasnote.alm.plugin.manage.annotation.FallbackImplementation 中发现 @Service 元注解
2025-08-02 13:19:18,609 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 发现服务相关注解: FallbackImplementation 在类 com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer
2025-08-02 13:19:18,609 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 发现服务类: com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu)
2025-08-02 13:19:18,609 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 自动注册服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu)
2025-08-02 13:19:18,609 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 添加服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer (总数: 2)
2025-08-02 13:19:18,609 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu
2025-08-02 13:19:18,609 [Catalina-utility-1] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 13:19:18,609 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu
2025-08-02 13:19:18,609 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuPackageScanProvider (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,609 [Catalina-utility-6] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.Activator (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 13:19:18,609 [Catalina-utility-6] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle 服务扫描完成: com.fasnote.alm.auth.feishu - 耗时: 161ms, 模块: 0, 服务: 2, 错误: 0
2025-08-02 13:19:18,610 [Catalina-utility-6] INFO  com.fasnote.alm.auth.feishu.Activator - FeishuPackageScanProvider OSGi 服务注册成功
2025-08-02 13:19:18,610 [Catalina-utility-6] INFO  com.fasnote.alm.auth.feishu.Activator - 扫描包路径: [com.fasnote.alm.auth.feishu]
2025-08-02 13:19:18,610 [Catalina-utility-6] INFO  com.fasnote.alm.auth.feishu.Activator - Feishu Authentication Plugin started successfully
2025-08-02 13:19:18,610 [Catalina-utility-6] INFO  com.fasnote.alm.auth.feishu.Activator - 飞书插件实现类将通过 OSGi 服务自动扫描注册
2025-08-02 13:19:18,628 [Catalina-utility-3] INFO  org.polarion.svncommons.commentscache.CommentsCache - Initializing comments cache. Id: http://localhost/repo, repository: http://localhost/repo/, url: http://localhost/repo/, cache directory: /opt/polarion/data/workspace/polarion-data/log-messages-cache, cache page size: 100
2025-08-02 13:19:18,669 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@198af3c8] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,676 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@445d26fd] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,683 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,684 [Catalina-utility-3] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 13:19:18,736 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@794d0492] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,741 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,780 [Catalina-utility-1] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 13:19:18,795 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3ac731b5] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,803 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,814 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@60f6a081] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,823 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:18,831 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3a077eb] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:18,884 [Catalina-utility-1] INFO  com.polarion.portal.velocity.VelocityPathManager - VelocityTemplatesPath=/opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/authapp/, /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/, /opt/polarion/polarion/plugins/com.polarion.alm.wiki_3.22.1/src/main/webapp/, ., /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/
2025-08-02 13:19:19,167 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2adb2ef4] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:19,173 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1fbd9e5f] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:19,174 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@364b80c1] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:19,179 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:19,185 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6fd2acf6] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:19,199 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:19,204 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@40b415c0] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:19,211 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@697b1b46] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:19,215 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@27a60d16] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:19,218 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:19,221 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5b3ab554] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:19,225 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@41850d76] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:19,226 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:19,227 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3de6a235] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:19,229 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 13:19:19,239 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@581f551f] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:19,256 [Catalina-utility-2] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 13:19:19,290 [Catalina-utility-4] INFO  com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Spring context refreshed, scanning for prompt variables...
2025-08-02 13:19:19,302 [Catalina-utility-4] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: polarionTool, Class: com.finething.hesai.ai.util.VelocityPolarionTool, Methods found: 29
2025-08-02 13:19:19,305 [Catalina-utility-4] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: enumTool, Class: com.finething.hesai.ai.util.EnumUtil, Methods found: 1
2025-08-02 13:19:19,306 [Catalina-utility-4] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: linkWorkItemUtil, Class: com.finething.hesai.ai.util.LinkWorkItemUtil, Methods found: 6
2025-08-02 13:19:19,309 [Catalina-utility-4] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: polarionContext, Class: com.finething.hesai.ai.service.impl.PolarionContextServiceImpl, Methods found: 4
2025-08-02 13:19:19,309 [Catalina-utility-4] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - No methods with @MethodDescription found for variable: polarionHelper, Class: com.finething.hesai.ai.service.impl.DefaultVariableDescriptionProvider$HelperMethods
2025-08-02 13:19:19,310 [Catalina-utility-4] INFO  com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Finished scanning. Found 4 prompt variables.
2025-08-02 13:19:19,364 [Catalina-utility-5] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-08-02 13:19:19,462 [Catalina-utility-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Started.
2025-08-02 13:19:20,179 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-08-02 13:19:20,182 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[90]')
2025-08-02 13:19:20,183 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[90]')
2025-08-02 13:19:20,187 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[96]')
2025-08-02 13:19:20,390 [Catalina-utility-5] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-08-02 13:19:20,391 [Catalina-utility-5] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[90]')
2025-08-02 13:19:20,393 [Catalina-utility-5] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[92]')
2025-08-02 13:19:20,515 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Starting ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-08-02 13:19:20,524 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Tomcat is listening on port 8889 using AJP/1.3 protocol with 600000 timeout in millis
2025-08-02 13:19:20,525 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Starting Help Service...
2025-08-02 13:19:20,532 [main] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3eafc7ee] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 13:19:20,539 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Help Service started
2025-08-02 13:19:20,615 [main | u:p] INFO  com.xpn.xwiki.XWiki - xwiki.cfg taken from /WEB-INF/xwiki.cfg because the XWikiConfig variable is not set in the context
2025-08-02 13:19:21,216 [Thread-33] INFO  class com.polarion.alm.server.util.ChartExporterStartup - Chart renderer says: Server started on 127.0.0.1:34567
2025-08-02 13:19:21,622 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-08-02 13:19:21,813 [ajp-nio-127.0.0.1-8889-exec-1 | cID:69386ab7-c0a844bd-2e1c5321-2927df63] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-08-02 13:19:21,848 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-08-02 13:19:21,848 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
2025-08-02 13:19:21,928 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" has id 69386b15-c0a844bd-2e1c5321-0baee690
2025-08-02 13:19:21,931 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to UNSCHEDULED
2025-08-02 13:19:21,934 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "Attachment Indexer" is /opt/polarion/data/workspace/polarion-data/jobs/20250802-1319
2025-08-02 13:19:21,936 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" runs as user "polarion"
2025-08-02 13:19:21,939 [main | u:p] INFO  com.polarion.platform.monitoring - Next slow periodic actions will be executed on Sun Aug 03 01:00:21 CST 2025
2025-08-02 13:19:21,940 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.tracker.internal.HttpsConfiguratorStartup successfully initialized
2025-08-02 13:19:21,940 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.ChartExporterStartup successfully initialized
2025-08-02 13:19:21,941 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.wiki.WikiPlugin successfully initialized
2025-08-02 13:19:21,941 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.qcentre.internal.QCentreStartup successfully initialized
2025-08-02 13:19:21,941 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.siemens.polarion.rt.communication.connection.RtCommunicationStartup successfully initialized
2025-08-02 13:19:21,941 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.internal.startup.NotificationServerStartup successfully initialized
2025-08-02 13:19:21,941 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.subterra.index.impl.IndexingJobsStartup successfully initialized
2025-08-02 13:19:21,941 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.ui.server.ServerStartup successfully initialized
2025-08-02 13:19:21,942 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.FormulaServerStartup successfully initialized
2025-08-02 13:19:21,942 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.monitoring.internal.MonitoringServiceStart successfully initialized
2025-08-02 13:19:21,942 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to ACTIVATING
2025-08-02 13:19:21,943 [main] INFO  com.polarion.platform.monitoring - Executing actions from stage POSTBOOT
2025-08-02 13:19:21,944 [main] INFO  com.polarion.platform.monitoring - Executing action 'polarion.info.cache.statistics'
2025-08-02 13:19:21,946 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to WAITING
2025-08-02 13:19:21,963 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to RUNNING
2025-08-02 13:19:21,963 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" has id 69386b65-c0a844bd-2e1c5321-75772e38
2025-08-02 13:19:21,965 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to UNSCHEDULED
2025-08-02 13:19:21,965 [main] INFO  com.polarion.platform.monitoring - polarion.info.cache.statistics (Statistics of caches) = Statistics of caches 
                         CACHE       HITS     MISSES  HIT_RATIO       GETS       PUTS    REMOVAL   EVICTION 
     attachments-content-cache          0          0          0%          0          0          0          0 
                baseline-cache          0          0          0%          0          0          0          0 
                            bq          0          0          0%          0          0          0          0 
                dao-attachment          0          0          0%          0          0          0          0 
                   dao-comment          0          0          0%          0          0          0          0 
                    dao-global          0          4          0%          4          0          0          0 
                    dao-module          0          0          0%          0          0          0          0 
          dao-moduleattachment          0          0          0%          0          0          0          0 
             dao-modulecomment          0          0          0%          0          0          0          0 
                      dao-plan          0          0          0%          0          0          0          0 
                   dao-project          0          0          0%          0          0          0          0 
              dao-projectgroup          0          6          0%          6          3          0          0 
                  dao-richpage          0          0          0%          0          0          0          0 
        dao-richpageattachment          0          0          0%          0          0          0          0 
                   dao-testrun          0          0          0%          0          0          0          0 
         dao-testrunattachment          0          0          0%          0          0          0          0 
                      dao-user          0          0          0%          0          0          0          0 
                  dao-wikipage          0          0          0%          0          0          0          0 
        dao-wikipageattachment          0          0          0%          0          0          0          0 
                  dao-workitem          0          0          0%          0          0          0          0 
      derived-linked-revisions          0          0          0%          0          0          0          0 
                  formulas-svg          0          0          0%          0          0          0          0 
                github-commits          0          0          0%          0          0          0          0 
            historical-queries          0          0          0%          0          0          0          0 
      historical-queries-sizes          0          0          0%          0          0          0          0 
        oslc-linked-item-cache          0          0          0%          0          0          0          0 
               plan-statistics          0          0          0%          0          0          0          0 
                   rmd-default          4          5         44%          9          7          0          0 
                   ss-combined          0          0          0%          0          0          0          0 
                    ss-context          0          0          0%          0          0          0          0 
                     wiki-page          2          8         20%         10          9          1          0 
              wiki-page-exists          0          0          0%          0          9          1          0 

 [Sat Aug 02 13:19:21 CST 2025]
2025-08-02 13:19:21,966 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "DB History Creator" is /opt/polarion/data/workspace/polarion-data/jobs/20250802-1319_0
2025-08-02 13:19:21,967 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" runs as user "polarion"
2025-08-02 13:19:21,969 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to ACTIVATING
2025-08-02 13:19:21,971 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to WAITING
2025-08-02 13:19:21,975 [main] INFO  com.polarion.platform.monitoring - Finished with actions from stage POSTBOOT: {OK=1}
2025-08-02 13:19:21,978 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 4.24 s. ]
2025-08-02 13:19:21,978 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to RUNNING
2025-08-02 13:19:21,978 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.784 s [90% info (158x)] (170x)
2025-08-02 13:19:21,979 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:19:21,979 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 12.4 s. ]
2025-08-02 13:19:21,979 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:19:21,987 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to FINISHED
2025-08-02 13:19:21,988 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - Status of job "Attachment Indexer" is OK
2025-08-02 13:19:21,988 [PreLoadDataService] INFO  com.polarion.psvn.launcher.internal.data.PreLoadDataService - Preloading data ...
2025-08-02 13:19:22,011 [Thread-36] INFO  com.polarion.core.util.process.JavaRunner - Executing /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/bin/java
  -- args [-jar, /opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar, --server.port=40608, --jwksUrl=http://localhost/polarion/.well-known/jwks.json]
  -- env null
  -- dir /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess986017840457868925.tmp
2025-08-02 13:19:22,181 [Notification-Worker-6 | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem
2025-08-02 13:19:22,203 [Notification-Worker-6 | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem
2025-08-02 13:19:22,442 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661a4e1ae0c42_0_661a4e1ae0c42_0_: finished. Total: 0.454 s, CPU [user: 0.204 s, system: 0.0209 s], Allocated memory: 16.1 MB, resolve: 0.128 s [97% User (2x)] (4x), Lucene: 0.0272 s [100% search (1x)] (1x)
2025-08-02 13:19:22,451 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-08-02 13:19:22,454 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-08-02 13:19:22,458 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-08-02 13:19:22,465 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-08-02 13:19:22,487 [Activities-Bulk-Publisher] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Activities
2025-08-02 13:19:22,690 [Thread-43] INFO  class com.polarion.alm.server.util.FormulaServerStartup - Formula renderer says: Server started on 127.0.0.1:34568
2025-08-02 13:19:23,163 [Thread-40] INFO  NotificationService - 
2025-08-02 13:19:23,164 [Thread-40] INFO  NotificationService -   .   ____          _            __ _ _
2025-08-02 13:19:23,164 [Thread-40] INFO  NotificationService -  /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
2025-08-02 13:19:23,164 [Thread-40] INFO  NotificationService - ( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
2025-08-02 13:19:23,164 [Thread-40] INFO  NotificationService -  \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
2025-08-02 13:19:23,164 [Thread-40] INFO  NotificationService -   '  |____| .__|_| |_|_| |_\__, | / / / /
2025-08-02 13:19:23,164 [Thread-40] INFO  NotificationService -  =========|_|==============|___/=/_/_/_/
2025-08-02 13:19:23,167 [Thread-40] INFO  NotificationService -  :: Spring Boot ::                (v2.6.6)
2025-08-02 13:19:23,170 [Thread-40] INFO  NotificationService - 
2025-08-02 13:19:23,297 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 25, for prototypes: WorkItem; with days range: 180d, took  [ TIME 1.11 s. ] 
2025-08-02 13:19:23,304 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.14 s, CPU [user: 0.00851 s, system: 0.00173 s], Allocated memory: 356.3 kB, transactions: 1
2025-08-02 13:19:23,311 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, notification worker: 0.468 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.168 s [95% User (3x)] (6x), Lucene: 0.0586 s [46% search (1x), 41% add (1x)] (3x), Incremental Baseline: 0.0448 s [100% WorkItem (24x)] (24x), persistence listener: 0.0282 s [82% indexRefreshPersistenceListener (1x)] (7x)
2025-08-02 13:19:23,311 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Starting Application using Java 11.0.27 on zhangwendeMini2.lan with PID 37032 (/opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar started by zhangwentian in /private/var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess986017840457868925.tmp)
2025-08-02 13:19:23,312 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - No active profile set, falling back to 1 default profile: "default"
2025-08-02 13:19:23,313 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.33 s, CPU [user: 0.227 s, system: 0.0364 s], Allocated memory: 18.8 MB, transactions: 25, svn: 1.03 s [98% getDatedRevision (181x)] (183x)
2025-08-02 13:19:23,314 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to FINISHED
2025-08-02 13:19:23,315 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - Status of job "DB History Creator" is OK
2025-08-02 13:19:24,482 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 25, for prototypes: WorkItem; with days range: 180d, took  [ TIME 1.18 s. ] 
2025-08-02 13:19:24,519 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a4e1ae2445_0_661a4e1ae2445_0_: finished. Total: 2.53 s, CPU [user: 0.619 s, system: 0.146 s], Allocated memory: 51.6 MB, svn: 1.81 s [64% getDatedRevision (181x), 24% getDir2 content (25x)] (328x), resolve: 0.653 s [100% Category (117x)] (117x), ObjectMaps: 0.235 s [45% getPrimaryObjectProperty (117x), 34% getPrimaryObjectLocation (117x), 21% getLastPromoted (117x)] (473x)
2025-08-02 13:19:24,524 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-08-02 13:19:24,531 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-08-02 13:19:24,572 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-08-02 13:19:24,575 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-08-02 13:19:24,580 [Thread-40] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 40608 (http)
2025-08-02 13:19:24,591 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-40608"]
2025-08-02 13:19:24,591 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-02 13:19:24,591 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-02 13:19:24,648 [Thread-40] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-02 13:19:24,648 [Thread-40] INFO  NotificationService - [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1257 ms
2025-08-02 13:19:24,668 [Thread-40] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing AtmosphereFramework
2025-08-02 13:19:24,669 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-08-02 13:19:24,673 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-08-02 13:19:24,897 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a4e1d7ec48_0_661a4e1d7ec48_0_: finished. Total: 0.228 s, CPU [user: 0.0984 s, system: 0.0166 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.112 s [53% getReadConfiguration (180x), 47% getReadUserConfiguration (10x)] (190x), svn: 0.11 s [59% info (21x), 36% getFile content (16x)] (39x), resolve: 0.065 s [100% User (9x)] (9x), ObjectMaps: 0.0239 s [58% getPrimaryObjectProperty (8x), 23% getPrimaryObjectLocation (8x)] (32x), GlobalHandler: 0.0139 s [73% applyTxChanges (2x), 27% get (27x)] (29x)
2025-08-02 13:19:25,006 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-40608"]
2025-08-02 13:19:25,050 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using org.atmosphere.cpr.DefaultAnnotationProcessor for processing annotation
2025-08-02 13:19:25,050 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.DefaultAnnotationProcessor - AnnotationProcessor class org.atmosphere.cpr.DefaultAnnotationProcessor$BytecodeBasedAnnotationProcessor being used
2025-08-02 13:19:25,069 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AnnotationHandler - Found Annotation in class com.siemens.polarion.service.notification.NotificationService being scanned: interface org.atmosphere.config.service.ManagedService
2025-08-02 13:19:25,076 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.AtmosphereResourceLifecycleInterceptor
2025-08-02 13:19:25,077 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.client.TrackMessageSizeInterceptor
2025-08-02 13:19:25,079 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.SuspendTrackerInterceptor
2025-08-02 13:19:25,080 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.config.managed.ManagedServiceInterceptor
2025-08-02 13:19:25,087 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class com.siemens.polarion.service.notification.JwtVerificationInterceptor
2025-08-02 13:19:25,094 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.util.ForkJoinPool - Using ForkJoinPool  java.util.concurrent.ForkJoinPool. Set the org.atmosphere.cpr.broadcaster.maxAsyncWriteThreads to -1 to fully use its power.
2025-08-02 13:19:25,098 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler mapped to context-path /notification and Broadcaster Class org.atmosphere.cpr.DefaultBroadcaster
2025-08-02 13:19:25,098 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor [Atmosphere LifeCycle,  Track Message Size Interceptor using |, UUID Tracking Interceptor, @ManagedService Interceptor, @Service Event Listeners, com.siemens.polarion.service.notification.JwtVerificationInterceptor] mapped to AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler
2025-08-02 13:19:25,115 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Auto detecting WebSocketHandler in /WEB-INF/classes/
2025-08-02 13:19:25,117 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed WebSocketProtocol org.atmosphere.websocket.protocol.SimpleHttpProtocol 
2025-08-02 13:19:25,118 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.container.JSR356AsyncSupport - JSR 356 Mapping path /notification
2025-08-02 13:19:25,126 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installing Default AtmosphereInterceptors
2025-08-02 13:19:25,127 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CorsInterceptor : CORS Interceptor Support
2025-08-02 13:19:25,127 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CacheHeadersInterceptor : Default Response's Headers Interceptor
2025-08-02 13:19:25,128 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.PaddingAtmosphereInterceptor : Browser Padding Interceptor Support
2025-08-02 13:19:25,128 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.AndroidAtmosphereInterceptor : Android Interceptor Support
2025-08-02 13:19:25,129 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.HeartbeatInterceptor : Heartbeat Interceptor Support
2025-08-02 13:19:25,130 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.SSEAtmosphereInterceptor : SSE Interceptor Support
2025-08-02 13:19:25,131 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JSONPAtmosphereInterceptor : JSONP Interceptor Support
2025-08-02 13:19:25,132 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JavaScriptProtocol : Atmosphere JavaScript Protocol
2025-08-02 13:19:25,133 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor : org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor
2025-08-02 13:19:25,135 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.OnDisconnectInterceptor : Browser disconnection detection
2025-08-02 13:19:25,137 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.IdleResourceInterceptor : org.atmosphere.interceptor.IdleResourceInterceptor
2025-08-02 13:19:25,137 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Set org.atmosphere.cpr.AtmosphereInterceptor.disableDefaults to disable them.
2025-08-02 13:19:25,137 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor CORS Interceptor Support with priority FIRST_BEFORE_DEFAULT 
2025-08-02 13:19:25,143 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Default Response's Headers Interceptor with priority AFTER_DEFAULT 
2025-08-02 13:19:25,144 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser Padding Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 13:19:25,144 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Android Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 13:19:25,144 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.interceptor.HeartbeatInterceptor - HeartbeatInterceptor configured with padding value 'X', client frequency 30 seconds and server frequency 120 seconds
2025-08-02 13:19:25,144 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Heartbeat Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 13:19:25,144 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor SSE Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 13:19:25,144 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor JSONP Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 13:19:25,144 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Atmosphere JavaScript Protocol with priority AFTER_DEFAULT 
2025-08-02 13:19:25,144 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor with priority AFTER_DEFAULT 
2025-08-02 13:19:25,144 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser disconnection detection with priority AFTER_DEFAULT 
2025-08-02 13:19:25,144 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.IdleResourceInterceptor with priority BEFORE_DEFAULT 
2025-08-02 13:19:25,145 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using EndpointMapper class org.atmosphere.util.DefaultEndpointMapper
2025-08-02 13:19:25,145 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterCache: org.atmosphere.cache.UUIDBroadcasterCache
2025-08-02 13:19:25,146 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Default Broadcaster Class: org.atmosphere.cpr.DefaultBroadcaster
2025-08-02 13:19:25,146 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Shared List Resources: false
2025-08-02 13:19:25,146 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Polling Wait Time 100
2025-08-02 13:19:25,146 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Shared ExecutorService supported: true
2025-08-02 13:19:25,146 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Messaging ExecutorService Pool Size unavailable - Not instance of ThreadPoolExecutor
2025-08-02 13:19:25,146 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Async I/O Thread Pool Size: 200
2025-08-02 13:19:25,146 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterFactory: org.atmosphere.cpr.DefaultBroadcasterFactory
2025-08-02 13:19:25,146 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using AtmosphereResurceFactory: org.atmosphere.cpr.DefaultAtmosphereResourceFactory
2025-08-02 13:19:25,146 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using WebSocketProcessor: org.atmosphere.websocket.DefaultWebSocketProcessor
2025-08-02 13:19:25,153 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Invoke AtmosphereInterceptor on WebSocket message true
2025-08-02 13:19:25,154 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - HttpSession supported: false
2025-08-02 13:19:25,154 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using Spring Web ObjectFactory for dependency injection and object creation
2025-08-02 13:19:25,154 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using async support: org.atmosphere.container.JSR356AsyncSupport running under container: Apache Tomcat/9.0.60 using javax.servlet/3.0 and jsr356/WebSocket API
2025-08-02 13:19:25,157 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere Framework 2.6.4 started.
2025-08-02 13:19:25,157 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 
2025-08-02 13:19:25,157 [Thread-40] INFO  NotificationService - 
2025-08-02 13:19:25,157 [Thread-40] INFO  NotificationService - 	For Atmosphere Framework Commercial Support, visit 
2025-08-02 13:19:25,157 [Thread-40] INFO  NotificationService - 	http://www.async-io.org/ or send an <NAME_EMAIL>
2025-08-02 13:19:25,157 [Thread-40] INFO  NotificationService - 
2025-08-02 13:19:25,166 [Thread-40] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 40608 (http) with context path ''
2025-08-02 13:19:25,174 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Started Application in 2.535 seconds (JVM running for 3.131)
2025-08-02 13:19:25,302 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testtype) created
2025-08-02 13:19:25,312 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (subtype) created
2025-08-02 13:19:25,319 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a4e1dcc04a_0_661a4e1dcc04a_0_: finished. Total: 0.342 s, CPU [user: 0.107 s, system: 0.0115 s], Allocated memory: 19.8 MB, svn: 0.279 s [74% getDir2 content (17x), 26% getFile content (44x)] (62x), RepositoryConfigService: 0.11 s [98% getReadConfiguration (170x)] (192x)
2025-08-02 13:19:25,621 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (YesNo) created
2025-08-02 13:19:25,631 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (software_VerificationMethod) created
2025-08-02 13:19:25,636 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checklist) created
2025-08-02 13:19:25,643 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonreqproperty) created
2025-08-02 13:19:25,649 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectoriented) created
2025-08-02 13:19:25,650 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submodule) created
2025-08-02 13:19:25,652 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (yesno) created
2025-08-02 13:19:25,653 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (cICategory) created
2025-08-02 13:19:25,654 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (wpFormat) created
2025-08-02 13:19:25,656 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (trigger) created
2025-08-02 13:19:25,662 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ASILLevel) created
2025-08-02 13:19:25,663 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CSRelated) created
2025-08-02 13:19:25,669 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_Module) created
2025-08-02 13:19:25,685 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (规格对象类型) created
2025-08-02 13:19:25,689 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (jenkins_job) created
2025-08-02 13:19:25,689 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (truefalse) created
2025-08-02 13:19:25,691 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (takeOnGroups) created
2025-08-02 13:19:25,700 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasetype) created
2025-08-02 13:19:25,705 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processGroup) created
2025-08-02 13:19:25,708 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeManagement) created
2025-08-02 13:19:25,716 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (seriousness) created
2025-08-02 13:19:25,720 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softReqClass) created
2025-08-02 13:19:25,726 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWDetailDesign) created
2025-08-02 13:19:25,729 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PhaseChecklists) created
2025-08-02 13:19:25,787 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PassNotpass) created
2025-08-02 13:19:25,793 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineType) created
2025-08-02 13:19:25,798 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (boolYesOrNo) created
2025-08-02 13:19:25,803 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testlevel) created
2025-08-02 13:19:25,808 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (source) created
2025-08-02 13:19:25,818 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectType) created
2025-08-02 13:19:25,823 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atppblversion) created
2025-08-02 13:19:25,827 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (aSIL) created
2025-08-02 13:19:25,837 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (EE) created
2025-08-02 13:19:25,843 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueType) created
2025-08-02 13:19:25,854 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SYS_reqClassification) created
2025-08-02 13:19:25,861 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (oem_2Status) created
2025-08-02 13:19:25,867 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (class) created
2025-08-02 13:19:25,873 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (promotionState) created
2025-08-02 13:19:25,878 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (git_project) created
2025-08-02 13:19:25,897 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (storageType) created
2025-08-02 13:19:25,903 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueproperty) created
2025-08-02 13:19:25,910 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonissueclass) created
2025-08-02 13:19:25,914 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (AgreeDisagree) created
2025-08-02 13:19:25,919 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SA_Category) created
2025-08-02 13:19:25,925 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relevance) created
2025-08-02 13:19:25,935 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (implementationPhase) created
2025-08-02 13:19:25,939 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplier_2Status) created
2025-08-02 13:19:25,952 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Testtype) created
2025-08-02 13:19:25,958 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (conf_baselineTime) created
2025-08-02 13:19:25,969 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (levelneed) created
2025-08-02 13:19:25,973 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (finalresult) created
2025-08-02 13:19:25,978 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testability) created
2025-08-02 13:19:25,986 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solution) created
2025-08-02 13:19:25,991 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Responsible) created
2025-08-02 13:19:25,995 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationstatus) created
2025-08-02 13:19:26,009 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsiassigngroup) created
2025-08-02 13:19:26,015 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqCategory) created
2025-08-02 13:19:26,021 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineName) created
2025-08-02 13:19:26,027 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskType) created
2025-08-02 13:19:26,048 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeReason) created
2025-08-02 13:19:26,051 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectmodule) created
2025-08-02 13:19:26,070 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseOutputMethod) created
2025-08-02 13:19:26,076 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SoftwareFeature) created
2025-08-02 13:19:26,086 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ResponsibleGroup) created
2025-08-02 13:19:26,091 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsifunctionmodule) created
2025-08-02 13:19:26,095 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (FwReqSource) created
2025-08-02 13:19:26,099 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (occurPhase) created
2025-08-02 13:19:26,103 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (compiletask) created
2025-08-02 13:19:26,107 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBVerificationMethod) created
2025-08-02 13:19:26,112 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (functionmodule) created
2025-08-02 13:19:26,116 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (variant) created
2025-08-02 13:19:26,118 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Fusatype) created
2025-08-02 13:19:26,121 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareversion) created
2025-08-02 13:19:26,126 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (appversion) created
2025-08-02 13:19:26,128 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casefirstmodule) created
2025-08-02 13:19:26,133 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditType) created
2025-08-02 13:19:26,139 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Samplestage) created
2025-08-02 13:19:26,144 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casesecondmodule) created
2025-08-02 13:19:26,148 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issue_source) created
2025-08-02 13:19:26,153 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ifNeedRegressionTesting) created
2025-08-02 13:19:26,157 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atpsfsversion) created
2025-08-02 13:19:26,161 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CustomerAllocation) created
2025-08-02 13:19:26,166 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issuesubclass) created
2025-08-02 13:19:26,171 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_importance) created
2025-08-02 13:19:26,176 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reviewMethod) created
2025-08-02 13:19:26,182 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_findType) created
2025-08-02 13:19:26,187 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (editType) created
2025-08-02 13:19:26,197 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testingobjects) created
2025-08-02 13:19:26,203 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcaselevel) created
2025-08-02 13:19:26,211 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplierproblem) created
2025-08-02 13:19:26,217 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqattribute) created
2025-08-02 13:19:26,223 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (fsigroup) created
2025-08-02 13:19:26,233 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_reqsource) created
2025-08-02 13:19:26,243 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (preset) created
2025-08-02 13:19:26,253 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Mechverificationmethod) created
2025-08-02 13:19:26,259 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CPMToTPM) created
2025-08-02 13:19:26,264 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBType) created
2025-08-02 13:19:26,270 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasesign) created
2025-08-02 13:19:26,277 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationphase) created
2025-08-02 13:19:26,298 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processArea) created
2025-08-02 13:19:26,303 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (artifactType) created
2025-08-02 13:19:26,318 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Classification) created
2025-08-02 13:19:26,326 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationmethod) created
2025-08-02 13:19:26,330 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeType) created
2025-08-02 13:19:26,334 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareAndSoftwareSubType) created
2025-08-02 13:19:26,339 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWIntegrationVerificationMethod) created
2025-08-02 13:19:26,343 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (category) created
2025-08-02 13:19:26,386 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBCategory) created
2025-08-02 13:19:26,396 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softreqclass) created
2025-08-02 13:19:26,401 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestMethod) created
2025-08-02 13:19:26,407 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reType) created
2025-08-02 13:19:26,414 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (VerificationCriteria) created
2025-08-02 13:19:26,425 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLinechecklist) created
2025-08-02 13:19:26,431 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Category) created
2025-08-02 13:19:26,442 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWUnitTestDerivingMethods) created
2025-08-02 13:19:26,448 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (firmware_Category) created
2025-08-02 13:19:26,453 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testMethod) created
2025-08-02 13:19:26,469 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QAPorcessAreas) created
2025-08-02 13:19:26,475 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (findSource) created
2025-08-02 13:19:26,483 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a4e1e21c4b_0_661a4e1e21c4b_0_: finished. Total: 1.16 s, CPU [user: 0.521 s, system: 0.0331 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.865 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.595 s [71% getFile content (412x), 29% getDir2 content (21x)] (434x), GC: 0.07 s [100% G1 Young Generation (4x)] (4x)
2025-08-02 13:19:26,596 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (tshirt-sizes) created
2025-08-02 13:19:26,599 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqtype) created
2025-08-02 13:19:26,602 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a4e1f44c4c_0_661a4e1f44c4c_0_: finished. Total: 0.118 s, CPU [user: 0.0259 s, system: 0.00268 s], Allocated memory: 17.9 MB, svn: 0.105 s [85% getDir2 content (18x)] (48x), RepositoryConfigService: 0.025 s [97% getReadConfiguration (124x)] (148x)
2025-08-02 13:19:26,943 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Feasibility) created
2025-08-02 13:19:26,948 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (locaMod) created
2025-08-02 13:19:26,952 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseType) created
2025-08-02 13:19:26,955 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ControlLevel) created
2025-08-02 13:19:26,958 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (NCitemSev) created
2025-08-02 13:19:26,962 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (signType) created
2025-08-02 13:19:26,968 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (WBSCategory) created
2025-08-02 13:19:26,972 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseEnv) created
2025-08-02 13:19:26,974 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verifiability) created
2025-08-02 13:19:26,977 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ProjectUser) created
2025-08-02 13:19:26,981 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (standardReq) created
2025-08-02 13:19:26,984 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (statusa) created
2025-08-02 13:19:26,986 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@WorkItems[type:configurationitemversion]) created
2025-08-02 13:19:26,987 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CIRevisionStatus) created
2025-08-02 13:19:26,991 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (dogTimeout) created
2025-08-02 13:19:26,993 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQCategory) created
2025-08-02 13:19:27,004 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proStage) created
2025-08-02 13:19:27,008 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (BaselineType) created
2025-08-02 13:19:27,014 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (custConfStat) created
2025-08-02 13:19:27,017 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sofReqVer) created
2025-08-02 13:19:27,023 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Source) created
2025-08-02 13:19:27,028 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scCategory) created
2025-08-02 13:19:27,031 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseType) created
2025-08-02 13:19:27,034 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solAdv) created
2025-08-02 13:19:27,036 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseMet) created
2025-08-02 13:19:27,039 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMeth) created
2025-08-02 13:19:27,041 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseMe) created
2025-08-02 13:19:27,044 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseEnv) created
2025-08-02 13:19:27,052 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTarget) created
2025-08-02 13:19:27,056 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ReviewForm) created
2025-08-02 13:19:27,057 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseType) created
2025-08-02 13:19:27,062 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@Collection) created
2025-08-02 13:19:27,070 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submissionStage) created
2025-08-02 13:19:27,073 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMet) created
2025-08-02 13:19:27,078 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandType) created
2025-08-02 13:19:27,082 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseMet) created
2025-08-02 13:19:27,085 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskUrgen) created
2025-08-02 13:19:27,089 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solveMethod) created
2025-08-02 13:19:27,092 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (audMethod) created
2025-08-02 13:19:27,095 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (desStat) created
2025-08-02 13:19:27,100 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scType) created
2025-08-02 13:19:27,103 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseType) created
2025-08-02 13:19:27,105 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (releaseType) created
2025-08-02 13:19:27,110 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseEnv) created
2025-08-02 13:19:27,112 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (targetStage) created
2025-08-02 13:19:27,115 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ClassificationType) created
2025-08-02 13:19:27,118 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testItem) created
2025-08-02 13:19:27,120 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (InfoSecurity) created
2025-08-02 13:19:27,123 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Verification) created
2025-08-02 13:19:27,127 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMod) created
2025-08-02 13:19:27,131 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verMethod) created
2025-08-02 13:19:27,134 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (diagramCategory) created
2025-08-02 13:19:27,138 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (assSubsystem) created
2025-08-02 13:19:27,141 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (OccurrenceProbability) created
2025-08-02 13:19:27,146 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (developmentMethod) created
2025-08-02 13:19:27,148 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (portType) created
2025-08-02 13:19:27,153 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checkType) created
2025-08-02 13:19:27,155 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandStatus) created
2025-08-02 13:19:27,159 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (important) created
2025-08-02 13:19:27,162 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMec) created
2025-08-02 13:19:27,165 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseTy) created
2025-08-02 13:19:27,168 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (recentPre) created
2025-08-02 13:19:27,173 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseDesignMethod) created
2025-08-02 13:19:27,176 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testCasePri) created
2025-08-02 13:19:27,183 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relObj) created
2025-08-02 13:19:27,187 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proSer) created
2025-08-02 13:19:27,191 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestProblemType) created
2025-08-02 13:19:27,195 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (chipName) created
2025-08-02 13:19:27,197 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTiming) created
2025-08-02 13:19:27,202 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a4e1f75c4e_0_661a4e1f75c4e_0_: finished. Total: 0.523 s, CPU [user: 0.209 s, system: 0.0183 s], Allocated memory: 384.3 MB, svn: 0.343 s [56% getFile content (185x), 44% getDir2 content (21x)] (207x), RepositoryConfigService: 0.341 s [97% getReadConfiguration (2787x)] (3025x)
2025-08-02 13:19:27,327 [PreLoadDataService | u:p] ERROR com.polarion.subterra.base.data.model.CustomField - Unknown custom field type 'enum' - using 'string' - for field with id 'taskType' for 'WorkItem task /default/WBS'
2025-08-02 13:19:27,347 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a4e1ff884f_0_661a4e1ff884f_0_: finished. Total: 0.145 s, CPU [user: 0.0332 s, system: 0.00354 s], Allocated memory: 13.1 MB, svn: 0.126 s [81% getDir2 content (18x)] (52x), RepositoryConfigService: 0.0384 s [98% getReadConfiguration (128x)] (150x)
2025-08-02 13:19:27,347 [PreLoadDataService] INFO  com.polarion.psvn.launcher.internal.data.PreLoadDataService - Preloading data FINISHED took  [ TIME 5.36 s. ]
2025-08-02 13:19:27,347 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 5.36 s, CPU [user: 1.75 s, system: 0.25 s], Allocated memory: 1.6 GB, transactions: 11, svn: 3.53 s [35% getDir2 content (133x), 33% getDatedRevision (181x), 29% getFile content (865x)] (1224x), RepositoryConfigService: 1.6 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.804 s [81% Category (117x)] (139x), ObjectMaps: 0.292 s [49% getPrimaryObjectProperty (131x), 31% getPrimaryObjectLocation (137x)] (536x)
2025-08-02 13:19:27,347 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 61, svn: 4.56 s [47% getDatedRevision (362x), 27% getDir2 content (133x), 22% getFile content (865x)] (1409x), RepositoryConfigService: 1.6 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.804 s [81% Category (117x)] (140x), ObjectMaps: 0.292 s [49% getPrimaryObjectProperty (131x), 31% getPrimaryObjectLocation (137x)] (536x)
2025-08-02 13:19:32,029 [Thread-36] INFO  NotificationService - Notification service was started successfully.
2025-08-02 13:21:06,934 [ajp-nio-127.0.0.1-8889-exec-3 | cID:6939eec6-c0a844bd-2e1c5321-2682230b] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?_t=1754112061051': Total: 5.81 s, CPU [user: 0.0429 s, system: 0.0307 s], Allocated memory: 512.7 kB, transactions: 0
2025-08-02 13:21:08,800 [ajp-nio-127.0.0.1-8889-exec-2 | cID:6939eec5-c0a844bd-2e1c5321-3faf0e8b] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?_t=1754112061051': Total: 7.67 s, CPU [user: 0.0214 s, system: 0.0303 s], Allocated memory: 641.8 kB, transactions: 0
2025-08-02 13:24:20,766 [Thread-32] INFO  class com.polarion.alm.server.util.ChartExporterStartup - Chart renderer was started successfully.
2025-08-02 13:24:22,124 [Thread-38] INFO  class com.polarion.alm.server.util.FormulaServerStartup - Formula renderer was started successfully.
