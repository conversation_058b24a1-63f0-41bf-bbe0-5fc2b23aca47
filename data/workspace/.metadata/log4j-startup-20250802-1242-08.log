2025-08-02 12:42:08,992 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 12:42:08,992 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-02 12:42:08,992 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 12:42:08,992 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-02 12:42:08,993 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-02 12:42:08,993 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:42:08,993 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-02 12:42:13,179 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-02 12:42:13,338 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.159 s. ]
2025-08-02 12:42:13,338 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-02 12:42:13,401 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0622 s. ]
2025-08-02 12:42:13,449 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-02 12:42:13,568 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 15 s. ]
2025-08-02 12:42:13,800 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.81 s. ]
2025-08-02 12:42:13,891 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:42:13,892 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-02 12:42:13,919 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-08-02 12:42:13,919 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:42:13,919 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-02 12:42:13,926 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-08-02 12:42:13,926 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-08-02 12:42:13,926 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-08-02 12:42:13,926 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-08-02 12:42:13,926 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-08-02 12:42:13,926 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-08-02 12:42:13,936 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-02 12:42:14,044 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-02 12:42:14,137 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-02 12:42:14,616 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.7 s. ]
2025-08-02 12:42:14,626 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:42:14,626 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-02 12:42:14,848 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-02 12:42:14,860 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-08-02 12:42:14,886 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:42:14,886 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-02 12:42:14,889 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-02 12:42:14,937 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-02 12:42:14,987 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-02 12:42:15,024 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-02 12:42:15,045 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-02 12:42:15,065 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-02 12:42:15,094 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-02 12:42:15,125 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-02 12:42:15,148 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-02 12:42:15,148 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.29 s. ]
2025-08-02 12:42:15,148 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:42:15,148 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-02 12:42:15,161 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-02 12:42:15,161 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:42:15,161 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-02 12:42:15,244 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-02 12:42:15,246 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-02 12:42:15,345 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.18 s. ]
2025-08-02 12:42:15,345 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:42:15,345 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-02 12:42:15,352 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-02 12:42:15,352 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:42:15,352 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-02 12:42:17,761 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.41 s. ]
2025-08-02 12:42:17,761 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 12:42:17,762 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.77 s. ]
2025-08-02 12:42:17,762 [main] INFO  com.polarion.platform.startup - ****************************************************************
