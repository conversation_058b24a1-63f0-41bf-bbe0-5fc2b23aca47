2025-08-01 21:31:10,028 [Catalina-utility-3] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-08-01 21:31:13,204 [Catalina-utility-3] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-08-01 21:31:13,217 [Catalina-utility-3] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[90]')
2025-08-01 21:31:13,221 [Catalina-utility-3] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[92]')
2025-08-01 21:31:13,225 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-08-01 21:31:13,245 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[90]')
2025-08-01 21:31:13,246 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[90]')
2025-08-01 21:31:13,262 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[96]')
2025-08-01 21:31:17,101 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-08-01 21:31:17,584 [ajp-nio-127.0.0.1-8889-exec-1 | cID:65d46e7c-c0a844bd-5a6dbab7-8f2627cd] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-08-01 21:31:17,738 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-08-01 21:31:17,739 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
