2025-08-02 20:46:37,787 [main | u:p] INFO  com.polarion.platform.monitoring - Full monitoring results are stored in file /opt/polarion/data/workspace/monitoring/results.txt
2025-08-02 20:46:37,811 [main | u:p] INFO  com.polarion.platform.monitoring - Executing actions from stage PREBOOT
2025-08-02 20:46:37,813 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 20:46:37,813 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,414.98 GB
 [Sat Aug 02 20:46:37 CST 2025]
2025-08-02 20:46:38,024 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.jvm.version'
2025-08-02 20:46:38,025 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.jvm.version (JVM Version) = JVM Version 
Microsoft OpenJDK 64-Bit Server VM 11.0.27+6-LTS
 [Sat Aug 02 20:46:38 CST 2025]
2025-08-02 20:46:38,027 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.os.version'
2025-08-02 20:46:38,027 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.os.version (OS Version) = OS Version 
Mac OS X 15.5 aarch64
 [Sat Aug 02 20:46:38 CST 2025]
2025-08-02 20:46:38,030 [main | u:p] INFO  com.polarion.platform.monitoring - Finished with actions from stage PREBOOT: {OK=3}
2025-08-02 20:46:43,980 [main | u:p] INFO  com.polarion.platform.monitoring - Next slow periodic actions will be executed on Sun Aug 03 01:00:43 CST 2025
2025-08-02 20:46:43,989 [main] INFO  com.polarion.platform.monitoring - Executing actions from stage POSTBOOT
2025-08-02 20:46:43,989 [main] INFO  com.polarion.platform.monitoring - Executing action 'polarion.info.cache.statistics'
2025-08-02 20:46:43,997 [main] INFO  com.polarion.platform.monitoring - polarion.info.cache.statistics (Statistics of caches) = Statistics of caches 
                         CACHE       HITS     MISSES  HIT_RATIO       GETS       PUTS    REMOVAL   EVICTION 
     attachments-content-cache          0          0          0%          0          0          0          0 
                baseline-cache          0          0          0%          0          0          0          0 
                            bq          0          0          0%          0          0          0          0 
                dao-attachment          0          0          0%          0          0          0          0 
                   dao-comment          0          0          0%          0          0          0          0 
                    dao-global          0          4          0%          4          0          0          0 
                    dao-module          0          0          0%          0          0          0          0 
          dao-moduleattachment          0          0          0%          0          0          0          0 
             dao-modulecomment          0          0          0%          0          0          0          0 
                      dao-plan          0          0          0%          0          0          0          0 
                   dao-project          0          0          0%          0          0          0          0 
              dao-projectgroup          0          6          0%          6          3          0          0 
                  dao-richpage          0          0          0%          0          0          0          0 
        dao-richpageattachment          0          0          0%          0          0          0          0 
                   dao-testrun          0          0          0%          0          0          0          0 
         dao-testrunattachment          0          0          0%          0          0          0          0 
                      dao-user          0          0          0%          0          0          0          0 
                  dao-wikipage          0          0          0%          0          0          0          0 
        dao-wikipageattachment          0          0          0%          0          0          0          0 
                  dao-workitem          0          0          0%          0          0          0          0 
      derived-linked-revisions          0          0          0%          0          0          0          0 
                  formulas-svg          0          0          0%          0          0          0          0 
                github-commits          0          0          0%          0          0          0          0 
            historical-queries          0          0          0%          0          0          0          0 
      historical-queries-sizes          0          0          0%          0          0          0          0 
        oslc-linked-item-cache          0          0          0%          0          0          0          0 
               plan-statistics          0          0          0%          0          0          0          0 
                   rmd-default          4          5         44%          9          7          0          0 
                   ss-combined          0          0          0%          0          0          0          0 
                    ss-context          0          0          0%          0          0          0          0 
                     wiki-page          2          8         20%         10          9          1          0 
              wiki-page-exists          0          0          0%          0          9          1          0 

 [Sat Aug 02 20:46:43 CST 2025]
2025-08-02 20:46:44,006 [main] INFO  com.polarion.platform.monitoring - Finished with actions from stage POSTBOOT: {OK=1}
2025-08-02 20:56:43,990 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 20:56:44,020 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 20:56:44,026 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 20:56:44 CST 2025]
2025-08-02 20:56:44,060 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 20:56:44,061 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,414.95 GB
 [Sat Aug 02 20:56:44 CST 2025]
2025-08-02 20:56:44,269 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 20:56:44,269 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 20:56:44 CST 2025]
2025-08-02 20:56:44,272 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 20:56:44,272 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 15; Time: 0.283 s.
 [Sat Aug 02 20:56:44 CST 2025]
2025-08-02 20:56:44,275 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 20:56:44,275 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 20:56:44 CST 2025]
2025-08-02 20:56:44,405 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 20:56:44,407 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 20:56:44 CST 2025]
2025-08-02 20:56:44,548 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 21:06:43,994 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 21:06:43,997 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 21:06:43,997 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 21:06:43 CST 2025]
2025-08-02 21:06:44,020 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 21:06:44,021 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,414.94 GB
 [Sat Aug 02 21:06:44 CST 2025]
2025-08-02 21:06:44,201 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 21:06:44,201 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 21:06:44 CST 2025]
2025-08-02 21:06:44,203 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 21:06:44,203 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 15; Time: 0.283 s.
 [Sat Aug 02 21:06:44 CST 2025]
2025-08-02 21:06:44,205 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 21:06:44,205 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 21:06:44 CST 2025]
2025-08-02 21:06:44,311 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 21:06:44,311 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 21:06:44 CST 2025]
2025-08-02 21:06:44,446 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 21:16:44,002 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 21:16:44,005 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 21:16:44,005 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 21:16:44 CST 2025]
2025-08-02 21:16:44,016 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 21:16:44,016 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,414.92 GB
 [Sat Aug 02 21:16:44 CST 2025]
2025-08-02 21:16:44,204 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 21:16:44,204 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 21:16:44 CST 2025]
2025-08-02 21:16:44,206 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 21:16:44,206 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 15; Time: 0.283 s.
 [Sat Aug 02 21:16:44 CST 2025]
2025-08-02 21:16:44,210 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 21:16:44,210 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 21:16:44 CST 2025]
2025-08-02 21:16:44,319 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 21:16:44,319 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 21:16:44 CST 2025]
2025-08-02 21:16:44,448 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 21:26:44,012 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 21:26:44,015 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 21:26:44,016 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 21:26:44 CST 2025]
2025-08-02 21:26:44,028 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 21:26:44,028 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,414.9 GB
 [Sat Aug 02 21:26:44 CST 2025]
2025-08-02 21:26:44,217 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 21:26:44,217 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 21:26:44 CST 2025]
2025-08-02 21:26:44,218 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 21:26:44,218 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 15; Time: 0.283 s.
 [Sat Aug 02 21:26:44 CST 2025]
2025-08-02 21:26:44,220 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 21:26:44,220 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 21:26:44 CST 2025]
2025-08-02 21:26:44,326 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 21:26:44,327 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 21:26:44 CST 2025]
2025-08-02 21:26:44,455 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 21:36:44,017 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 21:36:44,020 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 21:36:44,021 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 21:36:44 CST 2025]
2025-08-02 21:36:44,037 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 21:36:44,038 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,414.91 GB
 [Sat Aug 02 21:36:44 CST 2025]
2025-08-02 21:36:44,290 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 21:36:44,290 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 21:36:44 CST 2025]
2025-08-02 21:36:44,292 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 21:36:44,292 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 16; Time: 0.316 s.
 [Sat Aug 02 21:36:44 CST 2025]
2025-08-02 21:36:44,294 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 21:36:44,294 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 21:36:44 CST 2025]
2025-08-02 21:36:44,403 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 21:36:44,403 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 21:36:44 CST 2025]
2025-08-02 21:36:44,533 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 21:46:44,023 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 21:46:44,026 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 21:46:44,026 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 21:46:44 CST 2025]
2025-08-02 21:46:44,036 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 21:46:44,036 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,414.67 GB
 [Sat Aug 02 21:46:44 CST 2025]
2025-08-02 21:46:44,212 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 21:46:44,212 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 21:46:44 CST 2025]
2025-08-02 21:46:44,214 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 21:46:44,214 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 16; Time: 0.316 s.
 [Sat Aug 02 21:46:44 CST 2025]
2025-08-02 21:46:44,216 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 21:46:44,216 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 21:46:44 CST 2025]
2025-08-02 21:46:44,326 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 21:46:44,326 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 21:46:44 CST 2025]
2025-08-02 21:46:44,458 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 21:56:44,028 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 21:56:44,030 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 21:56:44,031 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 21:56:44 CST 2025]
2025-08-02 21:56:44,042 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 21:56:44,043 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,414.65 GB
 [Sat Aug 02 21:56:44 CST 2025]
2025-08-02 21:56:44,225 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 21:56:44,225 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 21:56:44 CST 2025]
2025-08-02 21:56:44,227 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 21:56:44,227 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 16; Time: 0.316 s.
 [Sat Aug 02 21:56:44 CST 2025]
2025-08-02 21:56:44,229 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 21:56:44,229 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 21:56:44 CST 2025]
2025-08-02 21:56:44,333 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 21:56:44,333 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 21:56:44 CST 2025]
2025-08-02 21:56:44,459 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 22:06:44,036 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 22:06:44,038 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 22:06:44,039 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 22:06:44 CST 2025]
2025-08-02 22:06:44,060 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 22:06:44,061 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,412.65 GB
 [Sat Aug 02 22:06:44 CST 2025]
2025-08-02 22:06:44,235 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 22:06:44,236 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 22:06:44 CST 2025]
2025-08-02 22:06:44,239 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 22:06:44,239 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 16; Time: 0.316 s.
 [Sat Aug 02 22:06:44 CST 2025]
2025-08-02 22:06:44,244 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 22:06:44,244 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 22:06:44 CST 2025]
2025-08-02 22:06:44,363 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 22:06:44,364 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 22:06:44 CST 2025]
2025-08-02 22:06:44,515 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 22:16:44,044 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 22:16:44,046 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 22:16:44,047 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 22:16:44 CST 2025]
2025-08-02 22:16:44,056 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 22:16:44,056 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,412.61 GB
 [Sat Aug 02 22:16:44 CST 2025]
2025-08-02 22:16:44,270 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 22:16:44,270 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 22:16:44 CST 2025]
2025-08-02 22:16:44,272 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 22:16:44,273 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 16; Time: 0.316 s.
 [Sat Aug 02 22:16:44 CST 2025]
2025-08-02 22:16:44,275 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 22:16:44,275 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 22:16:44 CST 2025]
2025-08-02 22:16:44,401 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 22:16:44,401 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 22:16:44 CST 2025]
2025-08-02 22:16:44,547 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 22:26:44,052 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 22:26:44,053 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 22:26:44,054 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 22:26:44 CST 2025]
2025-08-02 22:26:44,065 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 22:26:44,066 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,413.6 GB
 [Sat Aug 02 22:26:44 CST 2025]
2025-08-02 22:26:44,276 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 22:26:44,276 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 22:26:44 CST 2025]
2025-08-02 22:26:44,279 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 22:26:44,279 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 17; Time: 0.363 s.
 [Sat Aug 02 22:26:44 CST 2025]
2025-08-02 22:26:44,282 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 22:26:44,283 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 22:26:44 CST 2025]
2025-08-02 22:26:44,486 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 22:26:44,486 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 22:26:44 CST 2025]
2025-08-02 22:26:44,689 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 22:36:44,062 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 22:36:44,065 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 22:36:44,066 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 22:36:44 CST 2025]
2025-08-02 22:36:44,081 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 22:36:44,082 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,413.62 GB
 [Sat Aug 02 22:36:44 CST 2025]
2025-08-02 22:36:44,300 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 22:36:44,300 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 22:36:44 CST 2025]
2025-08-02 22:36:44,303 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 22:36:44,303 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 17; Time: 0.363 s.
 [Sat Aug 02 22:36:44 CST 2025]
2025-08-02 22:36:44,305 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 22:36:44,305 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 22:36:44 CST 2025]
2025-08-02 22:36:44,420 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 22:36:44,421 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 22:36:44 CST 2025]
2025-08-02 22:36:44,566 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
