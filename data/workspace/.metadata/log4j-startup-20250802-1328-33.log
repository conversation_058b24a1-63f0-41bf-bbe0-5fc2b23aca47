2025-08-02 13:28:33,810 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:28:33,811 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-02 13:28:33,811 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:28:33,811 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-02 13:28:33,811 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-02 13:28:33,811 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:28:33,811 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-02 13:28:39,235 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-02 13:28:39,512 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.277 s. ]
2025-08-02 13:28:39,512 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-02 13:28:39,655 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.141 s. ]
2025-08-02 13:28:39,785 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-02 13:28:39,989 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 14 s. ]
2025-08-02 13:28:40,300 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 6.5 s. ]
2025-08-02 13:28:40,407 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:28:40,407 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-02 13:28:40,442 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.14 s. ]
2025-08-02 13:28:40,442 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:28:40,442 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-02 13:28:40,449 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-08-02 13:28:40,449 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-02 13:28:40,449 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-08-02 13:28:40,449 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-08-02 13:28:40,449 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-08-02 13:28:40,449 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-08-02 13:28:40,466 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-02 13:28:40,677 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-02 13:28:40,776 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-02 13:28:41,442 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 1.0 s. ]
2025-08-02 13:28:41,469 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:28:41,469 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-02 13:28:41,840 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-02 13:28:41,859 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.42 s. ]
2025-08-02 13:28:41,899 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:28:41,899 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-02 13:28:41,907 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-02 13:28:42,001 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-02 13:28:42,104 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-02 13:28:42,161 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-02 13:28:42,198 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-02 13:28:42,235 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-02 13:28:42,288 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-02 13:28:42,361 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-02 13:28:42,434 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-02 13:28:42,434 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.58 s. ]
2025-08-02 13:28:42,434 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:28:42,434 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-02 13:28:42,453 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-02 13:28:42,453 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:28:42,453 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-02 13:28:42,642 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-02 13:28:42,651 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-02 13:28:42,846 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.39 s. ]
2025-08-02 13:28:42,847 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:28:42,847 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-02 13:28:42,860 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-02 13:28:42,861 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:28:42,861 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-02 13:29:38,304 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 55.44 s. ]
2025-08-02 13:29:38,306 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:29:38,306 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 64.5 s. ]
2025-08-02 13:29:38,306 [main] INFO  com.polarion.platform.startup - ****************************************************************
