2025-08-02 17:21:29,713 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0364 s [59% update (144x), 41% query (12x)] (221x), svn: 0.0116 s [60% getLatestRevision (2x), 30% testConnection (1x)] (4x)
2025-08-02 17:21:29,820 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0328 s [56% getDir2 content (2x), 36% info (3x)] (6x)
2025-08-02 17:21:30,506 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.682 s, CPU [user: 0.194 s, system: 0.272 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.104 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-02 17:21:30,506 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.682 s, CPU [user: 0.223 s, system: 0.33 s], Allocated memory: 68.4 MB, transactions: 0, ObjectMaps: 0.121 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-02 17:21:30,506 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.681 s, CPU [user: 0.0905 s, system: 0.167 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.0857 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0606 s [79% log2 (10x), 14% getLatestRevision (2x)] (13x)
2025-08-02 17:21:30,506 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.682 s, CPU [user: 0.109 s, system: 0.211 s], Allocated memory: 24.1 MB, transactions: 0, ObjectMaps: 0.0746 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-02 17:21:30,506 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.681 s, CPU [user: 0.0655 s, system: 0.084 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.078 s [76% log2 (10x), 17% getLatestRevision (2x)] (13x), ObjectMaps: 0.0579 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-02 17:21:30,506 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.682 s, CPU [user: 0.0739 s, system: 0.0964 s], Allocated memory: 9.8 MB, transactions: 0, svn: 0.0836 s [39% log2 (5x), 21% info (5x), 18% log (1x), 10% getLatestRevision (2x)] (18x), ObjectMaps: 0.0606 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-02 17:21:30,507 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.504 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.279 s [62% log2 (36x), 14% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-08-02 17:21:30,719 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.18 s [100% getReadConfiguration (48x)] (48x), svn: 0.0733 s [87% info (18x)] (38x)
2025-08-02 17:21:31,033 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.25 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.192 s [100% getReadConfiguration (54x)] (54x)
2025-08-02 17:21:31,279 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.232 s [100% doFinishStartup (1x)] (1x), commit: 0.0733 s [100% Revision (1x)] (1x), Lucene: 0.0281 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0135 s [100% objectsToInv (1x)] (1x)
2025-08-02 17:21:33,717 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.257 s [89% info (158x)] (168x)
2025-08-02 17:21:33,907 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661a858a06844_0_661a858a06844_0_: finished. Total: 0.185 s, CPU [user: 0.112 s, system: 0.0127 s], Allocated memory: 16.1 MB, resolve: 0.0532 s [96% User (2x)] (4x), Lucene: 0.0143 s [100% search (1x)] (1x), svn: 0.0138 s [56% getLatestRevision (2x), 36% testConnection (1x)] (5x)
2025-08-02 17:21:34,414 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.636 s, CPU [user: 0.00642 s, system: 0.00143 s], Allocated memory: 356.3 kB, transactions: 1
2025-08-02 17:21:34,415 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, notification worker: 0.195 s [97% RevisionActivityCreator (2x)] (6x), resolve: 0.065 s [94% User (3x)] (6x), Lucene: 0.0323 s [44% search (1x), 34% refresh (1x), 21% add (1x)] (3x), Incremental Baseline: 0.026 s [100% WorkItem (24x)] (24x), svn: 0.0196 s [54% getLatestRevision (3x), 41% testConnection (2x)] (7x), ObjectMaps: 0.0122 s [75% getPrimaryObjectLocation (2x), 14% getPrimaryObjectProperty (1x)] (7x), persistence listener: 0.0119 s [85% indexRefreshPersistenceListener (1x)] (7x)
2025-08-02 17:21:34,415 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.703 s, CPU [user: 0.135 s, system: 0.0216 s], Allocated memory: 18.8 MB, transactions: 25, svn: 0.558 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0409 s [81% buildBaselineSnapshots (1x)] (26x)
2025-08-02 17:21:34,714 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a858a07045_0_661a858a07045_0_: finished. Total: 0.99 s, CPU [user: 0.298 s, system: 0.0775 s], Allocated memory: 51.6 MB, svn: 0.584 s [47% getDatedRevision (181x), 32% getDir2 content (25x), 20% getFile content (119x)] (328x), resolve: 0.343 s [100% Category (117x)] (117x), ObjectMaps: 0.121 s [43% getPrimaryObjectProperty (117x), 31% getPrimaryObjectLocation (117x), 26% getLastPromoted (117x)] (473x)
2025-08-02 17:21:34,951 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a858b10c48_0_661a858b10c48_0_: finished. Total: 0.163 s, CPU [user: 0.0681 s, system: 0.012 s], Allocated memory: 8.0 MB, resolve: 0.0617 s [100% User (9x)] (9x), RepositoryConfigService: 0.0596 s [51% getReadConfiguration (180x), 49% getReadUserConfiguration (10x)] (190x), svn: 0.0572 s [57% info (21x), 33% getFile content (16x)] (39x), ObjectMaps: 0.0193 s [59% getPrimaryObjectProperty (8x), 22% getLastPromoted (8x)] (32x), GC: 0.017 s [100% G1 Young Generation (1x)] (1x)
2025-08-02 17:21:35,160 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a858b4684a_0_661a858b4684a_0_: finished. Total: 0.158 s, CPU [user: 0.0472 s, system: 0.00768 s], Allocated memory: 19.8 MB, svn: 0.128 s [76% getDir2 content (17x), 24% getFile content (44x)] (62x), RepositoryConfigService: 0.047 s [98% getReadConfiguration (170x)] (192x)
2025-08-02 17:21:35,728 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a858b6e04b_0_661a858b6e04b_0_: finished. Total: 0.567 s, CPU [user: 0.289 s, system: 0.0202 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.421 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.275 s [62% getFile content (412x), 38% getDir2 content (21x)] (434x)
2025-08-02 17:21:36,061 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a858c1544e_0_661a858c1544e_0_: finished. Total: 0.231 s, CPU [user: 0.0947 s, system: 0.00536 s], Allocated memory: 386.8 MB, svn: 0.146 s [50% getDir2 content (21x), 50% getFile content (185x)] (207x), RepositoryConfigService: 0.146 s [97% getReadConfiguration (2787x)] (3025x)
2025-08-02 17:21:36,117 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.4 s, CPU [user: 0.895 s, system: 0.137 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.38 s [42% getDir2 content (133x), 33% getFile content (865x), 20% getDatedRevision (181x)] (1224x), RepositoryConfigService: 0.754 s [93% getReadConfiguration (12165x)] (12859x), resolve: 0.448 s [77% Category (117x), 14% User (9x)] (139x), ObjectMaps: 0.155 s [48% getPrimaryObjectProperty (131x), 28% getPrimaryObjectLocation (137x), 24% getLastPromoted (131x)] (536x)
2025-08-02 17:21:36,117 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 61, svn: 1.93 s [43% getDatedRevision (362x), 30% getDir2 content (133x), 24% getFile content (865x)] (1407x), RepositoryConfigService: 0.754 s [93% getReadConfiguration (12165x)] (12859x), resolve: 0.448 s [77% Category (117x), 14% User (10x)] (140x), ObjectMaps: 0.155 s [48% getPrimaryObjectProperty (131x), 28% getPrimaryObjectLocation (137x), 24% getLastPromoted (131x)] (536x)
2025-08-02 17:22:19,416 [ajp-nio-127.0.0.1-8889-exec-2 | cID:6a16d9a1-7f000001-7115e771-1c3300a6] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?_t=1754126539134': Total: 0.246 s, CPU [user: 0.0567 s, system: 0.0358 s], Allocated memory: 3.2 MB, transactions: 0
2025-08-02 17:22:19,426 [ajp-nio-127.0.0.1-8889-exec-3 | cID:6a16d9a1-7f000001-7115e771-54725119] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?_t=1754126539133': Total: 0.256 s, CPU [user: 0.0591 s, system: 0.0375 s], Allocated memory: 2.4 MB, transactions: 0
2025-08-02 17:22:21,130 [ajp-nio-127.0.0.1-8889-exec-4 | cID:6a16e0de-7f000001-7115e771-a10a8f0c] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/template-config/template_demo_code_review?_t=1754126540996': Total: 0.107 s, CPU [user: 0.0221 s, system: 0.00522 s], Allocated memory: 1.0 MB, transactions: 0
2025-08-02 17:42:05,837 [ajp-nio-127.0.0.1-8889-exec-1 | cID:6a28f490-7f000001-7115e771-331eb98f] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates': Total: 0.122 s, CPU [user: 0.0319 s, system: 0.051 s], Allocated memory: 1.0 MB, transactions: 0
2025-08-02 17:42:06,028 [ajp-nio-127.0.0.1-8889-exec-2 | cID:6a28f540-7f000001-7115e771-4f319ab4] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/template-config/template_6c4b41a2ccd946f0b769317e89e5c5ca': Total: 0.14 s, CPU [user: 0.0217 s, system: 0.0132 s], Allocated memory: 628.1 kB, transactions: 0
2025-08-02 17:44:24,964 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.112646484375
2025-08-02 17:44:30,114 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.32 s, CPU [user: 0.00217 s, system: 0.00606 s], Allocated memory: 130.3 kB, transactions: 0, PullingJob: 0.212 s [100% collectChanges (1x)] (1x), svn: 0.212 s [100% getLatestRevision (1x)] (1x)
2025-08-02 17:44:34,974 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.532470703125
2025-08-02 17:44:44,970 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.3515625
2025-08-02 17:44:54,970 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.1978515625
2025-08-02 17:45:04,968 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.021630859375
2025-08-02 17:45:24,056 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.197 s, CPU [user: 0.0019 s, system: 0.0072 s], Allocated memory: 130.1 kB, transactions: 0, PullingJob: 0.174 s [100% collectChanges (1x)] (1x), svn: 0.17 s [100% getLatestRevision (1x)] (1x)
2025-08-02 17:45:25,305 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.48623046875
2025-08-02 17:45:34,971 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.364013671875
2025-08-02 17:45:44,966 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.225048828125
2025-08-02 17:45:54,967 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.0994140625
2025-08-02 17:46:04,969 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.21279296875
2025-08-02 17:46:14,962 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.07177734375
2025-08-02 17:51:24,967 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.07802734375
2025-08-02 17:51:34,970 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.033984375
2025-08-02 17:51:44,966 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 2.04755859375
2025-08-02 17:51:54,965 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 2.003369140625
2025-08-02 17:52:04,971 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.726708984375
2025-08-02 17:52:14,967 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.498681640625
2025-08-02 17:52:24,969 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.28359375
2025-08-02 17:52:34,973 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.116943359375
2025-08-02 17:53:24,979 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.32060546875
2025-08-02 17:53:34,969 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.33212890625
2025-08-02 17:53:44,965 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.188134765625
2025-08-02 17:53:54,969 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.050244140625
2025-08-02 17:54:43,246 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.292 s, CPU [user: 0.00232 s, system: 0.00638 s], Allocated memory: 129.8 kB, transactions: 0, PullingJob: 0.251 s [100% collectChanges (1x)] (1x), svn: 0.24 s [100% getLatestRevision (1x)] (1x)
2025-08-02 17:54:54,970 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.25166015625
2025-08-02 17:55:04,966 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.120068359375
2025-08-02 18:44:24,994 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.326513671875
2025-08-02 18:44:34,987 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.138525390625
2025-08-02 18:44:44,986 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.00283203125
2025-08-02 19:16:05,034 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.23 s, CPU [user: 0.0016 s, system: 0.00568 s], Allocated memory: 130.2 kB, transactions: 0, PullingJob: 0.0777 s [100% collectChanges (1x)] (1x), svn: 0.0316 s [100% getLatestRevision (1x)] (1x)
2025-08-02 19:16:15,004 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.76962890625
2025-08-02 19:16:25,004 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.844091796875
2025-08-02 19:16:35,001 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.742431640625
2025-08-02 19:16:45,006 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.560009765625
2025-08-02 19:16:55,007 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.365576171875
2025-08-02 19:17:04,999 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.202978515625
2025-08-02 19:17:14,999 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.0474609375
2025-08-02 19:18:45,000 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.211474609375
2025-08-02 19:18:55,000 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.078662109375
2025-08-02 19:19:05,002 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.0162109375
2025-08-02 19:21:41,499 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.104 s, CPU [user: 0.00226 s, system: 0.0141 s], Allocated memory: 129.7 kB, transactions: 0, PullingJob: 0.0912 s [100% collectChanges (1x)] (1x), svn: 0.0858 s [100% getLatestRevision (1x)] (1x)
2025-08-02 19:22:15,004 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.073583984375
2025-08-02 19:24:25,002 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.5044921875
2025-08-02 19:24:35,003 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.3279296875
2025-08-02 19:24:45,005 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.237451171875
2025-08-02 19:24:55,004 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.13076171875
2025-08-02 19:25:05,002 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.002978515625
2025-08-02 19:35:21,528 [ajp-nio-127.0.0.1-8889-exec-4 | cID:6a90a642-7f000001-7115e771-24426073] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/template-config/template_6c4b41a2ccd946f0b769317e89e5c5ca': Total: 0.112 s, CPU [user: 0.0144 s, system: 0.0475 s], Allocated memory: 647.5 kB, transactions: 0
2025-08-02 19:39:45,012 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.162646484375
2025-08-02 19:39:55,011 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.022607421875
2025-08-02 20:06:32,330 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.104 s, CPU [user: 0.00187 s, system: 0.0115 s], Allocated memory: 130.1 kB, transactions: 0, PullingJob: 0.0905 s [100% collectChanges (1x)] (1x), svn: 0.0781 s [100% getLatestRevision (1x)] (1x)
2025-08-02 20:12:15,026 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.32392578125
2025-08-02 20:12:25,024 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.173828125
2025-08-02 20:12:35,021 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.13359375
2025-08-02 20:12:45,025 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.0201171875
2025-08-02 20:29:27,836 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.124 s, CPU [user: 0.00194 s, system: 0.00321 s], Allocated memory: 130.1 kB, transactions: 0, PullingJob: 0.0954 s [100% collectChanges (1x)] (1x), svn: 0.0954 s [100% getLatestRevision (1x)] (1x)
