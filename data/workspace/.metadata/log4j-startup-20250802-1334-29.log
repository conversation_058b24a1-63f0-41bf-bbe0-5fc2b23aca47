2025-08-02 13:34:29,634 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:34:29,634 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-02 13:34:29,634 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:34:29,634 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-02 13:34:29,634 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-02 13:34:29,634 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:34:29,634 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-02 13:34:33,732 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-02 13:34:33,846 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.114 s. ]
2025-08-02 13:34:33,846 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-02 13:34:33,882 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.035 s. ]
2025-08-02 13:34:33,940 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-02 13:34:34,037 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 18 s. ]
2025-08-02 13:34:34,263 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.64 s. ]
2025-08-02 13:34:34,341 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:34:34,341 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-02 13:34:34,363 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-08-02 13:34:34,363 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:34:34,363 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-02 13:34:34,368 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-08-02 13:34:34,368 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-02 13:34:34,368 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-08-02 13:34:34,368 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-08-02 13:34:34,368 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-08-02 13:34:34,368 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-08-02 13:34:34,381 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-02 13:34:34,517 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-02 13:34:34,599 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-02 13:34:35,078 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.72 s. ]
2025-08-02 13:34:35,089 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:34:35,089 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-02 13:34:35,295 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-02 13:34:35,305 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-08-02 13:34:35,328 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:34:35,328 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-02 13:34:35,331 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-02 13:34:35,380 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-02 13:34:35,425 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-02 13:34:35,459 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-02 13:34:35,477 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-02 13:34:35,497 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-02 13:34:35,527 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-02 13:34:35,553 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-02 13:34:35,582 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-02 13:34:35,582 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.28 s. ]
2025-08-02 13:34:35,582 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:34:35,582 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-02 13:34:35,596 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-02 13:34:35,596 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:34:35,596 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-02 13:34:35,691 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-02 13:34:35,694 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-02 13:34:35,808 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.21 s. ]
2025-08-02 13:34:35,808 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:34:35,808 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-02 13:34:35,815 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-02 13:34:35,815 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 13:34:35,815 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-02 13:34:41,442 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 5.63 s. ]
2025-08-02 13:34:41,443 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 13:34:41,443 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11.8 s. ]
2025-08-02 13:34:41,443 [main] INFO  com.polarion.platform.startup - ****************************************************************
