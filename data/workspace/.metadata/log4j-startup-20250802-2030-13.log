2025-08-02 20:30:13,590 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 20:30:13,590 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-02 20:30:13,590 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 20:30:13,590 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-02 20:30:13,590 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-02 20:30:13,591 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 20:30:13,591 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-02 20:30:18,013 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-02 20:30:18,192 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.179 s. ]
2025-08-02 20:30:18,192 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-02 20:30:18,242 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0498 s. ]
2025-08-02 20:30:18,292 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-02 20:30:18,438 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 16 s. ]
2025-08-02 20:30:18,667 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.08 s. ]
2025-08-02 20:30:18,770 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 20:30:18,770 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-02 20:30:18,805 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.14 s. ]
2025-08-02 20:30:18,805 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 20:30:18,805 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-02 20:30:18,811 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-08-02 20:30:18,811 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-08-02 20:30:18,811 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-08-02 20:30:18,811 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-08-02 20:30:18,811 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-02 20:30:18,811 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-08-02 20:30:18,818 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-02 20:30:18,973 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-02 20:30:19,098 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-02 20:30:19,617 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.81 s. ]
2025-08-02 20:30:19,631 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 20:30:19,631 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-02 20:30:19,921 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-02 20:30:19,934 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.32 s. ]
2025-08-02 20:30:19,964 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 20:30:19,964 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-02 20:30:19,969 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-02 20:30:20,036 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-02 20:30:20,132 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-02 20:30:20,214 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-02 20:30:20,249 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-02 20:30:20,290 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-02 20:30:20,340 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-02 20:30:20,399 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-02 20:30:20,470 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-02 20:30:20,470 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.54 s. ]
2025-08-02 20:30:20,471 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 20:30:20,471 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-02 20:30:20,488 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-02 20:30:20,489 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 20:30:20,489 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-02 20:30:20,615 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-02 20:30:20,618 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-02 20:30:20,750 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.26 s. ]
2025-08-02 20:30:20,751 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 20:30:20,751 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-02 20:30:20,759 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-02 20:30:20,759 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 20:30:20,759 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-02 20:30:25,944 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 5.19 s. ]
2025-08-02 20:30:25,944 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 20:30:25,944 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 12.4 s. ]
2025-08-02 20:30:25,944 [main] INFO  com.polarion.platform.startup - ****************************************************************
