2025-08-02 12:43:15,258 [main | u:p | u:p] ERROR com.polarion.platform.repository.internal.config.RepositoryConfigService$ConfigProblemCatcher - Failed to work with configuration from location /WBS/.polarion/repositories/repositories.xml:
[/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
com.polarion.platform.repository.config.RepositoryConfigurationException: [/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:87) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocations(AbstractDataHandler.java:61) ~[platform-repository.jar:?]
	at $IDataHandler_19869174a0f.readLocations($IDataHandler_19869174a0f.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readLocations(RepositoryConfigService.java:291) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$3.runImpl(RepositoryConfigService.java:328) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction$1.runWEx(RepositoryConfigService.java:113) ~[platform-repository.jar:?]
	at com.polarion.core.util.RunnableWEx.runWRet(RunnableWEx.java:61) ~[util.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction.run(RepositoryConfigService.java:123) ~[platform-repository.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:361) ~[?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:58) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:422) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:412) ~[platform.jar:?]
	at $ISecurityService_1986917482a.doAsSystemUser($ISecurityService_1986917482a.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readConfiguration(RepositoryConfigService.java:324) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getConfigurationImpl(RepositoryConfigService.java:239) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfigurationImpl(RepositoryConfigService.java:199) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:177) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.addConfigurationListener(RepositoryConfigService.java:263) ~[platform-repository.jar:?]
	at $IRepositoryConfigService_19869174838.addConfigurationListener($IRepositoryConfigService_19869174838.java) ~[?:?]
	at com.polarion.platform.repository.external.internal.ExternalRepositoryProviderRegistry.initialize(ExternalRepositoryProviderRegistry.java:146) ~[platform-repository.jar:?]
	at $IExternalRepositoryProviderRegistry_198691748f4.initialize($IExternalRepositoryProviderRegistry_198691748f4.java) ~[?:?]
	at $IExternalRepositoryProviderRegistry_198691748f3.initialize($IExternalRepositoryProviderRegistry_198691748f3.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule.initModule(RevisionsPersistenceModule.java:353) ~[platform-persistence.jar:?]
	at $IObjectPersistenceModule_198691749e3.initModule($IObjectPersistenceModule_198691749e3.java) ~[?:?]
	at com.polarion.subterra.persistence.internal.PersistenceEngine.initModule(PersistenceEngine.java:251) ~[subterra-uniform-persistence.jar:?]
	at $IPersistenceEngine_198691749cb.initModule($IPersistenceEngine_198691749cb.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.LowLevelDataService.boot(LowLevelDataService.java:352) ~[platform-persistence.jar:?]
	at $ILowLevelPersistence_198691748f0.boot($ILowLevelPersistence_198691748f0.java) ~[?:?]
	at $ILowLevelPersistence_198691748ef.boot($ILowLevelPersistence_198691748ef.java) ~[?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.lambda$0(PlatformService.java:294) ~[launcher.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:423) [?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:69) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:417) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:407) [platform.jar:?]
	at $ISecurityService_1986917482b.doAsSystemUser($ISecurityService_1986917482b.java) [?:?]
	at $ISecurityService_1986917482a.doAsSystemUser($ISecurityService_1986917482a.java) [?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.bootPlatform(PlatformService.java:286) [launcher.jar:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.start(PlatformService.java:92) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.runImpl(PolarionSVNApplication.java:139) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.run(PolarionSVNApplication.java:94) [launcher.jar:?]
	at com.polarion.core.boot.launchers.BasicAppLauncher.launch(BasicAppLauncher.java:53) [boot.jar:?]
	at com.polarion.core.boot.impl.AppLaunchersManager.start(AppLaunchersManager.java:184) [boot.jar:?]
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196) [org.eclipse.equinox.app_1.3.500.v20171221-2204.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:388) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:243) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:656) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:592) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.run(Main.java:1498) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.main(Main.java:1471) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
Caused by: com.thoughtworks.xstream.converters.ConversionException: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:77) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
Caused by: com.thoughtworks.xstream.mapper.CannotResolveClassException: AutoBranchGitLab
	at com.thoughtworks.xstream.mapper.DefaultMapper.realClass(DefaultMapper.java:81) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.DynamicProxyMapper.realClass(DynamicProxyMapper.java:55) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.PackageAliasingMapper.realClass(PackageAliasingMapper.java:88) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ClassAliasingMapper.realClass(ClassAliasingMapper.java:79) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ArrayMapper.realClass(ArrayMapper.java:74) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.SecurityMapper.realClass(SecurityMapper.java:71) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.CachingMapper.realClass(CachingMapper.java:47) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.util.HierarchicalStreams.readClassType(HierarchicalStreams.java:29) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readBareItem(AbstractCollectionConverter.java:131) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readItem(AbstractCollectionConverter.java:117) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readCompleteItem(AbstractCollectionConverter.java:147) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.ArrayConverter.unmarshal(ArrayConverter.java:54) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:72) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
2025-08-02 12:43:16,501 [Catalina-utility-4] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/oauth-feishu] - For security constraints with URL pattern [/userinfo] only the HTTP methods [POST GET] are covered. All other methods are uncovered.
2025-08-02 12:43:16,912 [Catalina-utility-4] FATAL org.springframework.web.servlet.DispatcherServlet - Context initialization failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#health()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#simpleHealth() mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1794) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264) [catalina.jar:9.0.53]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386) [catalina.jar:9.0.53]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628) [?:?]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#health()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#simpleHealth() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:636) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:603) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:318) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:378) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:75) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:288) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:286) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	... 30 more
2025-08-02 12:43:16,915 [Catalina-utility-4] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/checklist] - Servlet.init() for servlet [dispatcher] threw exception
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#health()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#simpleHealth() mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1794) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) ~[javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264) [catalina.jar:9.0.53]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386) [catalina.jar:9.0.53]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628) [?:?]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#health()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#simpleHealth() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:636) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:603) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:318) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:378) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:75) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:288) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:286) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	... 30 more
2025-08-02 12:43:16,917 [Catalina-utility-4] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/checklist] - Servlet [dispatcher] in web application [/polarion/checklist] threw load() exception
java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#health()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#simpleHealth() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:636) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:603) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:318) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:378) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:75) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:288) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:286) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) ~[javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264) [catalina.jar:9.0.53]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386) [catalina.jar:9.0.53]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628) [?:?]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-02 12:43:21,189 [PreLoadDataService | u:p] ERROR com.polarion.subterra.base.data.model.CustomField - Unknown custom field type 'enum' - using 'string' - for field with id 'taskType' for 'WorkItem task /default/WBS'
2025-08-02 12:46:38,910 [ajp-nio-127.0.0.1-8889-exec-3 | cID:691a76af-c0a844bd-3495cc8d-e4a7fadd] FATAL org.springframework.web.servlet.DispatcherServlet - Context initialization failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#health()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#simpleHealth() mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1794) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:767) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:128) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#health()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#simpleHealth() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:636) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:603) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:318) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:378) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:75) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:288) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:286) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	... 37 more
2025-08-02 12:46:38,915 [ajp-nio-127.0.0.1-8889-exec-3 | cID:691a76af-c0a844bd-3495cc8d-e4a7fadd] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/checklist] - Servlet.init() for servlet [dispatcher] threw exception
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#health()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#simpleHealth() mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1794) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) ~[javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:767) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:128) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#health()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#simpleHealth() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:636) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:603) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:318) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:378) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:75) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:288) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:286) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	... 37 more
2025-08-02 12:46:38,919 [ajp-nio-127.0.0.1-8889-exec-3 | cID:691a76af-c0a844bd-3495cc8d-e4a7fadd] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/checklist].[dispatcher] - Allocate exception for servlet [dispatcher]
java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#health()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#simpleHealth() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:636) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:603) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:318) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:378) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:75) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:288) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:286) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) ~[javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:767) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:128) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-02 12:46:38,948 [ajp-nio-127.0.0.1-8889-exec-2 | cID:691a76af-c0a844bd-3495cc8d-1198d4c6] FATAL org.springframework.web.servlet.DispatcherServlet - Context initialization failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#health()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#simpleHealth() mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1794) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:767) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:128) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#health()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#simpleHealth() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:636) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:603) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:318) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:378) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:75) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:288) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:286) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	... 37 more
2025-08-02 12:46:38,949 [ajp-nio-127.0.0.1-8889-exec-2 | cID:691a76af-c0a844bd-3495cc8d-1198d4c6] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/checklist] - Servlet.init() for servlet [dispatcher] threw exception
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#health()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#simpleHealth() mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1794) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) ~[javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:767) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:128) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#health()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#simpleHealth() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:636) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:603) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:318) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:378) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:75) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:288) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:286) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	... 37 more
2025-08-02 12:46:38,953 [ajp-nio-127.0.0.1-8889-exec-2 | cID:691a76af-c0a844bd-3495cc8d-1198d4c6] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/checklist].[dispatcher] - Allocate exception for servlet [dispatcher]
java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'healthController' method 
com.checklist.controller.HealthController#health()
to {GET /health}: There is already 'healthController' bean method
com.checklist.controller.HealthController#simpleHealth() mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:636) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:603) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:318) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:378) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:75) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:288) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684) ~[?:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:286) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) ~[javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.allocate(StandardWrapper.java:767) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:128) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
