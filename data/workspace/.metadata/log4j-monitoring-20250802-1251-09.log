2025-08-02 12:51:13,781 [main | u:p] INFO  com.polarion.platform.monitoring - Full monitoring results are stored in file /opt/polarion/data/workspace/monitoring/results.txt
2025-08-02 12:51:13,820 [main | u:p] INFO  com.polarion.platform.monitoring - Executing actions from stage PREBOOT
2025-08-02 12:51:13,821 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 12:51:13,822 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,420.98 GB
 [Sat Aug 02 12:51:13 CST 2025]
2025-08-02 12:51:14,062 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.jvm.version'
2025-08-02 12:51:14,062 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.jvm.version (JVM Version) = JVM Version 
Microsoft OpenJDK 64-Bit Server VM 11.0.27+6-LTS
 [Sat Aug 02 12:51:14 CST 2025]
2025-08-02 12:51:14,065 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.os.version'
2025-08-02 12:51:14,065 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.os.version (OS Version) = OS Version 
Mac OS X 15.5 aarch64
 [Sat Aug 02 12:51:14 CST 2025]
2025-08-02 12:51:14,068 [main | u:p] INFO  com.polarion.platform.monitoring - Finished with actions from stage PREBOOT: {OK=3}
2025-08-02 12:51:18,832 [main | u:p] INFO  com.polarion.platform.monitoring - Next slow periodic actions will be executed on Sun Aug 03 01:00:18 CST 2025
2025-08-02 12:51:18,834 [main] INFO  com.polarion.platform.monitoring - Executing actions from stage POSTBOOT
2025-08-02 12:51:18,834 [main] INFO  com.polarion.platform.monitoring - Executing action 'polarion.info.cache.statistics'
2025-08-02 12:51:18,844 [main] INFO  com.polarion.platform.monitoring - polarion.info.cache.statistics (Statistics of caches) = Statistics of caches 
                         CACHE       HITS     MISSES  HIT_RATIO       GETS       PUTS    REMOVAL   EVICTION 
     attachments-content-cache          0          0          0%          0          0          0          0 
                baseline-cache          0          0          0%          0          0          0          0 
                            bq          0          0          0%          0          0          0          0 
                dao-attachment          0          0          0%          0          0          0          0 
                   dao-comment          0          0          0%          0          0          0          0 
                    dao-global          0          4          0%          4          0          0          0 
                    dao-module          0          0          0%          0          0          0          0 
          dao-moduleattachment          0          0          0%          0          0          0          0 
             dao-modulecomment          0          0          0%          0          0          0          0 
                      dao-plan          0          0          0%          0          0          0          0 
                   dao-project          0          0          0%          0          0          0          0 
              dao-projectgroup          0          6          0%          6          3          0          0 
                  dao-richpage          0          0          0%          0          0          0          0 
        dao-richpageattachment          0          0          0%          0          0          0          0 
                   dao-testrun          0          0          0%          0          0          0          0 
         dao-testrunattachment          0          0          0%          0          0          0          0 
                      dao-user          0          0          0%          0          0          0          0 
                  dao-wikipage          0          0          0%          0          0          0          0 
        dao-wikipageattachment          0          0          0%          0          0          0          0 
                  dao-workitem          0          0          0%          0          0          0          0 
      derived-linked-revisions          0          0          0%          0          0          0          0 
                  formulas-svg          0          0          0%          0          0          0          0 
                github-commits          0          0          0%          0          0          0          0 
            historical-queries          0          0          0%          0          0          0          0 
      historical-queries-sizes          0          0          0%          0          0          0          0 
        oslc-linked-item-cache          0          0          0%          0          0          0          0 
               plan-statistics          0          0          0%          0          0          0          0 
                   rmd-default          4          5         44%          9          7          0          0 
                   ss-combined          0          0          0%          0          0          0          0 
                    ss-context          0          0          0%          0          0          0          0 
                     wiki-page          2          8         20%         10          9          1          0 
              wiki-page-exists          0          0          0%          0          9          1          0 

 [Sat Aug 02 12:51:18 CST 2025]
2025-08-02 12:51:18,849 [main] INFO  com.polarion.platform.monitoring - Finished with actions from stage POSTBOOT: {OK=1}
2025-08-02 13:01:18,841 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 13:01:18,853 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 13:01:18,861 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 13:01:18 CST 2025]
2025-08-02 13:01:18,887 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 13:01:18,887 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,420.87 GB
 [Sat Aug 02 13:01:18 CST 2025]
2025-08-02 13:01:19,068 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 13:01:19,068 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 13:01:19 CST 2025]
2025-08-02 13:01:19,070 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 13:01:19,070 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 16; Time: 0.238 s.
 [Sat Aug 02 13:01:19 CST 2025]
2025-08-02 13:01:19,072 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 13:01:19,073 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 13:01:19 CST 2025]
2025-08-02 13:01:19,185 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 13:01:19,186 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 13:01:19 CST 2025]
2025-08-02 13:01:19,323 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 13:11:18,849 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 13:11:18,854 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 13:11:18,856 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 13:11:18 CST 2025]
2025-08-02 13:11:18,874 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 13:11:18,875 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,420.24 GB
 [Sat Aug 02 13:11:18 CST 2025]
2025-08-02 13:11:19,039 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 13:11:19,039 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 13:11:19 CST 2025]
2025-08-02 13:11:19,041 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 13:11:19,041 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 16; Time: 0.238 s.
 [Sat Aug 02 13:11:19 CST 2025]
2025-08-02 13:11:19,043 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 13:11:19,043 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 13:11:19 CST 2025]
2025-08-02 13:11:19,147 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 13:11:19,148 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 13:11:19 CST 2025]
2025-08-02 13:11:19,271 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
