2025-08-02 12:03:21,587 [Catalina-utility-6] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-08-02 12:03:22,304 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-08-02 12:03:22,306 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[90]')
2025-08-02 12:03:22,306 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[90]')
2025-08-02 12:03:22,308 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[96]')
2025-08-02 12:03:22,312 [Catalina-utility-6] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-08-02 12:03:22,313 [Catalina-utility-6] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[90]')
2025-08-02 12:03:22,313 [Catalina-utility-6] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[92]')
2025-08-02 12:03:23,075 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-08-02 12:03:23,182 [ajp-nio-127.0.0.1-8889-exec-1 | cID:68f2db9c-c0a844bd-3c206c5d-7254f941] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-08-02 12:03:23,199 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-08-02 12:03:23,200 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
