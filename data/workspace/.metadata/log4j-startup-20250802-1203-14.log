2025-08-02 12:03:14,203 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 12:03:14,204 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-02 12:03:14,204 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 12:03:14,204 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-02 12:03:14,204 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-02 12:03:14,204 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:03:14,204 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-02 12:03:18,459 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-02 12:03:18,584 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.125 s. ]
2025-08-02 12:03:18,584 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-02 12:03:18,627 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0429 s. ]
2025-08-02 12:03:18,684 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-02 12:03:18,845 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 16 s. ]
2025-08-02 12:03:19,090 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.89 s. ]
2025-08-02 12:03:19,175 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:03:19,175 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-02 12:03:19,203 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-02 12:03:19,203 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:03:19,203 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-02 12:03:19,208 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-08-02 12:03:19,208 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-08-02 12:03:19,208 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-08-02 12:03:19,208 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-08-02 12:03:19,208 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-08-02 12:03:19,208 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-08-02 12:03:19,216 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-02 12:03:19,335 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-02 12:03:19,433 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-02 12:03:19,961 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.76 s. ]
2025-08-02 12:03:19,974 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:03:19,974 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-02 12:03:20,208 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-02 12:03:20,223 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.26 s. ]
2025-08-02 12:03:20,248 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:03:20,248 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-02 12:03:20,252 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-02 12:03:20,299 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-02 12:03:20,355 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-02 12:03:20,389 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-02 12:03:20,410 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-02 12:03:20,436 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-02 12:03:20,460 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-02 12:03:20,483 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-02 12:03:20,508 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-02 12:03:20,508 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.29 s. ]
2025-08-02 12:03:20,509 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:03:20,509 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-02 12:03:20,522 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-02 12:03:20,523 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:03:20,523 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-02 12:03:20,631 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-02 12:03:20,637 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-02 12:03:20,771 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.25 s. ]
2025-08-02 12:03:20,773 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:03:20,773 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-02 12:03:20,782 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-02 12:03:20,782 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:03:20,782 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-02 12:03:23,238 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.46 s. ]
2025-08-02 12:03:23,238 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 12:03:23,238 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.03 s. ]
2025-08-02 12:03:23,238 [main] INFO  com.polarion.platform.startup - ****************************************************************
