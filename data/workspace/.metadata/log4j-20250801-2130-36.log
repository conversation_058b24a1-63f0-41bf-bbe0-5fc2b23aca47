2025-08-01 21:30:37,015 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using logging context STANDALONE
2025-08-01 21:30:37,033 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Launchers manager started...
2025-08-01 21:30:37,034 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using home directory /opt/polarion/polarion
2025-08-01 21:30:37,034 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using root directory /opt/polarion
2025-08-01 21:30:37,035 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using workspace directory /opt/polarion/data/workspace
2025-08-01 21:30:37,035 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using config directory /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-08-01 21:30:37,036 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading external properties ...
2025-08-01 21:30:37,036 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using external property file /opt/polarion/etc/polarion.properties
2025-08-01 21:30:37,037 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading internal properties ...
2025-08-01 21:30:37,066 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Host: zhangwendeMini2.lan (**************)
2025-08-01 21:30:37,074 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Product: com.polarion.alm
2025-08-01 21:30:37,075 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Version: 3.22.1
2025-08-01 21:30:37,075 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Build: 20220419-1528-22_R1-be3adceb
2025-08-01 21:30:37,077 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/fasnote
2025-08-01 21:30:37,081 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/2404
2025-08-01 21:30:37,081 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Extensions: [exts]
2025-08-01 21:30:37,099 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace location: /opt/polarion/data/workspace
2025-08-01 21:30:37,102 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace lock acquired
2025-08-01 21:30:37,108 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Found applications: [polarion.server, polarion.coordinator, polarion.rt]
2025-08-01 21:30:37,110 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Starting application: polarion.server
2025-08-01 21:30:37,166 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Application extension successfully read
2025-08-01 21:30:37,202 [main] INFO  com.polarion.platform.internal.SystemStatistics - Initializing monitoring, isThreadCpuTimeSupported: true, isThreadContentionMonitoringSupported: true, isThreadAllocatedMemorySupported: true
2025-08-01 21:30:37,202 [main] INFO  com.polarion.platform.internal.SystemStatistics - State before enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-08-01 21:30:37,202 [main] INFO  com.polarion.platform.internal.SystemStatistics - State after enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-08-01 21:30:37,206 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-01 21:30:37,207 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-01 21:30:37,208 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-01 21:30:37,211 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-01 21:30:37,211 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-01 21:30:37,212 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 21:30:37,212 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-01 21:30:37,214 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** Java system properties listing: 
2025-08-01 21:30:37,365 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminPasswd = admin
2025-08-01 21:30:37,366 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminUser = admin
2025-08-01 21:30:37,366 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.auth = false
2025-08-01 21:30:37,366 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.host = 
2025-08-01 21:30:37,366 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.password = **PASSWORD**HIDDEN**
2025-08-01 21:30:37,366 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.port = 25
2025-08-01 21:30:37,368 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.user = 
2025-08-01 21:30:37,368 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - awt.toolkit = sun.lwawt.macosx.LWCToolkit
2025-08-01 21:30:37,369 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - base.url = http://localhost
2025-08-01 21:30:37,369 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - bfh.jobs.workdir = /opt/polarion/data/workspace/polarion-data/jobs
2025-08-01 21:30:37,369 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - BIRDir = /opt/polarion/data/BIR
2025-08-01 21:30:37,369 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - calculated.fields.mode = async
2025-08-01 21:30:37,369 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.activationHelpLink = https://polarion.plm.automation.siemens.com/getlicense
2025-08-01 21:30:37,369 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.server = https://license.polarion.com/licenseGenerator/generator/generate
2025-08-01 21:30:37,369 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.enabled = false
2025-08-01 21:30:37,369 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.url = http://www.gravatar.com/avatar/$emailHash$?d=identicon&s=50
2025-08-01 21:30:37,369 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.application = polarion.server
2025-08-01 21:30:37,369 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.config = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-08-01 21:30:37,369 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.data = /opt/polarion/data
2025-08-01 21:30:37,369 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.eclipse = /opt/polarion/polarion
2025-08-01 21:30:37,377 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.home = /opt/polarion/polarion
2025-08-01 21:30:37,377 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.licenseDir = /opt/polarion/polarion/license
2025-08-01 21:30:37,377 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.internalPG = polarion:polarion@localhost:5434
2025-08-01 21:30:37,377 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.disabled = true
2025-08-01 21:30:37,377 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.receivers = 
2025-08-01 21:30:37,377 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.sender = 
2025-08-01 21:30:37,377 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.subject.prefix = 
2025-08-01 21:30:37,377 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.persistence.notifications.disabled = true
2025-08-01 21:30:37,378 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.propertyFile = /opt/polarion/etc/polarion.properties
2025-08-01 21:30:37,379 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.root = /opt/polarion
2025-08-01 21:30:37,379 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.workspace = /opt/polarion/data/workspace
2025-08-01 21:30:37,379 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.collaborationNotifications.enabled = true
2025-08-01 21:30:37,379 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.document.listStyle = 1ai
2025-08-01 21:30:37,379 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.loggingContext = STANDALONE
2025-08-01 21:30:37,379 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.preview.thumbnailsDataDir = /opt/polarion/data/workspace/previews-data/thumbnails
2025-08-01 21:30:37,379 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.cors.allowedOrigins = *
2025-08-01 21:30:37,379 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.enabled = true
2025-08-01 21:30:37,379 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.swaggerUi.enabled = true
2025-08-01 21:30:37,379 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.accEndpointUrl = https://acc.collab.sws.siemens.com
2025-08-01 21:30:37,379 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.baseDomain = sws.siemens.com
2025-08-01 21:30:37,379 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.shareEndpointUrl = https://share.sws.siemens.com
2025-08-01 21:30:37,379 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - content.types.user.table = /opt/polarion/polarion/plugins/com.polarion.core.boot_3.22.1/content-types.properties
2025-08-01 21:30:37,379 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlHostname = localhost
2025-08-01 21:30:37,379 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlPort = 8887
2025-08-01 21:30:37,379 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.location = Sandbox/
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.useUserId = true
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.limitedAccessMessage = You may create a project in the Sandbox project group (only). Please fill in the required properties below. For example:<br/><table><tr><td>Location:</td><td>Sandbox/MyFirstProject</td></tr><tr><td>ID:</td><td>MyFirstProject</td></tr></table><br/>Or use the suggested defaults.
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug = false
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.license.validation = true
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.machine.code.generation = true
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.security.validation = true
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.ALM = alm_vmodel
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Pro = alm_vmodel
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.QA = qa_vmodel
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Requirements = req_vmodel
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XBase = alm_vmodel
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XEnterprise = alm_vmodel
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XPro = alm_vmodel
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - derby.system.home = /opt/polarion/data/logs/derby
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.application = com.polarion.core.boot.app
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.commands = -application
com.polarion.core.boot.app
-data
/opt/polarion/data/workspace
-configuration
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
-dev
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
-os
linux
-ws
linux
-arch
arm64
-appId
polarion.server

2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.home.location = file:/opt/polarion/polarion/plugins/
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.p2.data.area = @config.dir/.p2
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.pde.launch = true
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.startTime = *************
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.stateSaveDelayInterval = 30000
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - enableCreateAccountForm = false
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - equinox.init.uuid = true
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - error.report.email = 
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.encoding = UTF-8
2025-08-01 21:30:37,382 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.separator = /
2025-08-01 21:30:37,383 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ftp.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-08-01 21:30:37,383 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gopherProxySet = false
2025-08-01 21:30:37,383 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gosh.args = --nointeractive
2025-08-01 21:30:37,383 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - htpasswd.path = htpasswd
2025-08-01 21:30:37,383 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - http.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-08-01 21:30:37,383 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - index.activities = /opt/polarion/data/workspace/polarion-data/index
2025-08-01 21:30:37,383 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.graphicsenv = sun.awt.CGraphicsEnvironment
2025-08-01 21:30:37,383 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.headless = true
2025-08-01 21:30:37,383 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.printerjob = sun.lwawt.macosx.CPrinterJob
2025-08-01 21:30:37,383 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.path = /opt/polarion/polarion/plugins/org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:
2025-08-01 21:30:37,383 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.version = 55.0
2025-08-01 21:30:37,383 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.home = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home
2025-08-01 21:30:37,383 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.io.tmpdir = /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/
2025-08-01 21:30:37,383 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.library.path = /Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
2025-08-01 21:30:37,383 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.name = OpenJDK Runtime Environment
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.version = 11.0.27+6-LTS
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.security.policy = /opt/polarion/polarion/policy
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.maintenance.version = 3
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.name = Java Platform API Specification
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.vendor = Oracle Corporation
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.version = 11
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor = Microsoft
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url = https://www.microsoft.com
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url.bug = https://github.com/microsoft/openjdk/issues
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.version = Microsoft-11367290
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version = 11.0.27
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version.date = 2025-04-15
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.compressedOopsMode = Zero based
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.info = mixed mode
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.name = OpenJDK 64-Bit Server VM
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.name = Java Virtual Machine Specification
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.vendor = Oracle Corporation
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.version = 11
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.vendor = Microsoft
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.version = 11.0.27+6-LTS
2025-08-01 21:30:37,391 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - javasvn.timeout = 10000
2025-08-01 21:30:37,392 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - jdk.debug = release
2025-08-01 21:30:37,392 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ldap.bind.password = **PASSWORD**HIDDEN**
2025-08-01 21:30:37,392 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.audit.enabled = true
2025-08-01 21:30:37,392 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.auto.scan.enabled = true
2025-08-01 21:30:37,392 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.size = 100
2025-08-01 21:30:37,392 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.ttl = 1800
2025-08-01 21:30:37,392 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.check.interval = 0
2025-08-01 21:30:37,392 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.expired = true
2025-08-01 21:30:37,392 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.local.files = true
2025-08-01 21:30:37,392 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.features = all
2025-08-01 21:30:37,392 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.max.users = 10
2025-08-01 21:30:37,392 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.plugin.id = com.fasnote.alm.plugin.manage
2025-08-01 21:30:37,392 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.mode = true
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.show.machine.code = true
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.machine.binding = true
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.network.validation = true
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.directory = dev-licenses
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.encryption.enabled = false
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.hot.reload.enabled = true
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.log.level = DEBUG
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.machine.binding.enabled = false
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.max.plugins = 1000
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.scan.interval = 60
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.signature.validation.enabled = false
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.validation.timeout = 1000
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - licenseForNewUserAccount = 
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - line.separator = 

2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.contextSelector = org.apache.logging.log4j.core.selector.BasicContextSelector
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.loggerContextFactory = org.apache.logging.log4j.core.impl.Log4jContextFactory
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - logDir = /opt/polarion/data/workspace/.metadata/
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - login = polarion
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - mavenConfigDir = /opt/polarion/polarion/../maven
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - minimalPasswordLength = **PASSWORD**HIDDEN**
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.equinox.simpleconfigurator.configUrl = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/org.eclipse.equinox.simpleconfigurator/bundles.info
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.lyo.oslc4j.strictDatatypes = false
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.executionenvironment = OSGi/Minimum-1.0, OSGi/Minimum-1.1, OSGi/Minimum-1.2, JavaSE/compact1-1.8, JavaSE/compact2-1.8, JavaSE/compact3-1.8, JRE-1.1, J2SE-1.2, J2SE-1.3, J2SE-1.4, J2SE-1.5, JavaSE-1.6, JavaSE-1.7, JavaSE-1.8, JavaSE-9, JavaSE-10, JavaSE-11
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.language = zh
2025-08-01 21:30:37,393 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.name = MacOSX
2025-08-01 21:30:37,394 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.version = 15.5.0
2025-08-01 21:30:37,394 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.processor = aarch64
2025-08-01 21:30:37,394 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.storage = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-08-01 21:30:37,394 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.capabilities = osgi.ee; osgi.ee="OSGi/Minimum"; version:List<Version>="1.0, 1.1, 1.2", osgi.ee; osgi.ee="JRE"; version:List<Version>="1.0, 1.1", osgi.ee; osgi.ee="JavaSE"; version:List<Version>="1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact1"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact2"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact3"; version:List<Version>="1.8, 9.0, 10.0, 11.0"
2025-08-01 21:30:37,394 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.packages = com.sun.jarsigner, com.sun.java.accessibility.util, com.sun.javadoc, com.sun.jdi, com.sun.jdi.connect, com.sun.jdi.connect.spi, com.sun.jdi.event, com.sun.jdi.request, com.sun.jndi.ldap.spi, com.sun.management, com.sun.net.httpserver, com.sun.net.httpserver.spi, com.sun.nio.file, com.sun.nio.sctp, com.sun.security.auth, com.sun.security.auth.callback, com.sun.security.auth.login, com.sun.security.auth.module, com.sun.security.jgss, com.sun.source.doctree, com.sun.source.tree, com.sun.source.util, com.sun.tools.attach, com.sun.tools.attach.spi, com.sun.tools.javac, com.sun.tools.javadoc, com.sun.tools.jconsole, java.applet, java.awt, java.awt.color, java.awt.datatransfer, java.awt.desktop, java.awt.dnd, java.awt.event, java.awt.font, java.awt.geom, java.awt.im, java.awt.im.spi, java.awt.image, java.awt.image.renderable, java.awt.print, java.beans, java.beans.beancontext, java.io, java.lang, java.lang.annotation, java.lang.instrument, java.lang.invoke, java.lang.management, java.lang.module, java.lang.ref, java.lang.reflect, java.math, java.net, java.net.http, java.net.spi, java.nio, java.nio.channels, java.nio.channels.spi, java.nio.charset, java.nio.charset.spi, java.nio.file, java.nio.file.attribute, java.nio.file.spi, java.rmi, java.rmi.activation, java.rmi.dgc, java.rmi.registry, java.rmi.server, java.security, java.security.acl, java.security.cert, java.security.interfaces, java.security.spec, java.sql, java.text, java.text.spi, java.time, java.time.chrono, java.time.format, java.time.temporal, java.time.zone, java.util, java.util.concurrent, java.util.concurrent.atomic, java.util.concurrent.locks, java.util.function, java.util.jar, java.util.logging, java.util.prefs, java.util.regex, java.util.spi, java.util.stream, java.util.zip, javax.accessibility, javax.annotation.processing, javax.crypto, javax.crypto.interfaces, javax.crypto.spec, javax.imageio, javax.imageio.event, javax.imageio.metadata, javax.imageio.plugins.bmp, javax.imageio.plugins.jpeg, javax.imageio.plugins.tiff, javax.imageio.spi, javax.imageio.stream, javax.lang.model, javax.lang.model.element, javax.lang.model.type, javax.lang.model.util, javax.management, javax.management.loading, javax.management.modelmbean, javax.management.monitor, javax.management.openmbean, javax.management.relation, javax.management.remote, javax.management.remote.rmi, javax.management.timer, javax.naming, javax.naming.directory, javax.naming.event, javax.naming.ldap, javax.naming.spi, javax.net, javax.net.ssl, javax.print, javax.print.attribute, javax.print.attribute.standard, javax.print.event, javax.rmi.ssl, javax.script, javax.security.auth, javax.security.auth.callback, javax.security.auth.kerberos, javax.security.auth.login, javax.security.auth.spi, javax.security.auth.x500, javax.security.cert, javax.security.sasl, javax.smartcardio, javax.sound.midi, javax.sound.midi.spi, javax.sound.sampled, javax.sound.sampled.spi, javax.sql, javax.sql.rowset, javax.sql.rowset.serial, javax.sql.rowset.spi, javax.swing, javax.swing.border, javax.swing.colorchooser, javax.swing.event, javax.swing.filechooser, javax.swing.plaf, javax.swing.plaf.basic, javax.swing.plaf.metal, javax.swing.plaf.multi, javax.swing.plaf.nimbus, javax.swing.plaf.synth, javax.swing.table, javax.swing.text, javax.swing.text.html, javax.swing.text.html.parser, javax.swing.text.rtf, javax.swing.tree, javax.swing.undo, javax.tools, javax.transaction.xa, javax.xml, javax.xml.catalog, javax.xml.crypto, javax.xml.crypto.dom, javax.xml.crypto.dsig, javax.xml.crypto.dsig.dom, javax.xml.crypto.dsig.keyinfo, javax.xml.crypto.dsig.spec, javax.xml.datatype, javax.xml.namespace, javax.xml.parsers, javax.xml.stream, javax.xml.stream.events, javax.xml.stream.util, javax.xml.transform, javax.xml.transform.dom, javax.xml.transform.sax, javax.xml.transform.stax, javax.xml.transform.stream, javax.xml.validation, javax.xml.xpath, jdk.dynalink, jdk.dynalink.beans, jdk.dynalink.linker, jdk.dynalink.linker.support, jdk.dynalink.support, jdk.javadoc.doclet, jdk.jfr, jdk.jfr.consumer, jdk.jshell, jdk.jshell.execution, jdk.jshell.spi, jdk.jshell.tool, jdk.management.jfr, jdk.nashorn.api.scripting, jdk.nashorn.api.tree, jdk.net, jdk.nio, jdk.security.jarsigner, jdk.swing.interop, netscape.javascript, org.ietf.jgss, org.w3c.dom, org.w3c.dom.bootstrap, org.w3c.dom.css, org.w3c.dom.events, org.w3c.dom.html, org.w3c.dom.ls, org.w3c.dom.ranges, org.w3c.dom.stylesheets, org.w3c.dom.traversal, org.w3c.dom.views, org.w3c.dom.xpath, org.xml.sax, org.xml.sax.ext, org.xml.sax.helpers, sun.misc, sun.reflect
2025-08-01 21:30:37,394 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.uuid = fa01ff43-c468-4da3-a3c6-3ea8f04e01e4
2025-08-01 21:30:37,394 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.vendor = Eclipse
2025-08-01 21:30:37,394 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.version = 1.9.0
2025-08-01 21:30:37,394 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.extension = true
2025-08-01 21:30:37,394 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.fragment = true
2025-08-01 21:30:37,394 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.requirebundle = true
2025-08-01 21:30:37,394 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.client.readbuffer.usedirect = true
2025-08-01 21:30:37,394 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.server.readbuffer.usedirect = true
2025-08-01 21:30:37,394 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.arch = aarch64
2025-08-01 21:30:37,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.name = Mac OS X
2025-08-01 21:30:37,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.version = 15.5
2025-08-01 21:30:37,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.arch = arm64
2025-08-01 21:30:37,395 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles = reference:file:/opt/polarion/polarion/plugins/org.eclipse.equinox.simpleconfigurator_1.3.0.v20180502-1828.jar@1:start
2025-08-01 21:30:37,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles.defaultStartLevel = 4
2025-08-01 21:30:37,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.checkConfiguration = true
2025-08-01 21:30:37,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation = true
2025-08-01 21:30:37,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation.default = true
2025-08-01 21:30:37,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.area = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
2025-08-01 21:30:37,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.cascaded = false
2025-08-01 21:30:37,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.dev = file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
2025-08-01 21:30:37,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework = file:/opt/polarion/polarion/plugins/org.eclipse.osgi_3.13.0.v20180409-1500.jar
2025-08-01 21:30:37,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.shape = jar
2025-08-01 21:30:37,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.useSystemProperties = true
2025-08-01 21:30:37,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.install.area = file:/opt/polarion/polarion/plugins/
2025-08-01 21:30:37,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.instance.area = file:/opt/polarion/data/workspace/
2025-08-01 21:30:37,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.logfile = /opt/polarion/data/workspace/.metadata/.log
2025-08-01 21:30:37,396 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.nl = zh_CN_#Hans
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.os = linux
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.syspath = /opt/polarion/polarion/plugins
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.tracefile = /opt/polarion/data/workspace/.metadata/trace.log
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.ws = linux
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - password = **PASSWORD**HIDDEN**
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - path.separator = :
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfbox.fontcache = /opt/polarion/data/workspace/polarion-data
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfexport.config = /opt/polarion/polarion/configuration/pdfexport.xml
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.id = polarion-shared
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.url = file:///opt/polarion/data/shared-maven-repo
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.maven.location.maven2 = /opt/polarion/polarion/../maven/distribution
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.size = 100
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.with.history = false
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.tx.doc.cache.size = 100
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.alm = https://polarion.plm.automation.siemens.com/products/polarion-alm
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.qa = https://polarion.plm.automation.siemens.com/products/polarion-qa
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/polarion-requirements
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - repo = http://localhost/repo
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - rolesForNewUserAccount = user
2025-08-01 21:30:37,397 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - RRDir = /opt/polarion/data/RR
2025-08-01 21:30:37,399 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - SDKDir = /opt/polarion/polarion/SDK
2025-08-01 21:30:37,402 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - secure.approvals = false
2025-08-01 21:30:37,406 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - shutdownCatchPhrase = shutdown
2025-08-01 21:30:37,406 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - simple.profiler.enabled = false
2025-08-01 21:30:37,406 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - skip.data.preloading = false
2025-08-01 21:30:37,406 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - socksNonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-08-01 21:30:37,406 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stderr.encoding = UTF-8
2025-08-01 21:30:37,406 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stdout.encoding = UTF-8
2025-08-01 21:30:37,406 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - storeUrl.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/licensing?product=REQUIREMENTS
2025-08-01 21:30:37,406 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.arch.data.model = 64
2025-08-01 21:30:37,411 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.boot.library.path = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/lib
2025-08-01 21:30:37,411 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.endian = little
2025-08-01 21:30:37,411 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.isalist = 
2025-08-01 21:30:37,411 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.io.unicode.encoding = UnicodeBig
2025-08-01 21:30:37,411 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.command = org.eclipse.equinox.launcher.Main -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -configuration file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/ -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server
2025-08-01 21:30:37,411 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.launcher = SUN_STANDARD
2025-08-01 21:30:37,411 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.jnu.encoding = UTF-8
2025-08-01 21:30:37,411 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.management.compiler = HotSpot 64-Bit Tiered Compilers
2025-08-01 21:30:37,411 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.os.patch.level = unknown
2025-08-01 21:30:37,411 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.contact = https://polarion.plm.automation.siemens.com/techsupport/resources
2025-08-01 21:30:37,411 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.license.email = <EMAIL>
2025-08-01 21:30:37,411 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.sales.email = <EMAIL>
2025-08-01 21:30:37,411 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.access.file = /opt/polarion/data/svn/access
2025-08-01 21:30:37,411 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.passwd.file = /opt/polarion/data/svn/passwd
2025-08-01 21:30:37,411 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.http.encoding = UTF-8
2025-08-01 21:30:37,411 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.library.gnome-keyring.enabled = false
2025-08-01 21:30:37,411 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.ajp13-port = 8889
2025-08-01 21:30:37,412 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.request.safeListedHosts = 0.0.0.0
2025-08-01 21:30:37,412 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.country = CN
2025-08-01 21:30:37,412 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.dir = /Applications/Eclipse JEE.app/Contents/MacOS
2025-08-01 21:30:37,412 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.home = /Users/<USER>
2025-08-01 21:30:37,412 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.language = zh
2025-08-01 21:30:37,412 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.name = zhangwentian
2025-08-01 21:30:37,412 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.script = Hans
2025-08-01 21:30:37,412 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.timezone = Asia/Shanghai
2025-08-01 21:30:37,412 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - userAccountVault = /opt/polarion/data/workspace/user-account-vault
2025-08-01 21:30:37,412 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - workDir = /opt/polarion/data/workspace/polarion-data
2025-08-01 21:30:37,412 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** END of Java system properties
2025-08-01 21:30:37,435 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - XML parsers factory: com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl
2025-08-01 21:30:37,439 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Starting Platform...
2025-08-01 21:30:37,460 [main] INFO  PolarionLicensing - Searching for valid license file in /opt/polarion/polarion/license
2025-08-01 21:30:37,465 [main] INFO  PolarionLicensing - Trying to load license file polarion.lic
2025-08-01 21:30:37,469 [main] INFO  PolarionLicensing - The license file contains the following fields:
2025-08-01 21:30:37,469 [main] INFO  PolarionLicensing - *** License fields ***
2025-08-01 21:30:37,469 [main] INFO  PolarionLicensing - VariantsNamedUsers = 3
2025-08-01 21:30:37,469 [main] INFO  PolarionLicensing - almNamedUsers = 3
2025-08-01 21:30:37,469 [main] INFO  PolarionLicensing - dateCreated = 23.07.2025
2025-08-01 21:30:37,469 [main] INFO  PolarionLicensing - expirationDate = 21.08.2025
2025-08-01 21:30:37,469 [main] INFO  PolarionLicensing - hardwareKey = 8AG9-261C-1962
2025-08-01 21:30:37,469 [main] INFO  PolarionLicensing - licenseFormat = 2022
2025-08-01 21:30:37,469 [main] INFO  PolarionLicensing - licenseType = EVAL
2025-08-01 21:30:37,469 [main] INFO  PolarionLicensing - multiInstanceRunningInstances = 3
2025-08-01 21:30:37,469 [main] INFO  PolarionLicensing - userCompany = Polarion Eval
2025-08-01 21:30:37,469 [main] INFO  PolarionLicensing - *** License fields END ***
2025-08-01 21:30:37,573 [main] INFO  PolarionLicensing - Removing allocations by null
2025-08-01 21:30:37,577 [main] INFO  PolarionLicensing - STATS:concurrentVariantsUser,current:0,peak:0,limit:0
2025-08-01 21:30:37,578 [main] INFO  PolarionLicensing - 0 namedReviewerUser assignments (out of 0) loaded: []
2025-08-01 21:30:37,579 [main] INFO  PolarionLicensing - 0 concurrentReviewerUser assignments (out of 0) loaded: []
2025-08-01 21:30:37,579 [main] INFO  PolarionLicensing - STATS:concurrentReviewerUser,current:0,peak:0,limit:0
2025-08-01 21:30:37,580 [main] INFO  PolarionLicensing - 0 namedXBaseUser assignments (out of 0) loaded: []
2025-08-01 21:30:37,581 [main] INFO  PolarionLicensing - 0 concurrentXBaseUser assignments (out of 0) loaded: []
2025-08-01 21:30:37,581 [main] INFO  PolarionLicensing - STATS:concurrentXBaseUser,current:0,peak:0,limit:0
2025-08-01 21:30:37,581 [main] INFO  PolarionLicensing - 0 namedXProUser assignments (out of 0) loaded: []
2025-08-01 21:30:37,581 [main] INFO  PolarionLicensing - 0 concurrentXProUser assignments (out of 0) loaded: []
2025-08-01 21:30:37,581 [main] INFO  PolarionLicensing - STATS:concurrentXProUser,current:0,peak:0,limit:0
2025-08-01 21:30:37,581 [main] INFO  PolarionLicensing - 0 namedXEnterpriseUser assignments (out of 0) loaded: []
2025-08-01 21:30:37,582 [main] INFO  PolarionLicensing - 0 concurrentXEnterpriseUser assignments (out of 0) loaded: []
2025-08-01 21:30:37,584 [main] INFO  PolarionLicensing - STATS:concurrentXEnterpriseUser,current:0,peak:0,limit:0
2025-08-01 21:30:37,584 [main] INFO  PolarionLicensing - 0 namedProUser assignments (out of 0) loaded: []
2025-08-01 21:30:37,584 [main] INFO  PolarionLicensing - 0 concurrentProUser assignments (out of 0) loaded: []
2025-08-01 21:30:37,584 [main] INFO  PolarionLicensing - STATS:concurrentProUser,current:0,peak:0,limit:0
2025-08-01 21:30:37,584 [main] INFO  PolarionLicensing - 0 namedRequirementsUser assignments (out of 0) loaded: []
2025-08-01 21:30:37,585 [main] INFO  PolarionLicensing - 0 concurrentRequirementsUser assignments (out of 0) loaded: []
2025-08-01 21:30:37,585 [main] INFO  PolarionLicensing - STATS:concurrentRequirementsUser,current:0,peak:0,limit:0
2025-08-01 21:30:37,585 [main] INFO  PolarionLicensing - 0 namedQAUser assignments (out of 0) loaded: []
2025-08-01 21:30:37,585 [main] INFO  PolarionLicensing - 0 concurrentQAUser assignments (out of 0) loaded: []
2025-08-01 21:30:37,585 [main] INFO  PolarionLicensing - STATS:concurrentQAUser,current:0,peak:0,limit:0
2025-08-01 21:30:37,586 [main] INFO  PolarionLicensing - 3 namedALMUser assignments (out of 3) loaded: [admin, ou_d6f3139d36fb2978b33a8f870096b9e3, mTest]
2025-08-01 21:30:37,592 [main] INFO  PolarionLicensing - 0 concurrentALMUser assignments (out of 0) loaded: []
2025-08-01 21:30:37,593 [main] INFO  PolarionLicensing - STATS:concurrentALMUser,current:0,peak:0,limit:0
2025-08-01 21:30:37,593 [main] INFO  PolarionLicensing - 
*******************************************************************
 Polarion successfully activated
*******************************************************************
2025-08-01 21:30:37,899 [main] INFO  com.polarion.platform.internal.i18n.LanguageContributor - Localization file /META-INF/messages_en.properties read successfully (7789 messages)
2025-08-01 21:30:38,011 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Processing bundles:
2025-08-01 21:30:38,011 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [0] - org.eclipse.osgi
2025-08-01 21:30:38,012 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [1] - org.eclipse.equinox.simpleconfigurator
2025-08-01 21:30:38,013 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [2] - antlr
2025-08-01 21:30:38,014 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [3] - antlr4
2025-08-01 21:30:38,016 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [4] - antlr4-runtime
2025-08-01 21:30:38,016 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [5] - bcprov
2025-08-01 21:30:38,019 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [6] - com.auth0.java-jwt
2025-08-01 21:30:38,022 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [7] - com.fasnote.alm.auth.feishu
2025-08-01 21:30:38,036 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.fasnote.alm.auth.feishu to HiveMind
2025-08-01 21:30:38,036 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [8] - com.fasnote.alm.checklist
2025-08-01 21:30:38,036 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [9] - com.fasnote.alm.injection
2025-08-01 21:30:38,036 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [10] - com.fasnote.alm.plugin.manage
2025-08-01 21:30:38,051 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [11] - com.fasnote.alm.test
2025-08-01 21:30:38,065 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [12] - com.fasnote.alm.watermark
2025-08-01 21:30:38,066 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [13] - com.fasterxml.classmate
2025-08-01 21:30:38,072 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [14] - com.fasterxml.jackson
2025-08-01 21:30:38,079 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [15] - com.fasterxml.jackson.dataformat.jackson-dataformat-yaml
2025-08-01 21:30:38,083 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [16] - com.fasterxml.jackson.jaxrs
2025-08-01 21:30:38,085 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [17] - com.fasterxml.woodstox
2025-08-01 21:30:38,090 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [18] - com.finething.hesai.ai
2025-08-01 21:30:38,111 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.finething.hesai.ai to HiveMind
2025-08-01 21:30:38,111 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [19] - com.finething.hesai.defect
2025-08-01 21:30:38,112 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.finething.hesai.defect to HiveMind
2025-08-01 21:30:38,112 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [20] - com.google.gson
2025-08-01 21:30:38,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [21] - com.google.guava
2025-08-01 21:30:38,122 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [22] - com.google.guava.failureaccess
2025-08-01 21:30:38,123 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [23] - com.ibm.icu.icu4j
2025-08-01 21:30:38,130 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [24] - com.icl.saxon
2025-08-01 21:30:38,134 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [25] - com.jayway.jsonpath.json-path
2025-08-01 21:30:38,136 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [26] - com.jcraft.jsch
2025-08-01 21:30:38,144 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [27] - com.networknt.json-schema-validator
2025-08-01 21:30:38,144 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [28] - com.nimbusds.content-type
2025-08-01 21:30:38,145 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [29] - com.nimbusds.nimbus-jose-jwt
2025-08-01 21:30:38,145 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [30] - com.opensymphony.quartz
2025-08-01 21:30:38,150 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [31] - com.polarion.alm.ProjectPlanGantt_new
2025-08-01 21:30:38,153 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.ProjectPlanGantt_new to HiveMind
2025-08-01 21:30:38,153 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [32] - com.polarion.alm.builder
2025-08-01 21:30:38,156 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.builder to HiveMind
2025-08-01 21:30:38,157 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [33] - com.polarion.alm.checker
2025-08-01 21:30:38,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.checker to HiveMind
2025-08-01 21:30:38,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [34] - com.polarion.alm.extension.vcontext
2025-08-01 21:30:38,162 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.extension.vcontext to HiveMind
2025-08-01 21:30:38,162 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [35] - com.polarion.alm.impex
2025-08-01 21:30:38,171 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.impex to HiveMind
2025-08-01 21:30:38,171 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [36] - com.polarion.alm.install
2025-08-01 21:30:38,188 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [37] - com.polarion.alm.oslc
2025-08-01 21:30:38,202 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.oslc to HiveMind
2025-08-01 21:30:38,202 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [38] - com.polarion.alm.projects
2025-08-01 21:30:38,203 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.projects to HiveMind
2025-08-01 21:30:38,203 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [39] - com.polarion.alm.qcentre
2025-08-01 21:30:38,204 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.qcentre to HiveMind
2025-08-01 21:30:38,204 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [40] - com.polarion.alm.tracker
2025-08-01 21:30:38,213 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.tracker to HiveMind
2025-08-01 21:30:38,213 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [41] - com.polarion.alm.ui
2025-08-01 21:30:38,236 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.ui to HiveMind
2025-08-01 21:30:38,236 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [42] - com.polarion.alm.ui.diagrams.mxgraph
2025-08-01 21:30:38,240 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [43] - com.polarion.alm.wiki
2025-08-01 21:30:38,250 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.wiki to HiveMind
2025-08-01 21:30:38,250 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [44] - com.polarion.alm.ws
2025-08-01 21:30:38,251 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [45] - com.polarion.alm.ws.client
2025-08-01 21:30:38,255 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [46] - com.polarion.cluster
2025-08-01 21:30:38,264 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.cluster to HiveMind
2025-08-01 21:30:38,264 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [47] - com.polarion.core.boot
2025-08-01 21:30:38,264 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [48] - com.polarion.core.util
2025-08-01 21:30:38,265 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [49] - com.polarion.fop
2025-08-01 21:30:38,268 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [50] - com.polarion.platform
2025-08-01 21:30:38,278 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform to HiveMind
2025-08-01 21:30:38,278 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [51] - com.polarion.platform.guice
2025-08-01 21:30:38,279 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [52] - com.polarion.platform.hivemind
2025-08-01 21:30:38,280 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.hivemind to HiveMind
2025-08-01 21:30:38,281 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [53] - com.polarion.platform.jobs
2025-08-01 21:30:38,282 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.jobs to HiveMind
2025-08-01 21:30:38,282 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [54] - com.polarion.platform.monitoring
2025-08-01 21:30:38,290 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.monitoring to HiveMind
2025-08-01 21:30:38,290 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [55] - com.polarion.platform.persistence
2025-08-01 21:30:38,292 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.persistence to HiveMind
2025-08-01 21:30:38,293 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [56] - com.polarion.platform.repository
2025-08-01 21:30:38,293 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository to HiveMind
2025-08-01 21:30:38,293 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [57] - com.polarion.platform.repository.driver.svn
2025-08-01 21:30:38,295 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.driver.svn to HiveMind
2025-08-01 21:30:38,295 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [58] - com.polarion.platform.repository.external
2025-08-01 21:30:38,305 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external to HiveMind
2025-08-01 21:30:38,305 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [59] - com.polarion.platform.repository.external.git
2025-08-01 21:30:38,319 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.git to HiveMind
2025-08-01 21:30:38,319 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [60] - com.polarion.platform.repository.external.svn
2025-08-01 21:30:38,319 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.svn to HiveMind
2025-08-01 21:30:38,321 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [61] - com.polarion.platform.sql
2025-08-01 21:30:38,322 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [62] - com.polarion.portal.tomcat
2025-08-01 21:30:38,363 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [63] - com.polarion.psvn.launcher
2025-08-01 21:30:38,365 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.psvn.launcher to HiveMind
2025-08-01 21:30:38,365 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [64] - com.polarion.psvn.translations.en
2025-08-01 21:30:38,366 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [65] - com.polarion.purevariants
2025-08-01 21:30:38,371 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.purevariants to HiveMind
2025-08-01 21:30:38,371 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [66] - com.polarion.qcentre
2025-08-01 21:30:38,380 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [67] - com.polarion.scripting
2025-08-01 21:30:38,412 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.scripting to HiveMind
2025-08-01 21:30:38,412 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [68] - com.polarion.scripting.servlet
2025-08-01 21:30:38,412 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [69] - com.polarion.subterra.base
2025-08-01 21:30:38,412 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [70] - com.polarion.subterra.index
2025-08-01 21:30:38,421 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.index to HiveMind
2025-08-01 21:30:38,421 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [71] - com.polarion.subterra.persistence
2025-08-01 21:30:38,428 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence to HiveMind
2025-08-01 21:30:38,428 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [72] - com.polarion.subterra.persistence.document
2025-08-01 21:30:38,434 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence.document to HiveMind
2025-08-01 21:30:38,437 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [73] - com.polarion.synchronizer
2025-08-01 21:30:38,441 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.synchronizer to HiveMind
2025-08-01 21:30:38,441 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [74] - com.polarion.synchronizer.proxy.feishu
2025-08-01 21:30:38,447 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [75] - com.polarion.synchronizer.proxy.hpalm
2025-08-01 21:30:38,462 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [76] - com.polarion.synchronizer.proxy.jira
2025-08-01 21:30:38,468 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [77] - com.polarion.synchronizer.proxy.polarion
2025-08-01 21:30:38,480 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [78] - com.polarion.synchronizer.proxy.reqif
2025-08-01 21:30:38,530 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [79] - com.polarion.synchronizer.ui
2025-08-01 21:30:38,544 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [80] - com.polarion.usdp.persistence
2025-08-01 21:30:38,545 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.usdp.persistence to HiveMind
2025-08-01 21:30:38,545 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [81] - com.polarion.xray.doc.user
2025-08-01 21:30:38,545 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [82] - com.siemens.des.logger.api
2025-08-01 21:30:38,545 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [83] - com.siemens.plm.bitools.analytics
2025-08-01 21:30:38,545 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [84] - com.siemens.polarion.ct.collectors.git
2025-08-01 21:30:38,548 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.ct.collectors.git to HiveMind
2025-08-01 21:30:38,548 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [85] - com.siemens.polarion.eclipse.configurator
2025-08-01 21:30:38,552 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [86] - com.siemens.polarion.integration.ci
2025-08-01 21:30:38,554 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.integration.ci to HiveMind
2025-08-01 21:30:38,554 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [87] - com.siemens.polarion.previewer
2025-08-01 21:30:38,557 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer to HiveMind
2025-08-01 21:30:38,557 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [88] - com.siemens.polarion.previewer.external
2025-08-01 21:30:38,560 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer.external to HiveMind
2025-08-01 21:30:38,561 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [89] - com.siemens.polarion.rest
2025-08-01 21:30:38,561 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [90] - com.siemens.polarion.rt
2025-08-01 21:30:38,563 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [91] - com.siemens.polarion.rt.api
2025-08-01 21:30:38,566 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [92] - com.siemens.polarion.rt.collectors.git
2025-08-01 21:30:38,567 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [93] - com.siemens.polarion.rt.communication.common
2025-08-01 21:30:38,577 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [94] - com.siemens.polarion.rt.communication.polarion
2025-08-01 21:30:38,585 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.rt.communication.polarion to HiveMind
2025-08-01 21:30:38,587 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [95] - com.siemens.polarion.rt.communication.rt
2025-08-01 21:30:38,588 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [96] - com.siemens.polarion.rt.parsers.c
2025-08-01 21:30:38,591 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [97] - com.siemens.polarion.rt.ui
2025-08-01 21:30:38,592 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [98] - com.siemens.polarion.synchronizer.proxy.tfs
2025-08-01 21:30:38,601 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [99] - com.sun.activation.javax.activation
2025-08-01 21:30:38,613 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [100] - com.sun.istack.commons-runtime
2025-08-01 21:30:38,614 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [101] - com.sun.jna
2025-08-01 21:30:38,615 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [102] - com.sun.jna.platform
2025-08-01 21:30:38,621 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [103] - com.sun.xml.bind.jaxb-impl
2025-08-01 21:30:38,622 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [104] - com.teamlive.hozon.expcounter
2025-08-01 21:30:38,625 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.teamlive.hozon.expcounter to HiveMind
2025-08-01 21:30:38,626 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [105] - com.teamlive.livechecklist
2025-08-01 21:30:38,627 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.teamlive.livechecklist to HiveMind
2025-08-01 21:30:38,627 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [106] - com.trilead.ssh2
2025-08-01 21:30:38,636 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [107] - com.zaxxer.hikariCP
2025-08-01 21:30:38,639 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [108] - des-sdk-core
2025-08-01 21:30:38,640 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [109] - des-sdk-dss
2025-08-01 21:30:38,650 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [110] - io.github.resilience4j.circuitbreaker
2025-08-01 21:30:38,653 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [111] - io.github.resilience4j.core
2025-08-01 21:30:38,657 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [112] - io.github.resilience4j.retry
2025-08-01 21:30:38,658 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [113] - io.swagger
2025-08-01 21:30:38,663 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [114] - io.vavr
2025-08-01 21:30:38,663 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [115] - jakaroma
2025-08-01 21:30:38,664 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [116] - jakarta.validation.validation-api
2025-08-01 21:30:38,664 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [117] - javassist
2025-08-01 21:30:38,664 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [118] - javax.annotation-api
2025-08-01 21:30:38,665 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [119] - javax.cache
2025-08-01 21:30:38,665 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [120] - javax.el
2025-08-01 21:30:38,667 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [121] - javax.inject
2025-08-01 21:30:38,667 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [122] - javax.servlet
2025-08-01 21:30:38,667 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [123] - javax.servlet.jsp
2025-08-01 21:30:38,668 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [124] - javax.transaction
2025-08-01 21:30:38,668 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [125] - jaxb-api
2025-08-01 21:30:38,670 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [126] - jcip-annotations
2025-08-01 21:30:38,670 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [127] - jcl.over.slf4j
2025-08-01 21:30:38,671 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [128] - jul.to.slf4j
2025-08-01 21:30:38,671 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [129] - kuromoji-core
2025-08-01 21:30:38,671 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [130] - kuromoji-ipadic
2025-08-01 21:30:38,671 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [131] - lang-tag
2025-08-01 21:30:38,680 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [132] - net.htmlparser.jericho
2025-08-01 21:30:38,680 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [133] - net.java.dev.jna
2025-08-01 21:30:38,680 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [134] - net.minidev.accessors-smart
2025-08-01 21:30:38,681 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [135] - net.minidev.asm
2025-08-01 21:30:38,682 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [136] - net.minidev.json-smart
2025-08-01 21:30:38,682 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [137] - net.n3.nanoxml
2025-08-01 21:30:38,683 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [138] - net.sourceforge.cssparser
2025-08-01 21:30:38,683 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [139] - nu.xom
2025-08-01 21:30:38,683 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [140] - oauth2-oidc-sdk
2025-08-01 21:30:38,687 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [141] - org.apache.ant
2025-08-01 21:30:38,733 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [142] - org.apache.avro
2025-08-01 21:30:38,733 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [143] - org.apache.axis
2025-08-01 21:30:38,738 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [144] - org.apache.batik
2025-08-01 21:30:38,740 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [145] - org.apache.commons.codec
2025-08-01 21:30:38,742 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [146] - org.apache.commons.collections
2025-08-01 21:30:38,743 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [147] - org.apache.commons.commons-beanutils
2025-08-01 21:30:38,750 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [148] - org.apache.commons.commons-collections4
2025-08-01 21:30:38,752 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [149] - org.apache.commons.commons-compress
2025-08-01 21:30:38,755 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [150] - org.apache.commons.commons-fileupload
2025-08-01 21:30:38,761 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [151] - org.apache.commons.digester
2025-08-01 21:30:38,761 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [152] - org.apache.commons.exec
2025-08-01 21:30:38,761 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [153] - org.apache.commons.io
2025-08-01 21:30:38,762 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [154] - org.apache.commons.lang
2025-08-01 21:30:38,762 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [155] - org.apache.commons.lang3
2025-08-01 21:30:38,762 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [156] - org.apache.commons.logging
2025-08-01 21:30:38,762 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [157] - org.apache.curator
2025-08-01 21:30:38,779 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [158] - org.apache.fop
2025-08-01 21:30:38,780 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [159] - org.apache.hivemind
2025-08-01 21:30:38,781 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle org.apache.hivemind to HiveMind
2025-08-01 21:30:38,782 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [160] - org.apache.httpcomponents.httpclient
2025-08-01 21:30:38,782 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [161] - org.apache.httpcomponents.httpcore
2025-08-01 21:30:38,784 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [162] - org.apache.jasper.glassfish
2025-08-01 21:30:38,784 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [163] - org.apache.kafka.clients
2025-08-01 21:30:38,785 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [164] - org.apache.kafka.streams
2025-08-01 21:30:38,786 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [165] - org.apache.logging.log4j.1.2-api
2025-08-01 21:30:38,786 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [166] - org.apache.logging.log4j.api
2025-08-01 21:30:38,792 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [167] - org.apache.logging.log4j.apiconf
2025-08-01 21:30:38,792 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [168] - org.apache.logging.log4j.core
2025-08-01 21:30:38,792 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [169] - org.apache.logging.log4j.slf4j-impl
2025-08-01 21:30:38,793 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [170] - org.apache.lucene.analyzers-common
2025-08-01 21:30:38,795 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [171] - org.apache.lucene.analyzers-common
2025-08-01 21:30:38,798 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [172] - org.apache.lucene.analyzers-smartcn
2025-08-01 21:30:38,799 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [173] - org.apache.lucene.core
2025-08-01 21:30:38,803 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [174] - org.apache.lucene.core
2025-08-01 21:30:38,830 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [175] - org.apache.lucene.grouping
2025-08-01 21:30:38,830 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [176] - org.apache.lucene.queryparser
2025-08-01 21:30:38,830 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [177] - org.apache.oro
2025-08-01 21:30:38,834 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [178] - org.apache.pdfbox.fontbox
2025-08-01 21:30:38,835 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [179] - org.apache.poi
2025-08-01 21:30:38,847 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [180] - org.apache.tika
2025-08-01 21:30:40,088 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [181] - org.apache.xalan
2025-08-01 21:30:40,089 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [182] - org.apache.xercesImpl
2025-08-01 21:30:40,090 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [183] - org.apache.xml.serializer
2025-08-01 21:30:40,091 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [184] - org.apache.xmlgraphics.commons
2025-08-01 21:30:40,092 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [185] - org.apache.zookeeper
2025-08-01 21:30:40,095 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [186] - org.codehaus.groovy
2025-08-01 21:30:40,096 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [187] - org.codehaus.jettison
2025-08-01 21:30:40,097 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [188] - org.dom4j
2025-08-01 21:30:40,098 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [189] - org.eclipse.core.contenttype
2025-08-01 21:30:40,102 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [190] - org.eclipse.core.expressions
2025-08-01 21:30:40,103 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [191] - org.eclipse.core.filesystem
2025-08-01 21:30:40,104 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [192] - org.eclipse.core.jobs
2025-08-01 21:30:40,104 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [193] - org.eclipse.core.net
2025-08-01 21:30:40,104 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [194] - org.eclipse.core.resources
2025-08-01 21:30:40,104 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [195] - org.eclipse.core.runtime
2025-08-01 21:30:40,104 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [196] - org.eclipse.equinox.app
2025-08-01 21:30:40,105 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [197] - org.eclipse.equinox.common
2025-08-01 21:30:40,105 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [198] - org.eclipse.equinox.event
2025-08-01 21:30:40,107 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [199] - org.eclipse.equinox.http.registry
2025-08-01 21:30:40,108 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [200] - org.eclipse.equinox.http.servlet
2025-08-01 21:30:40,108 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [201] - org.eclipse.equinox.jsp.jasper
2025-08-01 21:30:40,108 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [202] - org.eclipse.equinox.jsp.jasper.registry
2025-08-01 21:30:40,112 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [203] - org.eclipse.equinox.launcher
2025-08-01 21:30:40,114 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [204] - org.eclipse.equinox.preferences
2025-08-01 21:30:40,115 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [205] - org.eclipse.equinox.registry
2025-08-01 21:30:40,115 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [206] - org.eclipse.equinox.security
2025-08-01 21:30:40,115 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [207] - org.eclipse.help
2025-08-01 21:30:40,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [208] - org.eclipse.help.base
2025-08-01 21:30:40,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [209] - org.eclipse.help.webapp
2025-08-01 21:30:40,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [210] - org.eclipse.jgit
2025-08-01 21:30:40,119 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [211] - org.eclipse.osgi.services
2025-08-01 21:30:40,120 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [212] - org.eclipse.osgi.util
2025-08-01 21:30:40,123 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [213] - org.ehcache
2025-08-01 21:30:40,140 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [214] - org.gitlab.java-gitlab-api
2025-08-01 21:30:40,143 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [215] - org.glassfish.jersey
2025-08-01 21:30:40,148 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [216] - org.hibernate.annotations
2025-08-01 21:30:40,151 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [217] - org.hibernate.core
2025-08-01 21:30:40,158 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [218] - org.hibernate.entitymanager
2025-08-01 21:30:40,161 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [219] - org.hibernate.hikaricp
2025-08-01 21:30:40,162 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [220] - org.hibernate.jpa.2.1.api
2025-08-01 21:30:40,163 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [221] - org.jboss.logging
2025-08-01 21:30:40,165 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [222] - org.jvnet.mimepull
2025-08-01 21:30:40,166 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [223] - org.objectweb.asm
2025-08-01 21:30:40,170 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [224] - org.objectweb.jotm
2025-08-01 21:30:40,170 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [225] - org.opensaml
2025-08-01 21:30:40,213 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [226] - org.polarion.svncommons
2025-08-01 21:30:40,220 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [227] - org.polarion.svnwebclient
2025-08-01 21:30:40,222 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [228] - org.postgesql
2025-08-01 21:30:40,224 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [229] - org.projectlombok.lombok
2025-08-01 21:30:40,261 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [230] - org.rocksdb.rocksdbjni
2025-08-01 21:30:40,262 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [231] - org.springframework.data.core
2025-08-01 21:30:40,262 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [232] - org.springframework.data.jpa
2025-08-01 21:30:40,262 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [233] - org.springframework.spring-aop
2025-08-01 21:30:40,263 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [234] - org.springframework.spring-beans
2025-08-01 21:30:40,263 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [235] - org.springframework.spring-context
2025-08-01 21:30:40,264 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [236] - org.springframework.spring-core
2025-08-01 21:30:40,265 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [237] - org.springframework.spring-expression
2025-08-01 21:30:40,269 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [238] - org.springframework.spring-jdbc
2025-08-01 21:30:40,274 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [239] - org.springframework.spring-orm
2025-08-01 21:30:40,274 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [240] - org.springframework.spring-test
2025-08-01 21:30:40,275 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [241] - org.springframework.spring-tx
2025-08-01 21:30:40,276 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [242] - org.springframework.spring-web
2025-08-01 21:30:40,278 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [243] - org.springframework.spring-webmvc
2025-08-01 21:30:40,279 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [244] - org.tmatesoft.sqljet
2025-08-01 21:30:40,280 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [245] - org.tmatesoft.svnkit
2025-08-01 21:30:40,281 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [246] - saaj-api
2025-08-01 21:30:40,283 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [247] - sdk-lifecycle-collab
2025-08-01 21:30:40,286 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [248] - sdk-lifecycle-docmgmt
2025-08-01 21:30:40,287 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [249] - siemens.des.clientsecurity
2025-08-01 21:30:40,289 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [250] - slf4j.api
2025-08-01 21:30:40,290 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [251] - xml-apis
2025-08-01 21:30:40,291 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [252] - xml.apis.ext
2025-08-01 21:30:40,292 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [253] - xstream
2025-08-01 21:30:41,531 [main] INFO  com.polarion.core.util.remote.server.SocketRemoteControlServer - Remote control server socket is ready to listen on localhost/127.0.0.1:8887
2025-08-01 21:30:41,531 [xServer:8887] INFO  org.xsocket.connection.Server - server listening on localhost:8887 (xSocket 2.5.3)
2025-08-01 21:30:42,901 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database...
2025-08-01 21:30:43,180 [main] INFO  com.polarion.platform.sql.internal.PgServerInfo - PG server listening on localhost:5435
2025-08-01 21:30:45,525 [main] INFO  com.polarion.platform.internal.cache.CacheConfigurator - EHCache uses internal configuration
2025-08-01 21:30:47,209 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 48.567529296875
2025-08-01 21:30:47,393 [main] WARN  org.ehcache.impl.internal.executor.PooledExecutionService - No default pool configured, services requiring thread pools must be configured explicitly using named thread pools
2025-08-01 21:30:47,818 [main] INFO  org.ehcache.sizeof.filters.AnnotationSizeOfFilter - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-01 21:30:47,844 [main] INFO  org.ehcache.sizeof.impl.JvmInformation - Detected JVM data model settings of: 64-Bit OpenJDK JVM with Compressed OOPs
2025-08-01 21:30:47,923 [main] INFO  org.ehcache.sizeof.impl.AgentLoader - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-01 21:30:48,406 [main] INFO  com.polarion.platform.internal.cache.CachingProviderHandler - All the caches have been destroyed because of not clean shutdown. You can ignore this message if Polarion started in reindex mode.
2025-08-01 21:30:48,689 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-08-01 21:30:48,703 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-08-01 21:30:48,715 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion_history' is: *************************************************
2025-08-01 21:30:49,075 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database finished [ TIME 6.16 s. ]
2025-08-01 21:30:50,230 [main] INFO  com.polarion.platform.cluster.ClusterService - Initializing cluster service
2025-08-01 21:30:50,234 [main] INFO  com.polarion.platform.cluster.ClusterService - Cluster service is disabled.
2025-08-01 21:30:51,428 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-01 21:30:51,525 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Starting...
2025-08-01 21:30:51,720 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Start completed.
2025-08-01 21:30:51,970 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.542 s. ]
2025-08-01 21:30:51,970 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-01 21:30:52,003 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Starting...
2025-08-01 21:30:52,032 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Start completed.
2025-08-01 21:30:52,217 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.247 s. ]
2025-08-01 21:30:52,218 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot started
2025-08-01 21:30:52,447 [main] INFO  com.polarion.platform.repository.driver.svn.internal.security.SVNWatcher - SVN auth file watcher started with a period of 3000 milliseconds
2025-08-01 21:30:52,486 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-01 21:30:52,633 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion authenticated from system
2025-08-01 21:30:52,826 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion logged in from system
2025-08-01 21:30:52,844 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Created
2025-08-01 21:30:52,847 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Initialized
2025-08-01 21:30:52,861 [main | u:p] INFO  com.polarion.core.util.profiling.SimpleProfiler - Initialization
2025-08-01 21:30:52,900 [main | u:p] INFO  org.objectweb.jotm - JOTM started with a local transaction factory which is not bound.
2025-08-01 21:30:52,900 [main | u:p] INFO  org.objectweb.jotm - CAROL initialization
2025-08-01 21:30:52,948 [main | u:p] INFO  com.polarion.platform.internal.service.repository.listeners.job.PullingJob - lastFullyProcessedRevision [275]
2025-08-01 21:30:52,968 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - END initializeService
2025-08-01 21:30:52,983 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Polarion startup estimation:  [ TIME 15 s. ]
2025-08-01 21:30:52,983 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 15 s. ]
2025-08-01 21:30:53,020 [main | u:p] INFO  com.polarion.platform.monitoring - Full monitoring results are stored in file /opt/polarion/data/workspace/monitoring/results.txt
2025-08-01 21:30:53,237 [main | u:p] INFO  com.polarion.platform.monitoring - Executing actions from stage PREBOOT
2025-08-01 21:30:53,246 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-01 21:30:53,252 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,428.85 GB
 [Fri Aug 01 21:30:53 CST 2025]
2025-08-01 21:30:53,768 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.jvm.version'
2025-08-01 21:30:53,768 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.jvm.version (JVM Version) = JVM Version 
Microsoft OpenJDK 64-Bit Server VM 11.0.27+6-LTS
 [Fri Aug 01 21:30:53 CST 2025]
2025-08-01 21:30:53,777 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.os.version'
2025-08-01 21:30:53,778 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.os.version (OS Version) = OS Version 
Mac OS X 15.5 aarch64
 [Fri Aug 01 21:30:53 CST 2025]
2025-08-01 21:30:53,789 [main | u:p] INFO  com.polarion.platform.monitoring - Finished with actions from stage PREBOOT: {OK=3}
2025-08-01 21:30:53,790 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 16.6 s. ]
2025-08-01 21:30:53,793 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.281 s [77% update (144x), 23% query (12x)] (221x), svn: 0.0445 s [62% getLatestRevision (2x), 30% testConnection (1x)] (4x)
2025-08-01 21:30:53,798 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - calling ILowLevelPersistence.boot to start persistence
2025-08-01 21:30:53,848 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization started
2025-08-01 21:30:54,053 [main | u:p] INFO  com.polarion.subterra.base.internal.location.LocationCacheContext - Registered invalidationListener: com.polarion.platform.repository.internal.config.RepositoryConfigService$1@58bc1e1f
2025-08-01 21:30:54,222 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: BaselineCollection
2025-08-01 21:30:54,222 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: TestRun
2025-08-01 21:30:54,222 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: Plan
2025-08-01 21:30:54,294 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition started
2025-08-01 21:30:54,294 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 21:30:54,295 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-01 21:30:54,355 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition finished [ TIME 0.0611 s. ]
2025-08-01 21:30:54,356 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context tree: 
ROOT_CTX_NAME (ContextNature[Root], ContextId[context [global]])
+-default (ContextNature[Repository], ContextId[cluster default, context [global]])
  +-WBS (ContextNature[Project], ContextId[cluster default, context WBS])
  +-WBSdev (ContextNature[Project], ContextId[cluster default, context WBSdev])
  +-Demo Projects (ContextNature[ProjectGroup], ContextId[cluster default, context --Demo Projects])
  | +-elibrary (ContextNature[Project], ContextId[cluster default, context elibrary])
  | +-drivepilot (ContextNature[Project], ContextId[cluster default, context drivepilot])
  +-library (ContextNature[Project], ContextId[cluster default, context library])
  +-hesai (ContextNature[Project], ContextId[cluster default, context hesai])
2025-08-01 21:30:54,356 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.57 s. ]
2025-08-01 21:30:54,356 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.092 s [46% getDir2 content (2x), 39% info (3x)] (6x)
2025-08-01 21:30:54,357 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 21:30:54,357 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-01 21:30:54,357 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Startup workers for phase 3: 6
2025-08-01 21:30:54,469 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-08-01 21:30:54,471 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context --Demo Projects] (1/9) ...
2025-08-01 21:30:54,471 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-08-01 21:30:54,472 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context library] (4/9) ...
2025-08-01 21:30:54,470 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-08-01 21:30:54,472 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context [global]] (2/9) ...
2025-08-01 21:30:54,473 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-08-01 21:30:54,474 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[context [global]] (5/9) ...
2025-08-01 21:30:54,470 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-08-01 21:30:54,474 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBS] (3/9) ...
2025-08-01 21:30:54,485 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-01 21:30:54,485 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBSdev] (6/9) ...
2025-08-01 21:30:54,608 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[context [global]] (5/9) TOOK  [ TIME 0.119 s. ]
2025-08-01 21:30:54,610 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-01 21:30:54,610 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context hesai] (7/9) ...
2025-08-01 21:30:55,192 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/.polarion'
2025-08-01 21:30:55,217 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/modules'
2025-08-01 21:30:55,238 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/documents'
2025-08-01 21:30:55,264 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/_wiki'
2025-08-01 21:30:55,270 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context --Demo Projects contains 0 primary objects (work items+comments).
2025-08-01 21:30:55,271 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context --Demo Projects] (1/9) TOOK  [ TIME 0.799 s. ]
2025-08-01 21:30:55,271 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-01 21:30:55,271 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context elibrary] (8/9) ...
2025-08-01 21:30:55,362 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/library/documents'
2025-08-01 21:30:55,401 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBS/documents'
2025-08-01 21:30:55,680 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context library contains 288 primary objects (work items+comments).
2025-08-01 21:30:55,680 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context library] (4/9) TOOK  [ TIME 1.21 s. ]
2025-08-01 21:30:55,685 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-01 21:30:55,685 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context drivepilot] (9/9) ...
2025-08-01 21:30:55,869 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBS contains 344 primary objects (work items+comments).
2025-08-01 21:30:55,871 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBS] (3/9) TOOK  [ TIME 1.4 s. ]
2025-08-01 21:30:55,908 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/elibrary/documents'
2025-08-01 21:30:56,034 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context elibrary contains 334 primary objects (work items+comments).
2025-08-01 21:30:56,034 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context elibrary] (8/9) TOOK  [ TIME 0.763 s. ]
2025-08-01 21:30:56,050 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/hesai/documents'
2025-08-01 21:30:56,086 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/drivepilot/documents'
2025-08-01 21:30:56,184 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context drivepilot contains 461 primary objects (work items+comments).
2025-08-01 21:30:56,185 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context drivepilot] (9/9) TOOK  [ TIME 0.499 s. ]
2025-08-01 21:30:56,307 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context hesai contains 1148 primary objects (work items+comments).
2025-08-01 21:30:56,307 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context hesai] (7/9) TOOK  [ TIME 1.7 s. ]
2025-08-01 21:30:56,557 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context null contains 2214 primary objects (work items+comments).
2025-08-01 21:30:56,558 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context [global]] (2/9) TOOK  [ TIME 2.09 s. ]
2025-08-01 21:30:56,654 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBSdev/documents'
2025-08-01 21:30:57,041 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBSdev contains 3322 primary objects (work items+comments).
2025-08-01 21:30:57,042 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBSdev] (6/9) TOOK  [ TIME 2.56 s. ]
2025-08-01 21:30:57,067 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 2.63 s, CPU [user: 0.186 s, system: 0.139 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.257 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.144 s [70% log2 (5x), 22% getLatestRevision (1x)] (7x), GC: 0.137 s [100% G1 Young Generation (1x)] (1x)
2025-08-01 21:30:57,069 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 2.65 s, CPU [user: 0.0834 s, system: 0.046 s], Allocated memory: 7.4 MB, transactions: 0, svn: 0.339 s [85% log2 (5x)] (7x), ObjectMaps: 0.148 s [99% getAllPrimaryObjects (1x)] (10x), GC: 0.137 s [100% G1 Young Generation (1x)] (1x)
2025-08-01 21:30:57,069 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 2.63 s, CPU [user: 0.367 s, system: 0.23 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.35 s [100% getAllPrimaryObjects (1x)] (10x), GC: 0.137 s [100% G1 Young Generation (1x)] (1x)
2025-08-01 21:30:57,076 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 2.65 s, CPU [user: 0.462 s, system: 0.332 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.387 s [100% getAllPrimaryObjects (1x)] (7x), GC: 0.137 s [100% G1 Young Generation (1x)] (1x)
2025-08-01 21:30:57,078 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 2.66 s, CPU [user: 0.143 s, system: 0.0532 s], Allocated memory: 11.1 MB, transactions: 0, svn: 0.559 s [52% log2 (10x), 13% getLatestRevision (3x), 13% info (5x), 12% log (1x)] (24x), GC: 0.137 s [100% G1 Young Generation (1x)] (1x)
2025-08-01 21:30:57,090 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 2.67 s, CPU [user: 0.129 s, system: 0.0838 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.361 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.23 s [82% log2 (10x)] (13x), GC: 0.137 s [100% G1 Young Generation (1x)] (1x)
2025-08-01 21:30:57,090 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 2.73 s. ]
2025-08-01 21:30:57,090 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 1.62 s [100% getAllPrimaryObjects (8x)] (62x), svn: 1.37 s [68% log2 (36x), 13% getLatestRevision (9x)] (61x)
2025-08-01 21:30:57,202 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 48.772705078125
2025-08-01 21:30:57,233 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [START].
2025-08-01 21:30:57,233 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 21:30:57,235 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-01 21:30:57,807 [main | u:p] INFO  TXLOGGER - Tx 66197507f8401_0_66197507f8401_0_: finished. Total: 0.422 s, CPU [user: 0.174 s, system: 0.0268 s], Allocated memory: 21.8 MB
2025-08-01 21:30:57,849 [main | u:p | u:p] ERROR com.polarion.platform.repository.internal.config.RepositoryConfigService$ConfigProblemCatcher - Failed to work with configuration from location /WBS/.polarion/repositories/repositories.xml:
[/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
com.polarion.platform.repository.config.RepositoryConfigurationException: [/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:87) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocations(AbstractDataHandler.java:61) ~[platform-repository.jar:?]
	at $IDataHandler_19865d3e0a3.readLocations($IDataHandler_19865d3e0a3.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readLocations(RepositoryConfigService.java:291) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$3.runImpl(RepositoryConfigService.java:328) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction$1.runWEx(RepositoryConfigService.java:113) ~[platform-repository.jar:?]
	at com.polarion.core.util.RunnableWEx.runWRet(RunnableWEx.java:61) ~[util.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction.run(RepositoryConfigService.java:123) ~[platform-repository.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:361) ~[?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:58) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:422) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:412) ~[platform.jar:?]
	at $ISecurityService_19865d3debe.doAsSystemUser($ISecurityService_19865d3debe.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readConfiguration(RepositoryConfigService.java:324) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getConfigurationImpl(RepositoryConfigService.java:239) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfigurationImpl(RepositoryConfigService.java:199) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:177) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.addConfigurationListener(RepositoryConfigService.java:263) ~[platform-repository.jar:?]
	at $IRepositoryConfigService_19865d3decc.addConfigurationListener($IRepositoryConfigService_19865d3decc.java) ~[?:?]
	at com.polarion.platform.repository.external.internal.ExternalRepositoryProviderRegistry.initialize(ExternalRepositoryProviderRegistry.java:146) ~[platform-repository.jar:?]
	at $IExternalRepositoryProviderRegistry_19865d3df88.initialize($IExternalRepositoryProviderRegistry_19865d3df88.java) ~[?:?]
	at $IExternalRepositoryProviderRegistry_19865d3df87.initialize($IExternalRepositoryProviderRegistry_19865d3df87.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule.initModule(RevisionsPersistenceModule.java:353) ~[platform-persistence.jar:?]
	at $IObjectPersistenceModule_19865d3e077.initModule($IObjectPersistenceModule_19865d3e077.java) ~[?:?]
	at com.polarion.subterra.persistence.internal.PersistenceEngine.initModule(PersistenceEngine.java:251) ~[subterra-uniform-persistence.jar:?]
	at $IPersistenceEngine_19865d3e05f.initModule($IPersistenceEngine_19865d3e05f.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.LowLevelDataService.boot(LowLevelDataService.java:352) ~[platform-persistence.jar:?]
	at $ILowLevelPersistence_19865d3df84.boot($ILowLevelPersistence_19865d3df84.java) ~[?:?]
	at $ILowLevelPersistence_19865d3df83.boot($ILowLevelPersistence_19865d3df83.java) ~[?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.lambda$0(PlatformService.java:294) ~[launcher.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:423) [?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:69) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:417) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:407) [platform.jar:?]
	at $ISecurityService_19865d3debf.doAsSystemUser($ISecurityService_19865d3debf.java) [?:?]
	at $ISecurityService_19865d3debe.doAsSystemUser($ISecurityService_19865d3debe.java) [?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.bootPlatform(PlatformService.java:286) [launcher.jar:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.start(PlatformService.java:92) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.runImpl(PolarionSVNApplication.java:139) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.run(PolarionSVNApplication.java:94) [launcher.jar:?]
	at com.polarion.core.boot.launchers.BasicAppLauncher.launch(BasicAppLauncher.java:53) [boot.jar:?]
	at com.polarion.core.boot.impl.AppLaunchersManager.start(AppLaunchersManager.java:184) [boot.jar:?]
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196) [org.eclipse.equinox.app_1.3.500.v20171221-2204.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:388) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:243) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:656) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:592) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.run(Main.java:1498) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.main(Main.java:1471) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
Caused by: com.thoughtworks.xstream.converters.ConversionException: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:77) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
Caused by: com.thoughtworks.xstream.mapper.CannotResolveClassException: AutoBranchGitLab
	at com.thoughtworks.xstream.mapper.DefaultMapper.realClass(DefaultMapper.java:81) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.DynamicProxyMapper.realClass(DynamicProxyMapper.java:55) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.PackageAliasingMapper.realClass(PackageAliasingMapper.java:88) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ClassAliasingMapper.realClass(ClassAliasingMapper.java:79) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ArrayMapper.realClass(ArrayMapper.java:74) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.SecurityMapper.realClass(SecurityMapper.java:71) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.CachingMapper.realClass(CachingMapper.java:47) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.util.HierarchicalStreams.readClassType(HierarchicalStreams.java:29) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readBareItem(AbstractCollectionConverter.java:131) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readItem(AbstractCollectionConverter.java:117) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readCompleteItem(AbstractCollectionConverter.java:147) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.ArrayConverter.unmarshal(ArrayConverter.java:54) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:72) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
2025-08-01 21:30:58,139 [main | u:p] INFO  TXLOGGER - Tx 6619750865402_0_6619750865402_0_: finished. Total: 0.325 s, CPU [user: 0.095 s, system: 0.0196 s], Allocated memory: 26.9 MB, svn: 0.028 s [83% info (2x)] (4x)
2025-08-01 21:30:58,409 [main | u:p] INFO  TXLOGGER - Tx 66197508b7003_0_66197508b7003_0_: finished. Total: 0.264 s, CPU [user: 0.00346 s, system: 0.00193 s], Allocated memory: 184.9 kB, svn: 0.262 s [100% info (1x)] (2x)
2025-08-01 21:30:58,665 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-01 21:30:58,718 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions from repository default in context ContextId[context [global]] finished
2025-08-01 21:30:58,720 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [FINISHED].
2025-08-01 21:30:58,720 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 1.63 s. ]
2025-08-01 21:30:58,720 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 1.28 s [100% getReadConfiguration (48x)] (48x), svn: 0.496 s [93% info (18x)] (38x)
2025-08-01 21:30:58,942 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 21:30:58,943 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-01 21:30:58,943 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting repository for build artifacts-related changes
2025-08-01 21:30:58,943 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[context [global]]
2025-08-01 21:30:58,943 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[context [global]]
2025-08-01 21:30:58,951 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[context [global]] has been successfully processed
2025-08-01 21:30:58,973 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[context [global]] finished [ TIME 0.0299 s. ]
2025-08-01 21:30:58,973 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-01 21:30:58,973 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context [global]]
2025-08-01 21:30:58,973 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context [global]]
2025-08-01 21:30:59,023 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Reconfiguring build artifacts from context ContextId[cluster default, context [global]]
2025-08-01 21:30:59,117 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context [global]] has been successfully processed
2025-08-01 21:30:59,153 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context [global]] finished [ TIME 0.18 s. ]
2025-08-01 21:30:59,153 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-01 21:30:59,153 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBS]
2025-08-01 21:30:59,153 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBS]
2025-08-01 21:30:59,218 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Reconfiguring build artifacts from context ContextId[cluster default, context WBS]
2025-08-01 21:30:59,297 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBS] has been successfully processed
2025-08-01 21:30:59,298 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBS] finished [ TIME 0.145 s. ]
2025-08-01 21:30:59,298 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-01 21:30:59,299 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBSdev]
2025-08-01 21:30:59,299 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBSdev]
2025-08-01 21:30:59,353 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Reconfiguring build artifacts from context ContextId[cluster default, context WBSdev]
2025-08-01 21:30:59,418 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBSdev] has been successfully processed
2025-08-01 21:30:59,418 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBSdev] finished [ TIME 0.119 s. ]
2025-08-01 21:30:59,418 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-01 21:30:59,418 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context --Demo Projects]
2025-08-01 21:30:59,418 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context --Demo Projects]
2025-08-01 21:30:59,446 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Reconfiguring build artifacts from context ContextId[cluster default, context --Demo Projects]
2025-08-01 21:30:59,471 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context --Demo Projects] has been successfully processed
2025-08-01 21:30:59,538 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context --Demo Projects] finished [ TIME 0.119 s. ]
2025-08-01 21:30:59,538 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-01 21:30:59,538 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context library]
2025-08-01 21:30:59,538 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context library]
2025-08-01 21:30:59,573 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Reconfiguring build artifacts from context ContextId[cluster default, context library]
2025-08-01 21:30:59,628 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context library] has been successfully processed
2025-08-01 21:30:59,631 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context library] finished [ TIME 0.0928 s. ]
2025-08-01 21:30:59,631 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-01 21:30:59,631 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context hesai]
2025-08-01 21:30:59,631 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context hesai]
2025-08-01 21:30:59,695 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Reconfiguring build artifacts from context ContextId[cluster default, context hesai]
2025-08-01 21:30:59,770 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context hesai] has been successfully processed
2025-08-01 21:30:59,770 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context hesai] finished [ TIME 0.139 s. ]
2025-08-01 21:30:59,770 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-01 21:30:59,770 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context elibrary]
2025-08-01 21:30:59,770 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context elibrary]
2025-08-01 21:30:59,826 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Reconfiguring build artifacts from context ContextId[cluster default, context elibrary]
2025-08-01 21:30:59,921 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context elibrary] has been successfully processed
2025-08-01 21:30:59,921 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context elibrary] finished [ TIME 0.151 s. ]
2025-08-01 21:30:59,921 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-01 21:30:59,921 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context drivepilot]
2025-08-01 21:30:59,921 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context drivepilot]
2025-08-01 21:30:59,980 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Reconfiguring build artifacts from context ContextId[cluster default, context drivepilot]
2025-08-01 21:31:00,062 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context drivepilot] has been successfully processed
2025-08-01 21:31:00,064 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context drivepilot] finished [ TIME 0.141 s. ]
2025-08-01 21:31:00,064 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-01 21:31:00,064 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... repository inspection finished [ TIME 1.12 s. ]
2025-08-01 21:31:00,065 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 1.34 s. ]
2025-08-01 21:31:00,065 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.806 s [76% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.728 s [100% getReadConfiguration (94x)] (94x), PersistenceEngineListener: 0.0581 s [100% objectsModified (8x)] (16x)
2025-08-01 21:31:00,065 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 21:31:00,065 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-01 21:31:00,065 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting BIR for new or removed builds
2025-08-01 21:31:00,127 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were removed (including calculations from previous run)
2025-08-01 21:31:00,127 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were added or modified
2025-08-01 21:31:00,128 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... BIR inspection finished [ TIME 0.0622 s. ]
2025-08-01 21:31:00,128 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.06 s. ]
2025-08-01 21:31:00,197 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 21:31:00,198 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-01 21:31:00,198 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing startup index events, starting iterations.
2025-08-01 21:31:00,198 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Iteration 1 - processing 21 events
2025-08-01 21:31:00,243 [main | u:p] INFO  com.polarion.alm.tracker.internal.planning.PlanFieldsProvider - livePlanXMLLocation: Location[path /default/.reports/xml/live-plan.xml]
2025-08-01 21:31:00,446 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener -  - reindexing 12 existing objects and 0 deleted objects.
2025-08-01 21:31:01,040 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 11 objects in index: BuildArtifact
2025-08-01 21:31:01,041 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Attachment
2025-08-01 21:31:01,041 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPage
2025-08-01 21:31:01,041 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-08-01 21:31:01,171 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 11 from 11 objects were refreshed (100%)
2025-08-01 21:31:01,175 [main | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BuildArtifact
2025-08-01 21:31:01,370 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-08-01 21:31:01,371 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Attachment
2025-08-01 21:31:01,371 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-08-01 21:31:01,371 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPage
2025-08-01 21:31:01,373 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPageAttachment
2025-08-01 21:31:01,421 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [Attachment]: finished. Total: 0.44 s, CPU [user: 0.0354 s, system: 0.00754 s], Allocated memory: 2.2 MB
2025-08-01 21:31:01,421 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem
2025-08-01 21:31:01,422 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.443 s, CPU [user: 0.0403 s, system: 0.00932 s], Allocated memory: 2.7 MB
2025-08-01 21:31:01,422 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: UserGroup
2025-08-01 21:31:01,423 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [User]: finished. Total: 0.446 s, CPU [user: 0.0389 s, system: 0.00808 s], Allocated memory: 1.9 MB
2025-08-01 21:31:01,423 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BaselineCollection
2025-08-01 21:31:01,443 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-08-01 21:31:01,451 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: UserGroup
2025-08-01 21:31:01,453 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRun
2025-08-01 21:31:01,461 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.469 s, CPU [user: 0.00791 s, system: 0.00283 s], Allocated memory: 870.9 kB
2025-08-01 21:31:01,565 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPageAttachment
2025-08-01 21:31:01,565 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BaselineCollection
2025-08-01 21:31:01,566 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRun
2025-08-01 21:31:01,567 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [BaselineCollection]: finished. Total: 0.143 s, CPU [user: 0.00668 s, system: 0.0029 s], Allocated memory: 1.4 MB
2025-08-01 21:31:01,567 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Build
2025-08-01 21:31:01,567 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleComment
2025-08-01 21:31:01,569 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Build
2025-08-01 21:31:01,570 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [TestRun]: finished. Total: 0.116 s, CPU [user: 0.00614 s, system: 0.00337 s], Allocated memory: 1.7 MB
2025-08-01 21:31:01,570 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [Build]: finished. Total: 0.107 s, CPU [user: 0.00137 s, system: 0.000692 s], Allocated memory: 78.0 kB
2025-08-01 21:31:01,580 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Comment
2025-08-01 21:31:01,596 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleComment
2025-08-01 21:31:01,603 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BuildArtifact
2025-08-01 21:31:01,608 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: DocumentWorkflowSignature
2025-08-01 21:31:01,641 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BuildArtifact
2025-08-01 21:31:01,642 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPageAttachment
2025-08-01 21:31:01,644 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Comment
2025-08-01 21:31:01,677 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPageAttachment
2025-08-01 21:31:01,677 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-08-01 21:31:01,683 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-08-01 21:31:01,684 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Baseline
2025-08-01 21:31:01,686 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.68 s, CPU [user: 0.0263 s, system: 0.00902 s], Allocated memory: 8.5 MB
2025-08-01 21:31:01,686 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: DocumentWorkflowSignature
2025-08-01 21:31:01,695 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem
2025-08-01 21:31:01,723 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [WorkItem]: finished. Total: 0.302 s, CPU [user: 0.0155 s, system: 0.00629 s], Allocated memory: 7.3 MB
2025-08-01 21:31:01,724 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkRecord
2025-08-01 21:31:01,728 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Baseline
2025-08-01 21:31:01,728 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem-OutlineNumbers
2025-08-01 21:31:01,731 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunAttachment
2025-08-01 21:31:01,745 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem-OutlineNumbers
2025-08-01 21:31:01,747 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-08-01 21:31:01,749 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [Comment]: finished. Total: 0.168 s, CPU [user: 0.0028 s, system: 0.00108 s], Allocated memory: 416.6 kB
2025-08-01 21:31:01,757 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunAttachment
2025-08-01 21:31:01,758 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleAttachment
2025-08-01 21:31:01,759 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunComment
2025-08-01 21:31:01,760 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Plan
2025-08-01 21:31:01,765 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunComment
2025-08-01 21:31:01,766 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-08-01 21:31:01,778 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-08-01 21:31:01,778 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPage
2025-08-01 21:31:01,778 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkRecord
2025-08-01 21:31:01,780 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Module
2025-08-01 21:31:01,787 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Plan
2025-08-01 21:31:01,790 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [Plan]: finished. Total: 0.104 s, CPU [user: 0.00392 s, system: 0.00178 s], Allocated memory: 1.4 MB
2025-08-01 21:31:01,791 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-08-01 21:31:01,805 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-08-01 21:31:01,845 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPage
2025-08-01 21:31:01,903 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-01 21:31:01,905 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-01 21:31:01,905 [main | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-08-01 21:31:02,009 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleAttachment
2025-08-01 21:31:02,021 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Revision
2025-08-01 21:31:02,021 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}273
2025-08-01 21:31:02,021 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}272
2025-08-01 21:31:02,021 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}275
2025-08-01 21:31:02,021 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}274
2025-08-01 21:31:02,021 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}270
2025-08-01 21:31:02,021 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}271
2025-08-01 21:31:02,021 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}224
2025-08-01 21:31:02,021 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}225
2025-08-01 21:31:02,021 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}226
2025-08-01 21:31:02,021 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}228
2025-08-01 21:31:02,021 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}230
2025-08-01 21:31:02,021 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}231
2025-08-01 21:31:02,021 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}232
2025-08-01 21:31:02,021 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}233
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}234
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}235
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}239
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}240
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}241
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}242
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}243
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}245
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}246
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}248
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}249
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}250
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}251
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}253
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}254
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}255
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}256
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}257
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}258
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}259
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}260
2025-08-01 21:31:02,028 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}261
2025-08-01 21:31:02,029 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}262
2025-08-01 21:31:02,029 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}263
2025-08-01 21:31:02,029 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}264
2025-08-01 21:31:02,029 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}265
2025-08-01 21:31:02,029 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}267
2025-08-01 21:31:02,029 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}268
2025-08-01 21:31:02,029 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}269
2025-08-01 21:31:02,031 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Revision]: finished. Total: 0.263 s, CPU [user: 0.0123 s, system: 0.00758 s], Allocated memory: 2.4 MB
2025-08-01 21:31:02,038 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [ModuleAttachment]: finished. Total: 0.287 s, CPU [user: 0.0128 s, system: 0.00849 s], Allocated memory: 3.7 MB
2025-08-01 21:31:02,088 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Module
2025-08-01 21:31:02,093 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [Module]: finished. Total: 0.313 s, CPU [user: 0.0188 s, system: 0.0107 s], Allocated memory: 10.8 MB
2025-08-01 21:31:02,093 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [ProjectGroup]: finished. Total: 0.301 s, CPU [user: 0.00236 s, system: 0.00161 s], Allocated memory: 365.1 kB
2025-08-01 21:31:02,093 [PolarionDocIdCreator-1] INFO  com.polarion.subterra.index.impl.lucene.baseline.PolarionDocIdCreator - Bloom filter loading for 28 indices took  [ TIME 1.12 s. ]
2025-08-01 21:31:02,290 [main | u:p] INFO  TXLOGGER - Tx Lucene Commit [head]: finished. Total: 0.233 s, CPU [user: 0.0598 s, system: 0.0184 s], Allocated memory: 17.6 MB, commit: 0.231 s [66% Revision (1x), 34% BuildArtifact (1x)] (2x)
2025-08-01 21:31:02,309 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.DelegatingCalculatedFieldsListener - Calculated fields mode: async
2025-08-01 21:31:02,325 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing took  [ TIME 2.13 s. ]
2025-08-01 21:31:02,325 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 2.2 s. ]
2025-08-01 21:31:02,326 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 2.13 s [100% doFinishStartup (1x)] (1x), Lucene: 0.39 s [100% refresh (2x)] (2x), DB: 0.321 s [55% update (45x), 22% query (20x), 12% commit (22x)] (122x), SubterraURITable: 0.235 s [100% addIfNotExistsDB (20x)] (20x), commit: 0.231 s [66% Revision (1x), 34% BuildArtifact (1x)] (2x), calculatedFieldsContributor: 0.139 s [100% objectsToInv (9x)] (9x), resolve: 0.13 s [88% BuildArtifact (11x)] (13x), GlobalHandler: 0.107 s [95% put (13x)] (26x)
2025-08-01 21:31:02,329 [main | u:p] INFO  com.polarion.platform.internal.service.repository.ListenerManager - Starting the pulling job for repository: default
2025-08-01 21:31:02,329 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization finished [ TIME 8.48 s. ]
2025-08-01 21:31:02,329 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 21:31:02,329 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-01 21:31:02,333 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.CalculatedFieldsStorage - Checking integrity of calculated fields storage /opt/polarion/data/workspace/polarion-data/calculated-fields
2025-08-01 21:31:02,375 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.05 s. ]
2025-08-01 21:31:02,375 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 21:31:02,375 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-01 21:31:02,505 [main | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - Updating local scheduler state: start
2025-08-01 21:31:02,538 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-08-01 21:31:02,621 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-01 21:31:02,624 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-08-01 21:31:02,624 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-08-01 21:31:02,625 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutting down.
2025-08-01 21:31:02,625 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED paused.
2025-08-01 21:31:02,627 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-08-01 21:31:02,639 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-08-01 21:31:02,656 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-01 21:31:02,656 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-08-01 21:31:02,656 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-08-01 21:31:02,656 [main | u:p | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - 15 scheduled job(s) configured
2025-08-01 21:31:02,688 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
2025-08-01 21:31:03,263 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot finished
2025-08-01 21:31:03,263 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform started
2025-08-01 21:31:03,327 [main] INFO  com.polarion.portal.tomcat.TomcatPlugin - Tomcat home directory was set to /opt/polarion/data/workspace/.metadata/.plugins/com.polarion.portal.tomcat
2025-08-01 21:31:03,354 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Starting Tomcat...
2025-08-01 21:31:03,878 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: webui, contextRoot: webapp/webui, plugin: com.polarion.alm.ui, priority: -10]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,880 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion, contextRoot: webapp/authapp, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,880 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/.well-known, contextRoot: webapp/well-known, plugin: com.polarion.platform, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,880 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ProjectPlanGantt, contextRoot: webapp, plugin: com.polarion.alm.ProjectPlanGantt_new, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,881 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/activate, contextRoot: webapp/activation, plugin: com.polarion.psvn.launcher, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,881 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/announcements, contextRoot: webapp/announcements, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,882 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/bir, contextRoot: webapp/bir, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,882 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/checklist, contextRoot: src/main/webapp, plugin: com.fasnote.alm.checklist, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,882 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/codemirror-modes, contextRoot: webapp/codemirror-modes, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,885 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/defect, contextRoot: webapp, plugin: com.finething.hesai.defect, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,885 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/doorsconnector, contextRoot: webapp, plugin: com.polarion.synchronizer, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,886 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/export, contextRoot: webapp/export, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,886 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/fileupload, contextRoot: webapp/fileupload, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,886 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/gwt, contextRoot: war, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,888 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/hesai-ai, contextRoot: webapp, plugin: com.finething.hesai.ai, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,889 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/icons, contextRoot: webapp/icons, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,889 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/internal-login, contextRoot: webapp/internal-login, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,889 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/livechecklist, contextRoot: webapp, plugin: com.teamlive.livechecklist, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,889 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/module-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,889 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/modulehome, contextRoot: webapp/module-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,889 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/mxgraph, contextRoot: draw.io/war, plugin: com.polarion.alm.ui.diagrams.mxgraph, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,889 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/oauth-feishu, contextRoot: webapp, plugin: com.fasnote.alm.auth.feishu, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,889 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/page-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,889 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/perf-testing, contextRoot: webapp/perf-testing, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,890 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/plugin-manage, contextRoot: webapp, plugin: com.fasnote.alm.plugin.manage, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,891 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/print, contextRoot: webapp/print, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,891 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/register, contextRoot: webapp/register, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,891 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rest, contextRoot: webapp, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,891 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ria, contextRoot: webapp/ria, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,892 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/richpagehome, contextRoot: webapp/richpage-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,896 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt, contextRoot: src/main/webapp, plugin: com.siemens.polarion.rt, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,897 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt-connect, contextRoot: ws, plugin: com.siemens.polarion.rt.communication.polarion, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,897 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/scripting, contextRoot: webapp/scripting, plugin: com.polarion.scripting.servlet, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,897 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/sdk, contextRoot: webapp/sdk, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,897 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/svnwebclient, contextRoot: webapp, plugin: org.polarion.svnwebclient, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,897 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/swagger, contextRoot: webapp/swagger, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,897 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/synchronizer, contextRoot: webapp, plugin: com.polarion.synchronizer.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,897 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/template-download, contextRoot: webapp/project-template, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,897 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/testrun-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,898 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/tour, contextRoot: webapp/tour, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,898 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/watermark, contextRoot: webapp, plugin: com.fasnote.alm.watermark, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,898 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,898 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment-auth, contextRoot: webapp/wi-attachment-auth, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,898 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/widget-resource, contextRoot: webapp/widget-resource, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,898 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wiki, contextRoot: src/main/webapp, plugin: com.polarion.alm.wiki, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,898 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/workreport, contextRoot: webapp/workreport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,899 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ws, contextRoot: ws, plugin: com.polarion.alm.ws, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,899 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/xunitimport, contextRoot: webapp/xunitimport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:03,901 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/oslc, contextRoot: webapp, plugin: com.polarion.alm.oslc, priority: 1]'; protocol: AJP/1.3, port: 8889
2025-08-01 21:31:04,127 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Initializing ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-08-01 21:31:04,161 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 21:31:04,161 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-08-01 21:31:04,436 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7b3521db] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:04,436 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@53ccd995] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:04,436 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@50d65662] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:04,436 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@38c960b7] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:04,437 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@a123d8e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:04,439 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@501286c3] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:04,664 [Catalina-utility-2] INFO  org.apache.catalina.startup.ContextConfig - No global web.xml found
2025-08-01 21:31:04,839 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:04,838 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:04,840 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:04,840 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [admin] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:04,838 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:04,879 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@192ac204] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:04,879 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@54242d9f] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:04,882 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4ebbee76] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:04,883 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@117f2a90] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:04,886 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:04,887 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@516a54be] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:04,904 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1f75708d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:04,904 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:04,907 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@13aec952] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:04,929 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:04,937 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@66353861] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:04,955 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7494e6c7] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:04,972 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:04,973 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:05,029 [Catalina-utility-2] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-01 21:31:05,034 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:05,054 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@39eaaf01] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:05,054 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4a7f9a94] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:05,058 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@770cf7e6] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:05,068 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@441de5ff] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:05,076 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:05,114 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:05,141 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@38957508] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:05,149 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:05,182 [Catalina-utility-5] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-01 21:31:05,183 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@f989d46] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:05,185 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@279a4314] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:05,197 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@574c67b8] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:05,209 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:05,221 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:05,260 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@613b10bd] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:05,261 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4c9aaf7f] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:05,270 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@14335c47] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:05,283 [Catalina-utility-3] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/oauth-feishu] - For security constraints with URL pattern [/userinfo] only the HTTP methods [POST GET] are covered. All other methods are uncovered.
2025-08-01 21:31:05,284 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:05,285 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:05,302 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@8717557] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:05,324 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@352850de] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:05,377 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4719fe67] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:05,649 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.304 s, CPU [user: 0.00693 s, system: 0.00354 s], Allocated memory: 371.7 kB, transactions: 0, PullingJob: 0.257 s [100% collectChanges (1x)] (1x), svn: 0.255 s [68% testConnection (1x), 32% getLatestRevision (1x)] (2x)
2025-08-01 21:31:05,686 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@33440632] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:05,739 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:05,781 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2928651c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:06,073 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3673c520] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:06,187 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:06,471 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:06,520 [Catalina-utility-6] INFO  org.apache.tomcat.dbcp.dbcp2.BasicDataSourceFactory - Name = XWikiDS Ignoring unknown property: value of "DB Connection" for "description" property
2025-08-01 21:31:07,038 [Catalina-utility-6] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-01 21:31:07,206 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 47.27236328125
2025-08-01 21:31:07,841 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@18392e47] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:08,058 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7c17c87e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:08,106 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:08,117 [Catalina-utility-2] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-01 21:31:08,261 [Catalina-utility-3] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/oauth-feishu] - Servlet [FeishuUserInfoServlet] in web application [/polarion/oauth-feishu] threw load() exception
java.lang.ClassNotFoundException: com.fasnote.alm.auth.feishu.FeishuUserInfoServlet
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1407) ~[catalina.jar:9.0.53]
	at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1215) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.DefaultInstanceManager.loadClass(DefaultInstanceManager.java:538) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.DefaultInstanceManager.loadClassMaybePrivileged(DefaultInstanceManager.java:519) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.DefaultInstanceManager.newInstance(DefaultInstanceManager.java:149) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1049) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264) [catalina.jar:9.0.53]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386) [catalina.jar:9.0.53]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628) [?:?]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-01 21:31:08,288 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@22289a0c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:08,292 [Catalina-utility-4] WARN  org.springframework.web.context.support.AnnotationConfigWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Failed to introspect Class [com.finething.hesai.defect.controller.ProjectController] from ClassLoader [org.eclipse.osgi.internal.loader.EquinoxClassLoader@79f43cff[com.finething.hesai.defect:1.0.0.qualifier(id=19)]]
2025-08-01 21:31:08,294 [Catalina-utility-4] FATAL org.springframework.web.servlet.DispatcherServlet - Context initialization failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Failed to introspect Class [com.finething.hesai.defect.controller.ProjectController] from ClassLoader [org.eclipse.osgi.internal.loader.EquinoxClassLoader@79f43cff[com.finething.hesai.defect:1.0.0.qualifier(id=19)]]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1794) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264) [catalina.jar:9.0.53]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386) [catalina.jar:9.0.53]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628) [?:?]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.finething.hesai.defect.controller.ProjectController] from ClassLoader [org.eclipse.osgi.internal.loader.EquinoxClassLoader@79f43cff[com.finething.hesai.defect:1.0.0.qualifier(id=19)]]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:481) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.util.ReflectionUtils.doWithMethods(ReflectionUtils.java:358) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.core.MethodIntrospector.selectMethods(MethodIntrospector.java:72) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:273) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	... 30 more
Caused by: java.lang.NoClassDefFoundError: com/finething/hesai/defect/controller/ProjectController$1
	at java.lang.Class.getDeclaredMethods0(Native Method) ~[?:?]
	at java.lang.Class.privateGetDeclaredMethods(Class.java:3166) ~[?:?]
	at java.lang.Class.getDeclaredMethods(Class.java:2309) ~[?:?]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:463) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.util.ReflectionUtils.doWithMethods(ReflectionUtils.java:358) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.core.MethodIntrospector.selectMethods(MethodIntrospector.java:72) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:273) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	... 30 more
Caused by: java.lang.ClassNotFoundException: com.finething.hesai.defect.controller.ProjectController$1 cannot be found by com.finething.hesai.defect_1.0.0.qualifier
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:508) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at java.lang.ClassLoader.loadClass(ClassLoader.java:527) ~[?:?]
	at java.lang.Class.getDeclaredMethods0(Native Method) ~[?:?]
	at java.lang.Class.privateGetDeclaredMethods(Class.java:3166) ~[?:?]
	at java.lang.Class.getDeclaredMethods(Class.java:2309) ~[?:?]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:463) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.util.ReflectionUtils.doWithMethods(ReflectionUtils.java:358) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.core.MethodIntrospector.selectMethods(MethodIntrospector.java:72) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:273) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	... 30 more
2025-08-01 21:31:08,296 [Catalina-utility-4] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/defect] - Servlet.init() for servlet [defectSpreadServlet] threw exception
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in org.springframework.web.servlet.config.annotation.DelegatingWebMvcConfiguration: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Failed to introspect Class [com.finething.hesai.defect.controller.ProjectController] from ClassLoader [org.eclipse.osgi.internal.loader.EquinoxClassLoader@79f43cff[com.finething.hesai.defect:1.0.0.qualifier(id=19)]]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1794) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) ~[javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264) [catalina.jar:9.0.53]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386) [catalina.jar:9.0.53]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628) [?:?]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.finething.hesai.defect.controller.ProjectController] from ClassLoader [org.eclipse.osgi.internal.loader.EquinoxClassLoader@79f43cff[com.finething.hesai.defect:1.0.0.qualifier(id=19)]]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:481) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.util.ReflectionUtils.doWithMethods(ReflectionUtils.java:358) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.core.MethodIntrospector.selectMethods(MethodIntrospector.java:72) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:273) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	... 30 more
Caused by: java.lang.NoClassDefFoundError: com/finething/hesai/defect/controller/ProjectController$1
	at java.lang.Class.getDeclaredMethods0(Native Method) ~[?:?]
	at java.lang.Class.privateGetDeclaredMethods(Class.java:3166) ~[?:?]
	at java.lang.Class.getDeclaredMethods(Class.java:2309) ~[?:?]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:463) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.util.ReflectionUtils.doWithMethods(ReflectionUtils.java:358) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.core.MethodIntrospector.selectMethods(MethodIntrospector.java:72) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:273) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	... 30 more
Caused by: java.lang.ClassNotFoundException: com.finething.hesai.defect.controller.ProjectController$1 cannot be found by com.finething.hesai.defect_1.0.0.qualifier
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:508) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at java.lang.ClassLoader.loadClass(ClassLoader.java:527) ~[?:?]
	at java.lang.Class.getDeclaredMethods0(Native Method) ~[?:?]
	at java.lang.Class.privateGetDeclaredMethods(Class.java:3166) ~[?:?]
	at java.lang.Class.getDeclaredMethods(Class.java:2309) ~[?:?]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:463) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.util.ReflectionUtils.doWithMethods(ReflectionUtils.java:358) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.core.MethodIntrospector.selectMethods(MethodIntrospector.java:72) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:273) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	... 30 more
2025-08-01 21:31:08,302 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:08,303 [Catalina-utility-4] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/defect] - Servlet [defectSpreadServlet] in web application [/polarion/defect] threw load() exception
java.lang.ClassNotFoundException: com.finething.hesai.defect.controller.ProjectController$1 cannot be found by com.finething.hesai.defect_1.0.0.qualifier
	at org.eclipse.osgi.internal.loader.BundleLoader.findClassInternal(BundleLoader.java:508) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:419) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.osgi.internal.loader.BundleLoader.findClass(BundleLoader.java:411) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(ModuleClassLoader.java:150) ~[org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at java.lang.ClassLoader.loadClass(ClassLoader.java:527) ~[?:?]
	at java.lang.Class.getDeclaredMethods0(Native Method) ~[?:?]
	at java.lang.Class.privateGetDeclaredMethods(Class.java:3166) ~[?:?]
	at java.lang.Class.getDeclaredMethods(Class.java:2309) ~[?:?]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:463) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.util.ReflectionUtils.doWithMethods(ReflectionUtils.java:358) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.core.MethodIntrospector.selectMethods(MethodIntrospector.java:72) ~[org.springframework.spring-core_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:273) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897) ~[org.springframework.spring-beans_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551) ~[org.springframework.spring-context_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.configureAndRefreshWebApplicationContext(FrameworkServlet.java:702) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:668) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.createWebApplicationContext(FrameworkServlet.java:716) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initWebApplicationContext(FrameworkServlet.java:591) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.initServletBean(FrameworkServlet.java:530) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.HttpServletBean.init(HttpServletBean.java:170) ~[org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.GenericServlet.init(GenericServlet.java:158) ~[javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.StandardWrapper.initServlet(StandardWrapper.java:1143) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.loadServlet(StandardWrapper.java:1096) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapper.load(StandardWrapper.java:989) ~[catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.loadOnStartup(StandardContext.java:4957) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5264) [catalina.jar:9.0.53]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386) [catalina.jar:9.0.53]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628) [?:?]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-01 21:31:08,335 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@eb428c8] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:08,372 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:08,452 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@39215bc9] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:08,472 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:08,533 [Catalina-utility-3] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-01 21:31:08,537 [Catalina-utility-5] INFO  org.polarion.svncommons.commentscache.CommentsCache - Initializing comments cache. Id: http://localhost/repo, repository: http://localhost/repo/, url: http://localhost/repo/, cache directory: /opt/polarion/data/workspace/polarion-data/log-messages-cache, cache page size: 100
2025-08-01 21:31:08,587 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@462cd30e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:08,637 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@307befa3] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:08,715 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1b8f03af] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:08,834 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2fc13bf6] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:08,851 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:08,958 [Catalina-utility-4] INFO  com.polarion.portal.velocity.VelocityPathManager - VelocityTemplatesPath=/opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/authapp/, /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/, /opt/polarion/polarion/plugins/com.polarion.alm.wiki_3.22.1/src/main/webapp/, ., /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/
2025-08-01 21:31:09,250 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@24487ac9] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:09,389 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:09,408 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@79bb074b] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:09,479 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@88e78] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:09,482 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@45066bc9] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:09,496 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:09,526 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2deeb33f] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:09,629 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1e7c2bd6] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:09,642 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:09,676 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-01 21:31:09,733 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1a04a12] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:09,772 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4faad6d6] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:09,782 [Catalina-utility-6] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-01 21:31:09,948 [Catalina-utility-1] INFO  com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Spring context refreshed, scanning for prompt variables...
2025-08-01 21:31:09,992 [Catalina-utility-1] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: polarionTool, Class: com.finething.hesai.ai.util.VelocityPolarionTool, Methods found: 29
2025-08-01 21:31:10,004 [Catalina-utility-1] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: enumTool, Class: com.finething.hesai.ai.util.EnumUtil, Methods found: 1
2025-08-01 21:31:10,004 [Catalina-utility-1] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: linkWorkItemUtil, Class: com.finething.hesai.ai.util.LinkWorkItemUtil, Methods found: 6
2025-08-01 21:31:10,028 [Catalina-utility-3] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-08-01 21:31:10,030 [Catalina-utility-1] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: polarionContext, Class: com.finething.hesai.ai.service.impl.PolarionContextServiceImpl, Methods found: 4
2025-08-01 21:31:10,030 [Catalina-utility-1] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - No methods with @MethodDescription found for variable: polarionHelper, Class: com.finething.hesai.ai.service.impl.DefaultVariableDescriptionProvider$HelperMethods
2025-08-01 21:31:10,031 [Catalina-utility-1] INFO  com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Finished scanning. Found 4 prompt variables.
2025-08-01 21:31:10,522 [Catalina-utility-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Started.
2025-08-01 21:31:13,204 [Catalina-utility-3] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-08-01 21:31:13,217 [Catalina-utility-3] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[90]')
2025-08-01 21:31:13,221 [Catalina-utility-3] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[92]')
2025-08-01 21:31:13,225 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-08-01 21:31:13,245 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[90]')
2025-08-01 21:31:13,246 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[90]')
2025-08-01 21:31:13,262 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[96]')
2025-08-01 21:31:14,512 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Starting ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-08-01 21:31:14,555 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Tomcat is listening on port 8889 using AJP/1.3 protocol with 600000 timeout in millis
2025-08-01 21:31:14,556 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Starting Help Service...
2025-08-01 21:31:14,588 [main] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@522a8886] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-01 21:31:14,622 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Help Service started
2025-08-01 21:31:14,982 [main | u:p] INFO  com.xpn.xwiki.XWiki - xwiki.cfg taken from /WEB-INF/xwiki.cfg because the XWikiConfig variable is not set in the context
2025-08-01 21:31:15,600 [main | u:p | u:p] INFO  TXLOGGER - Tx 661975198703f_0_661975198703f_0_: finished. Total: 0.241 s, CPU [user: 0.0895 s, system: 0.0219 s], Allocated memory: 7.0 MB, resolve: 0.0372 s [100% ProjectGroup (3x)] (3x)
2025-08-01 21:31:16,497 [Thread-33] INFO  class com.polarion.alm.server.util.ChartExporterStartup - Chart renderer says: Server started on 127.0.0.1:34567
2025-08-01 21:31:17,101 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-08-01 21:31:17,207 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 47.75576171875
2025-08-01 21:31:17,584 [ajp-nio-127.0.0.1-8889-exec-1 | cID:65d46e7c-c0a844bd-5a6dbab7-8f2627cd] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-08-01 21:31:17,710 [ajp-nio-127.0.0.1-8889-exec-1 | cID:65d46e7c-c0a844bd-5a6dbab7-8f2627cd] INFO  TXLOGGER - Summary for 'servlet /polarion/rt/configuration-change-all': Total: 0.208 s, CPU [user: 0.0798 s, system: 0.0284 s], Allocated memory: 3.1 MB, transactions: 0
2025-08-01 21:31:17,738 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-08-01 21:31:17,739 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
2025-08-01 21:31:17,848 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" has id 65d46fa7-c0a844bd-5a6dbab7-1c63cc32
2025-08-01 21:31:17,852 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to UNSCHEDULED
2025-08-01 21:31:17,855 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "Attachment Indexer" is /opt/polarion/data/workspace/polarion-data/jobs/20250801-2131
2025-08-01 21:31:17,858 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" runs as user "polarion"
2025-08-01 21:31:17,864 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to ACTIVATING
2025-08-01 21:31:17,872 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to WAITING
2025-08-01 21:31:17,872 [main | u:p] INFO  com.polarion.platform.monitoring - Next slow periodic actions will be executed on Sat Aug 02 01:00:17 CST 2025
2025-08-01 21:31:17,872 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.tracker.internal.HttpsConfiguratorStartup successfully initialized
2025-08-01 21:31:17,876 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.ChartExporterStartup successfully initialized
2025-08-01 21:31:17,876 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.wiki.WikiPlugin successfully initialized
2025-08-01 21:31:17,876 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.qcentre.internal.QCentreStartup successfully initialized
2025-08-01 21:31:17,876 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.siemens.polarion.rt.communication.connection.RtCommunicationStartup successfully initialized
2025-08-01 21:31:17,882 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.internal.startup.NotificationServerStartup successfully initialized
2025-08-01 21:31:17,882 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.subterra.index.impl.IndexingJobsStartup successfully initialized
2025-08-01 21:31:17,882 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.ui.server.ServerStartup successfully initialized
2025-08-01 21:31:17,889 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.FormulaServerStartup successfully initialized
2025-08-01 21:31:17,889 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.monitoring.internal.MonitoringServiceStart successfully initialized
2025-08-01 21:31:17,892 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" has id 65d47000-c0a844bd-5a6dbab7-4d0f782c
2025-08-01 21:31:17,893 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to UNSCHEDULED
2025-08-01 21:31:17,894 [main] INFO  com.polarion.platform.monitoring - Executing actions from stage POSTBOOT
2025-08-01 21:31:17,894 [main] INFO  com.polarion.platform.monitoring - Executing action 'polarion.info.cache.statistics'
2025-08-01 21:31:17,896 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "DB History Creator" is /opt/polarion/data/workspace/polarion-data/jobs/20250801-2131_0
2025-08-01 21:31:17,900 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" runs as user "polarion"
2025-08-01 21:31:17,929 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to ACTIVATING
2025-08-01 21:31:17,932 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to WAITING
2025-08-01 21:31:17,983 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to RUNNING
2025-08-01 21:31:18,002 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to RUNNING
2025-08-01 21:31:18,005 [main] INFO  com.polarion.platform.monitoring - polarion.info.cache.statistics (Statistics of caches) = Statistics of caches 
                         CACHE       HITS     MISSES  HIT_RATIO       GETS       PUTS    REMOVAL   EVICTION 
     attachments-content-cache          0          0          0%          0          0          0          0 
                baseline-cache          0          0          0%          0          0          0          0 
                            bq          0          0          0%          0          0          0          0 
                dao-attachment          0          0          0%          0          0          0          0 
                   dao-comment          0          0          0%          0          0          0          0 
                    dao-global          0         26          0%         26         11          0          0 
                    dao-module          0          0          0%          0          0          0          0 
          dao-moduleattachment          0          0          0%          0          0          0          0 
             dao-modulecomment          0          0          0%          0          0          0          0 
                      dao-plan          0          0          0%          0          0          0          0 
                   dao-project          0          0          0%          0          0          0          0 
              dao-projectgroup          0          6          0%          6          3          0          0 
                  dao-richpage          0          0          0%          0          0          0          0 
        dao-richpageattachment          0          0          0%          0          0          0          0 
                   dao-testrun          0          0          0%          0          0          0          0 
         dao-testrunattachment          0          0          0%          0          0          0          0 
                      dao-user          0          0          0%          0          0          0          0 
                  dao-wikipage          0          0          0%          0          0          0          0 
        dao-wikipageattachment          0          0          0%          0          0          0          0 
                  dao-workitem          0          0          0%          0          0          0          0 
      derived-linked-revisions          0          0          0%          0          0          0          0 
                  formulas-svg          0          0          0%          0          0          0          0 
                github-commits          0          0          0%          0          0          0          0 
            historical-queries          0          0          0%          0          0          0          0 
      historical-queries-sizes          0          0          0%          0          0          0          0 
        oslc-linked-item-cache          0          0          0%          0          0          0          0 
               plan-statistics          0          0          0%          0          0          0          0 
                   rmd-default          4          5         44%          9          7          0          0 
                   ss-combined          0          0          0%          0          0          0          0 
                    ss-context          0          0          0%          0          0          0          0 
                     wiki-page          2          8         20%         10          9          1          0 
              wiki-page-exists          0          0          0%          0          9          1          0 

 [Fri Aug 01 21:31:18 CST 2025]
2025-08-01 21:31:18,023 [Thread-36] INFO  com.polarion.core.util.process.JavaRunner - Executing /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/bin/java
  -- args [-jar, /opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar, --server.port=40608, --jwksUrl=http://localhost/polarion/.well-known/jwks.json]
  -- env null
  -- dir /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess4323185135773266800.tmp
2025-08-01 21:31:18,035 [main] INFO  com.polarion.platform.monitoring - Finished with actions from stage POSTBOOT: {OK=1}
2025-08-01 21:31:18,038 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 15.66 s. ]
2025-08-01 21:31:18,042 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 1.86 s [74% info (158x), 11% getLatestRevision (6x)] (174x), PullingJob: 0.37 s [100% collectChanges (5x)] (5x)
2025-08-01 21:31:18,042 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-01 21:31:18,042 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 40.8 s. ]
2025-08-01 21:31:18,042 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-01 21:31:18,054 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to FINISHED
2025-08-01 21:31:18,079 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - Status of job "Attachment Indexer" is OK
2025-08-01 21:31:18,094 [PreLoadDataService] INFO  com.polarion.psvn.launcher.internal.data.PreLoadDataService - Preloading data ...
2025-08-01 21:31:19,060 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Tx command #144: finished. Total: 0.203 s, CPU [user: 0.00611 s, system: 0.00463 s], Allocated memory: 349.4 kB, Lucene: 0.067 s [100% refresh (1x)] (1x)
2025-08-01 21:31:19,063 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Tx command #145: finished. Total: 0.196 s, CPU [user: 0.00462 s, system: 0.00177 s], Allocated memory: 256.5 kB, Lucene: 0.189 s [100% refresh (1x)] (1x)
2025-08-01 21:31:19,455 [Notification-Worker-6 | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem
2025-08-01 21:31:19,646 [Notification-Worker-6 | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem
2025-08-01 21:31:19,675 [Activities-Bulk-Publisher] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Activities
2025-08-01 21:31:19,735 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-08-01 21:31:19,753 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-08-01 21:31:19,809 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-08-01 21:31:19,846 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-08-01 21:31:20,494 [Thread-43] INFO  class com.polarion.alm.server.util.FormulaServerStartup - Formula renderer says: Server started on 127.0.0.1:34568
2025-08-01 21:31:20,917 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6619751c3cc44_0_6619751c3cc44_0_: finished. Total: 2.79 s, CPU [user: 0.197 s, system: 0.0411 s], Allocated memory: 15.3 MB, resolve: 0.665 s [98% User (2x)] (4x), ObjectMaps: 0.291 s [98% getPrimaryObjectLocation (1x)] (6x), Lucene: 0.217 s [100% search (1x)] (1x)
2025-08-01 21:31:21,374 [Thread-40] INFO  NotificationService - 
2025-08-01 21:31:21,382 [Thread-40] INFO  NotificationService -   .   ____          _            __ _ _
2025-08-01 21:31:21,382 [Thread-40] INFO  NotificationService -  /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
2025-08-01 21:31:21,382 [Thread-40] INFO  NotificationService - ( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
2025-08-01 21:31:21,382 [Thread-40] INFO  NotificationService -  \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
2025-08-01 21:31:21,382 [Thread-40] INFO  NotificationService -   '  |____| .__|_| |_|_| |_\__, | / / / /
2025-08-01 21:31:21,382 [Thread-40] INFO  NotificationService -  =========|_|==============|___/=/_/_/_/
2025-08-01 21:31:21,382 [Thread-40] INFO  NotificationService -  :: Spring Boot ::                (v2.6.6)
2025-08-01 21:31:21,382 [Thread-40] INFO  NotificationService - 
2025-08-01 21:31:22,216 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Starting Application using Java 11.0.27 on zhangwendeMini2.lan with PID 20159 (/opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar started by zhangwentian in /private/var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess4323185135773266800.tmp)
2025-08-01 21:31:22,219 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - No active profile set, falling back to 1 default profile: "default"
2025-08-01 21:31:22,303 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 25, for prototypes: WorkItem; with days range: 180d, took  [ TIME 3.23 s. ] 
2025-08-01 21:31:22,306 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 3.45 s, CPU [user: 0.00654 s, system: 0.00262 s], Allocated memory: 424.5 kB, transactions: 1, GC: 0.207 s [100% G1 Young Generation (1x)] (1x), Lucene: 0.189 s [100% refresh (1x)] (1x)
2025-08-01 21:31:22,306 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 3.45 s, CPU [user: 0.00801 s, system: 0.00564 s], Allocated memory: 544.8 kB, transactions: 1, GC: 0.207 s [100% G1 Young Generation (1x)] (1x)
2025-08-01 21:31:22,308 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 72, notification worker: 2.97 s [99% RevisionActivityCreator (18x)] (54x), resolve: 0.857 s [96% User (3x)] (6x), Lucene: 0.551 s [47% refresh (2x), 39% search (1x)] (5x), ObjectMaps: 0.409 s [98% getPrimaryObjectLocation (2x)] (7x), Incremental Baseline: 0.238 s [100% WorkItem (24x)] (24x)
2025-08-01 21:31:22,310 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 4.3 s, CPU [user: 0.239 s, system: 0.0609 s], Allocated memory: 18.9 MB, transactions: 25, svn: 2.9 s [99% getDatedRevision (181x)] (183x), Lucene: 0.247 s [62% buildBaselineSnapshots (1x), 38% buildBaseline (25x)] (26x)
2025-08-01 21:31:22,318 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to FINISHED
2025-08-01 21:31:22,319 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - Status of job "DB History Creator" is OK
2025-08-01 21:31:24,750 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 25, for prototypes: WorkItem; with days range: 180d, took  [ TIME 1.67 s. ] 
2025-08-01 21:31:24,914 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6619751c43045_0_6619751c43045_0_: finished. Total: 6.76 s, CPU [user: 0.655 s, system: 0.212 s], Allocated memory: 52.5 MB, svn: 3.13 s [51% getDatedRevision (181x), 31% getDir2 content (25x)] (328x), resolve: 2.67 s [100% Category (117x)] (117x), ObjectMaps: 0.969 s [60% getPrimaryObjectLocation (117x), 23% getPrimaryObjectProperty (117x)] (473x)
2025-08-01 21:31:25,094 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-08-01 21:31:25,145 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-08-01 21:31:25,284 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66197522dcc6e_0_66197522dcc6e_0_: finished. Total: 0.368 s, CPU [user: 0.0222 s, system: 0.0101 s], Allocated memory: 1.8 MB, Lucene: 0.127 s [100% search (1x)] (1x), svn: 0.0421 s [61% getDir2 content (2x), 39% info (1x)] (4x)
2025-08-01 21:31:25,303 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-08-01 21:31:25,309 [Thread-40] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 40608 (http)
2025-08-01 21:31:25,312 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-08-01 21:31:25,330 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-40608"]
2025-08-01 21:31:25,330 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-01 21:31:25,330 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-01 21:31:25,443 [Thread-40] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-01 21:31:25,443 [Thread-40] INFO  NotificationService - [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2904 ms
2025-08-01 21:31:25,508 [Thread-40] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing AtmosphereFramework
2025-08-01 21:31:25,510 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661975233906f_0_661975233906f_0_: finished. Total: 0.221 s, CPU [user: 0.0458 s, system: 0.0157 s], Allocated memory: 3.8 MB, resolve: 0.163 s [100% Project (6x)] (6x), svn: 0.074 s [36% getFile content (6x), 33% info (6x), 30% log (3x)] (16x), ObjectMaps: 0.0657 s [51% getPrimaryObjectProperty (6x), 41% getPrimaryObjectLocation (6x)] (25x), GlobalHandler: 0.0262 s [98% applyTxChanges (1x)] (7x)
2025-08-01 21:31:25,523 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-08-01 21:31:25,561 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-08-01 21:31:26,161 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6619752371870_0_6619752371870_0_: finished. Total: 0.651 s, CPU [user: 0.0926 s, system: 0.0293 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.295 s [59% getReadUserConfiguration (10x), 41% getReadConfiguration (180x)] (190x), svn: 0.239 s [68% info (21x), 27% getFile content (16x)] (39x), resolve: 0.133 s [100% User (9x)] (9x), ObjectMaps: 0.08 s [60% getPrimaryObjectProperty (8x), 28% getPrimaryObjectLocation (8x)] (32x), GlobalHandler: 0.0435 s [88% applyTxChanges (2x)] (29x), Lucene: 0.0381 s [100% search (1x)] (1x)
2025-08-01 21:31:26,342 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-40608"]
2025-08-01 21:31:26,417 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6619752414871_0_6619752414871_0_: finished. Total: 0.255 s, CPU [user: 0.0451 s, system: 0.00975 s], Allocated memory: 4.0 MB, RepositoryConfigService: 0.172 s [97% getReadConfiguration (54x)] (77x), svn: 0.0913 s [100% getFile content (13x)] (14x)
2025-08-01 21:31:26,528 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using org.atmosphere.cpr.DefaultAnnotationProcessor for processing annotation
2025-08-01 21:31:26,528 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.DefaultAnnotationProcessor - AnnotationProcessor class org.atmosphere.cpr.DefaultAnnotationProcessor$BytecodeBasedAnnotationProcessor being used
2025-08-01 21:31:26,568 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AnnotationHandler - Found Annotation in class com.siemens.polarion.service.notification.NotificationService being scanned: interface org.atmosphere.config.service.ManagedService
2025-08-01 21:31:26,577 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.AtmosphereResourceLifecycleInterceptor
2025-08-01 21:31:26,577 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.client.TrackMessageSizeInterceptor
2025-08-01 21:31:26,585 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.SuspendTrackerInterceptor
2025-08-01 21:31:26,587 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.config.managed.ManagedServiceInterceptor
2025-08-01 21:31:26,616 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class com.siemens.polarion.service.notification.JwtVerificationInterceptor
2025-08-01 21:31:26,626 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.util.ForkJoinPool - Using ForkJoinPool  java.util.concurrent.ForkJoinPool. Set the org.atmosphere.cpr.broadcaster.maxAsyncWriteThreads to -1 to fully use its power.
2025-08-01 21:31:26,634 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler mapped to context-path /notification and Broadcaster Class org.atmosphere.cpr.DefaultBroadcaster
2025-08-01 21:31:26,635 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor [Atmosphere LifeCycle,  Track Message Size Interceptor using |, UUID Tracking Interceptor, @ManagedService Interceptor, @Service Event Listeners, com.siemens.polarion.service.notification.JwtVerificationInterceptor] mapped to AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler
2025-08-01 21:31:26,654 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Auto detecting WebSocketHandler in /WEB-INF/classes/
2025-08-01 21:31:26,656 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed WebSocketProtocol org.atmosphere.websocket.protocol.SimpleHttpProtocol 
2025-08-01 21:31:26,662 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.container.JSR356AsyncSupport - JSR 356 Mapping path /notification
2025-08-01 21:31:26,679 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installing Default AtmosphereInterceptors
2025-08-01 21:31:26,679 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CorsInterceptor : CORS Interceptor Support
2025-08-01 21:31:26,679 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CacheHeadersInterceptor : Default Response's Headers Interceptor
2025-08-01 21:31:26,679 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.PaddingAtmosphereInterceptor : Browser Padding Interceptor Support
2025-08-01 21:31:26,683 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.AndroidAtmosphereInterceptor : Android Interceptor Support
2025-08-01 21:31:26,691 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.HeartbeatInterceptor : Heartbeat Interceptor Support
2025-08-01 21:31:26,691 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.SSEAtmosphereInterceptor : SSE Interceptor Support
2025-08-01 21:31:26,691 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JSONPAtmosphereInterceptor : JSONP Interceptor Support
2025-08-01 21:31:26,695 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JavaScriptProtocol : Atmosphere JavaScript Protocol
2025-08-01 21:31:26,695 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor : org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor
2025-08-01 21:31:26,695 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.OnDisconnectInterceptor : Browser disconnection detection
2025-08-01 21:31:26,700 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.IdleResourceInterceptor : org.atmosphere.interceptor.IdleResourceInterceptor
2025-08-01 21:31:26,700 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Set org.atmosphere.cpr.AtmosphereInterceptor.disableDefaults to disable them.
2025-08-01 21:31:26,700 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor CORS Interceptor Support with priority FIRST_BEFORE_DEFAULT 
2025-08-01 21:31:26,703 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Default Response's Headers Interceptor with priority AFTER_DEFAULT 
2025-08-01 21:31:26,703 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser Padding Interceptor Support with priority AFTER_DEFAULT 
2025-08-01 21:31:26,703 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Android Interceptor Support with priority AFTER_DEFAULT 
2025-08-01 21:31:26,703 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.interceptor.HeartbeatInterceptor - HeartbeatInterceptor configured with padding value 'X', client frequency 30 seconds and server frequency 120 seconds
2025-08-01 21:31:26,703 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Heartbeat Interceptor Support with priority AFTER_DEFAULT 
2025-08-01 21:31:26,703 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor SSE Interceptor Support with priority AFTER_DEFAULT 
2025-08-01 21:31:26,703 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor JSONP Interceptor Support with priority AFTER_DEFAULT 
2025-08-01 21:31:26,703 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Atmosphere JavaScript Protocol with priority AFTER_DEFAULT 
2025-08-01 21:31:26,703 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor with priority AFTER_DEFAULT 
2025-08-01 21:31:26,704 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser disconnection detection with priority AFTER_DEFAULT 
2025-08-01 21:31:26,704 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.IdleResourceInterceptor with priority BEFORE_DEFAULT 
2025-08-01 21:31:26,713 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using EndpointMapper class org.atmosphere.util.DefaultEndpointMapper
2025-08-01 21:31:26,714 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterCache: org.atmosphere.cache.UUIDBroadcasterCache
2025-08-01 21:31:26,714 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Default Broadcaster Class: org.atmosphere.cpr.DefaultBroadcaster
2025-08-01 21:31:26,714 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Shared List Resources: false
2025-08-01 21:31:26,714 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Polling Wait Time 100
2025-08-01 21:31:26,714 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Shared ExecutorService supported: true
2025-08-01 21:31:26,714 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Messaging ExecutorService Pool Size unavailable - Not instance of ThreadPoolExecutor
2025-08-01 21:31:26,714 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Async I/O Thread Pool Size: 200
2025-08-01 21:31:26,714 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterFactory: org.atmosphere.cpr.DefaultBroadcasterFactory
2025-08-01 21:31:26,714 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using AtmosphereResurceFactory: org.atmosphere.cpr.DefaultAtmosphereResourceFactory
2025-08-01 21:31:26,714 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using WebSocketProcessor: org.atmosphere.websocket.DefaultWebSocketProcessor
2025-08-01 21:31:26,714 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Invoke AtmosphereInterceptor on WebSocket message true
2025-08-01 21:31:26,714 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - HttpSession supported: false
2025-08-01 21:31:26,714 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using Spring Web ObjectFactory for dependency injection and object creation
2025-08-01 21:31:26,714 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using async support: org.atmosphere.container.JSR356AsyncSupport running under container: Apache Tomcat/9.0.60 using javax.servlet/3.0 and jsr356/WebSocket API
2025-08-01 21:31:26,715 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere Framework 2.6.4 started.
2025-08-01 21:31:26,715 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 
2025-08-01 21:31:26,715 [Thread-40] INFO  NotificationService - 
2025-08-01 21:31:26,715 [Thread-40] INFO  NotificationService - 	For Atmosphere Framework Commercial Support, visit 
2025-08-01 21:31:26,715 [Thread-40] INFO  NotificationService - 	http://www.async-io.org/ or send an <NAME_EMAIL>
2025-08-01 21:31:26,715 [Thread-40] INFO  NotificationService - 
2025-08-01 21:31:26,766 [Thread-40] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 40608 (http) with context path ''
2025-08-01 21:31:26,789 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Started Application in 6.813 seconds (JVM running for 8.721)
2025-08-01 21:31:27,202 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testtype) created
2025-08-01 21:31:27,204 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 48.55302734375
2025-08-01 21:31:27,229 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (subtype) created
2025-08-01 21:31:27,262 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6619752454872_0_6619752454872_0_: finished. Total: 0.843 s, CPU [user: 0.131 s, system: 0.0294 s], Allocated memory: 19.8 MB, svn: 0.595 s [80% getDir2 content (17x), 20% getFile content (44x)] (62x), RepositoryConfigService: 0.247 s [98% getReadConfiguration (170x)] (192x)
2025-08-01 21:31:28,081 [Thread-36] INFO  NotificationService - Notification service was started successfully.
2025-08-01 21:31:28,700 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (YesNo) created
2025-08-01 21:31:28,733 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (software_VerificationMethod) created
2025-08-01 21:31:28,763 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checklist) created
2025-08-01 21:31:28,787 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonreqproperty) created
2025-08-01 21:31:28,819 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectoriented) created
2025-08-01 21:31:28,822 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submodule) created
2025-08-01 21:31:28,829 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (yesno) created
2025-08-01 21:31:28,833 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (cICategory) created
2025-08-01 21:31:28,835 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (wpFormat) created
2025-08-01 21:31:28,847 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (trigger) created
2025-08-01 21:31:28,875 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ASILLevel) created
2025-08-01 21:31:28,879 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CSRelated) created
2025-08-01 21:31:28,907 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_Module) created
2025-08-01 21:31:28,977 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (规格对象类型) created
2025-08-01 21:31:28,993 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (jenkins_job) created
2025-08-01 21:31:28,994 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (truefalse) created
2025-08-01 21:31:29,000 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (takeOnGroups) created
2025-08-01 21:31:29,048 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasetype) created
2025-08-01 21:31:29,076 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processGroup) created
2025-08-01 21:31:29,090 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeManagement) created
2025-08-01 21:31:29,120 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (seriousness) created
2025-08-01 21:31:29,158 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softReqClass) created
2025-08-01 21:31:29,180 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWDetailDesign) created
2025-08-01 21:31:29,192 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PhaseChecklists) created
2025-08-01 21:31:29,205 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PassNotpass) created
2025-08-01 21:31:29,218 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineType) created
2025-08-01 21:31:29,230 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (boolYesOrNo) created
2025-08-01 21:31:29,241 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testlevel) created
2025-08-01 21:31:29,253 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (source) created
2025-08-01 21:31:29,269 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectType) created
2025-08-01 21:31:29,279 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atppblversion) created
2025-08-01 21:31:29,287 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (aSIL) created
2025-08-01 21:31:29,308 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (EE) created
2025-08-01 21:31:29,319 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueType) created
2025-08-01 21:31:29,331 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SYS_reqClassification) created
2025-08-01 21:31:29,342 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (oem_2Status) created
2025-08-01 21:31:29,355 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (class) created
2025-08-01 21:31:29,368 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (promotionState) created
2025-08-01 21:31:29,376 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (git_project) created
2025-08-01 21:31:29,397 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (storageType) created
2025-08-01 21:31:29,407 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueproperty) created
2025-08-01 21:31:29,439 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonissueclass) created
2025-08-01 21:31:29,454 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (AgreeDisagree) created
2025-08-01 21:31:29,489 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SA_Category) created
2025-08-01 21:31:29,513 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relevance) created
2025-08-01 21:31:29,530 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (implementationPhase) created
2025-08-01 21:31:29,562 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplier_2Status) created
2025-08-01 21:31:29,579 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Testtype) created
2025-08-01 21:31:29,593 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (conf_baselineTime) created
2025-08-01 21:31:29,626 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (levelneed) created
2025-08-01 21:31:29,633 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (finalresult) created
2025-08-01 21:31:29,646 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testability) created
2025-08-01 21:31:29,656 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solution) created
2025-08-01 21:31:29,665 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Responsible) created
2025-08-01 21:31:29,676 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationstatus) created
2025-08-01 21:31:29,707 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsiassigngroup) created
2025-08-01 21:31:29,715 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqCategory) created
2025-08-01 21:31:29,721 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineName) created
2025-08-01 21:31:29,728 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskType) created
2025-08-01 21:31:29,760 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeReason) created
2025-08-01 21:31:29,772 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectmodule) created
2025-08-01 21:31:29,794 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseOutputMethod) created
2025-08-01 21:31:29,803 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SoftwareFeature) created
2025-08-01 21:31:29,821 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ResponsibleGroup) created
2025-08-01 21:31:29,837 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsifunctionmodule) created
2025-08-01 21:31:29,851 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (FwReqSource) created
2025-08-01 21:31:29,873 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (occurPhase) created
2025-08-01 21:31:29,900 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (compiletask) created
2025-08-01 21:31:29,941 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBVerificationMethod) created
2025-08-01 21:31:29,949 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (functionmodule) created
2025-08-01 21:31:29,957 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (variant) created
2025-08-01 21:31:29,961 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Fusatype) created
2025-08-01 21:31:29,968 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareversion) created
2025-08-01 21:31:29,980 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (appversion) created
2025-08-01 21:31:29,987 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casefirstmodule) created
2025-08-01 21:31:29,994 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditType) created
2025-08-01 21:31:30,006 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Samplestage) created
2025-08-01 21:31:30,013 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casesecondmodule) created
2025-08-01 21:31:30,019 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issue_source) created
2025-08-01 21:31:30,027 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ifNeedRegressionTesting) created
2025-08-01 21:31:30,035 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atpsfsversion) created
2025-08-01 21:31:30,043 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CustomerAllocation) created
2025-08-01 21:31:30,050 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issuesubclass) created
2025-08-01 21:31:30,058 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_importance) created
2025-08-01 21:31:30,065 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reviewMethod) created
2025-08-01 21:31:30,074 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_findType) created
2025-08-01 21:31:30,083 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (editType) created
2025-08-01 21:31:30,098 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testingobjects) created
2025-08-01 21:31:30,106 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcaselevel) created
2025-08-01 21:31:30,117 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplierproblem) created
2025-08-01 21:31:30,130 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqattribute) created
2025-08-01 21:31:30,147 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (fsigroup) created
2025-08-01 21:31:30,158 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_reqsource) created
2025-08-01 21:31:30,167 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (preset) created
2025-08-01 21:31:30,182 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Mechverificationmethod) created
2025-08-01 21:31:30,214 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CPMToTPM) created
2025-08-01 21:31:30,243 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBType) created
2025-08-01 21:31:30,275 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasesign) created
2025-08-01 21:31:30,339 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationphase) created
2025-08-01 21:31:30,363 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processArea) created
2025-08-01 21:31:30,372 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (artifactType) created
2025-08-01 21:31:30,392 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Classification) created
2025-08-01 21:31:30,415 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationmethod) created
2025-08-01 21:31:30,424 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeType) created
2025-08-01 21:31:30,431 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareAndSoftwareSubType) created
2025-08-01 21:31:30,442 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWIntegrationVerificationMethod) created
2025-08-01 21:31:30,456 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (category) created
2025-08-01 21:31:30,503 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBCategory) created
2025-08-01 21:31:30,524 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softreqclass) created
2025-08-01 21:31:30,530 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestMethod) created
2025-08-01 21:31:30,538 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reType) created
2025-08-01 21:31:30,546 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (VerificationCriteria) created
2025-08-01 21:31:30,553 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLinechecklist) created
2025-08-01 21:31:30,559 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Category) created
2025-08-01 21:31:30,569 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWUnitTestDerivingMethods) created
2025-08-01 21:31:30,575 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (firmware_Category) created
2025-08-01 21:31:30,585 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testMethod) created
2025-08-01 21:31:30,628 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QAPorcessAreas) created
2025-08-01 21:31:30,649 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (findSource) created
2025-08-01 21:31:30,699 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6619752527873_0_6619752527873_0_: finished. Total: 3.44 s, CPU [user: 0.989 s, system: 0.212 s], Allocated memory: 1.1 GB, RepositoryConfigService: 2.6 s [98% getReadConfiguration (8682x)] (9021x), svn: 1.58 s [62% getFile content (412x), 38% getDir2 content (21x)] (434x), GC: 0.181 s [100% G1 Young Generation (5x)] (5x)
2025-08-01 21:31:31,043 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (tshirt-sizes) created
2025-08-01 21:31:31,051 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqtype) created
2025-08-01 21:31:31,060 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6619752882c74_0_6619752882c74_0_: finished. Total: 0.36 s, CPU [user: 0.0755 s, system: 0.0129 s], Allocated memory: 17.9 MB, svn: 0.296 s [74% getDir2 content (18x), 26% getFile content (29x)] (48x), RepositoryConfigService: 0.127 s [99% getReadConfiguration (124x)] (148x)
2025-08-01 21:31:31,230 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66197528dd075_0_66197528dd075_0_: finished. Total: 0.166 s, CPU [user: 0.0381 s, system: 0.00577 s], Allocated memory: 5.3 MB, svn: 0.144 s [89% getDir2 content (11x)] (20x), RepositoryConfigService: 0.0302 s [96% getReadConfiguration (38x)] (54x)
2025-08-01 21:31:31,884 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Feasibility) created
2025-08-01 21:31:31,900 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (locaMod) created
2025-08-01 21:31:31,906 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseType) created
2025-08-01 21:31:31,911 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ControlLevel) created
2025-08-01 21:31:31,919 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (NCitemSev) created
2025-08-01 21:31:31,924 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (signType) created
2025-08-01 21:31:31,943 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (WBSCategory) created
2025-08-01 21:31:31,957 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseEnv) created
2025-08-01 21:31:31,969 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verifiability) created
2025-08-01 21:31:31,977 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ProjectUser) created
2025-08-01 21:31:31,987 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (standardReq) created
2025-08-01 21:31:31,994 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (statusa) created
2025-08-01 21:31:32,001 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@WorkItems[type:configurationitemversion]) created
2025-08-01 21:31:32,003 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CIRevisionStatus) created
2025-08-01 21:31:32,014 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (dogTimeout) created
2025-08-01 21:31:32,021 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQCategory) created
2025-08-01 21:31:32,043 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proStage) created
2025-08-01 21:31:32,060 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (BaselineType) created
2025-08-01 21:31:32,075 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (custConfStat) created
2025-08-01 21:31:32,100 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sofReqVer) created
2025-08-01 21:31:32,122 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Source) created
2025-08-01 21:31:32,141 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scCategory) created
2025-08-01 21:31:32,149 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseType) created
2025-08-01 21:31:32,157 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solAdv) created
2025-08-01 21:31:32,170 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseMet) created
2025-08-01 21:31:32,182 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMeth) created
2025-08-01 21:31:32,194 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseMe) created
2025-08-01 21:31:32,213 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseEnv) created
2025-08-01 21:31:32,233 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTarget) created
2025-08-01 21:31:32,246 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ReviewForm) created
2025-08-01 21:31:32,252 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseType) created
2025-08-01 21:31:32,258 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@Collection) created
2025-08-01 21:31:32,261 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submissionStage) created
2025-08-01 21:31:32,270 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMet) created
2025-08-01 21:31:32,274 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandType) created
2025-08-01 21:31:32,279 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseMet) created
2025-08-01 21:31:32,282 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskUrgen) created
2025-08-01 21:31:32,286 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solveMethod) created
2025-08-01 21:31:32,293 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (audMethod) created
2025-08-01 21:31:32,298 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (desStat) created
2025-08-01 21:31:32,310 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scType) created
2025-08-01 21:31:32,316 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseType) created
2025-08-01 21:31:32,322 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (releaseType) created
2025-08-01 21:31:32,331 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseEnv) created
2025-08-01 21:31:32,336 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (targetStage) created
2025-08-01 21:31:32,341 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ClassificationType) created
2025-08-01 21:31:32,351 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testItem) created
2025-08-01 21:31:32,362 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (InfoSecurity) created
2025-08-01 21:31:32,371 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Verification) created
2025-08-01 21:31:32,377 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMod) created
2025-08-01 21:31:32,384 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verMethod) created
2025-08-01 21:31:32,390 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (diagramCategory) created
2025-08-01 21:31:32,399 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (assSubsystem) created
2025-08-01 21:31:32,404 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (OccurrenceProbability) created
2025-08-01 21:31:32,412 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (developmentMethod) created
2025-08-01 21:31:32,415 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (portType) created
2025-08-01 21:31:32,424 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checkType) created
2025-08-01 21:31:32,430 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandStatus) created
2025-08-01 21:31:32,447 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (important) created
2025-08-01 21:31:32,487 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMec) created
2025-08-01 21:31:32,500 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseTy) created
2025-08-01 21:31:32,510 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (recentPre) created
2025-08-01 21:31:32,521 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseDesignMethod) created
2025-08-01 21:31:32,527 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testCasePri) created
2025-08-01 21:31:32,557 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relObj) created
2025-08-01 21:31:32,569 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proSer) created
2025-08-01 21:31:32,575 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestProblemType) created
2025-08-01 21:31:32,580 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (chipName) created
2025-08-01 21:31:32,586 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTiming) created
2025-08-01 21:31:32,593 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6619752907c76_0_6619752907c76_0_: finished. Total: 1.36 s, CPU [user: 0.377 s, system: 0.0933 s], Allocated memory: 384.3 MB, RepositoryConfigService: 0.935 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.722 s [53% getFile content (185x), 47% getDir2 content (21x)] (207x)
2025-08-01 21:31:32,848 [PreLoadDataService | u:p] ERROR com.polarion.subterra.base.data.model.CustomField - Unknown custom field type 'enum' - using 'string' - for field with id 'taskType' for 'WorkItem task /default/WBS'
2025-08-01 21:31:32,882 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6619752a5c477_0_6619752a5c477_0_: finished. Total: 0.288 s, CPU [user: 0.0715 s, system: 0.0102 s], Allocated memory: 13.1 MB, svn: 0.25 s [79% getDir2 content (18x), 21% getFile content (33x)] (52x), RepositoryConfigService: 0.0818 s [98% getReadConfiguration (128x)] (150x)
2025-08-01 21:31:32,882 [PreLoadDataService] INFO  com.polarion.psvn.launcher.internal.data.PreLoadDataService - Preloading data FINISHED took  [ TIME 14.8 s. ]
2025-08-01 21:31:32,882 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 14.8 s, CPU [user: 2.55 s, system: 0.643 s], Allocated memory: 1.6 GB, transactions: 11, svn: 7.16 s [41% getDir2 content (133x), 31% getFile content (865x), 22% getDatedRevision (181x)] (1224x), RepositoryConfigService: 4.58 s [94% getReadConfiguration (12165x)] (12859x), resolve: 2.98 s [90% Category (117x)] (139x), ObjectMaps: 1.12 s [56% getPrimaryObjectLocation (137x), 27% getPrimaryObjectProperty (131x)] (536x)
2025-08-01 21:31:32,883 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 61, svn: 10.1 s [44% getDatedRevision (362x), 29% getDir2 content (133x), 22% getFile content (865x)] (1411x), RepositoryConfigService: 4.58 s [94% getReadConfiguration (12165x)] (12859x), resolve: 2.98 s [90% Category (117x)] (140x), ObjectMaps: 1.12 s [56% getPrimaryObjectLocation (137x), 27% getPrimaryObjectProperty (131x)] (536x), Lucene: 0.563 s [52% search (5x), 27% buildBaselineSnapshots (2x), 21% buildBaseline (50x)] (58x)
2025-08-01 21:31:37,200 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 46.2478515625
2025-08-01 21:31:47,204 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 39.31943359375
2025-08-01 21:31:57,204 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 33.467724609375
2025-08-01 21:32:00,638 [ajp-nio-127.0.0.1-8889-exec-3 | cID:65d516c4-c0a844bd-5a6dbab7-fc79f662] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /polarion/checklist/api/api/templates
2025-08-01 21:32:00,765 [ajp-nio-127.0.0.1-8889-exec-3 | cID:65d516c4-c0a844bd-5a6dbab7-fc79f662] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/api/templates?type=code-review&_t=1754055120560': Total: 0.183 s, CPU [user: 0.0132 s, system: 0.0379 s], Allocated memory: 640.4 kB, transactions: 0
2025-08-01 21:32:07,202 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 28.413818359375
2025-08-01 21:32:07,553 [ajp-nio-127.0.0.1-8889-exec-5 | cID:65d531ff-c0a844bd-5a6dbab7-5f446988] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /polarion/checklist/api/api/templates
2025-08-01 21:32:07,553 [ajp-nio-127.0.0.1-8889-exec-6 | cID:65d53200-c0a844bd-5a6dbab7-acfc9f6e] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /polarion/checklist/api/api/templates/types
2025-08-01 21:32:12,835 [ajp-nio-127.0.0.1-8889-exec-7 | cID:65d5469d-c0a844bd-5a6dbab7-87237766] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /polarion/checklist/api/api/templates
2025-08-01 21:32:12,835 [ajp-nio-127.0.0.1-8889-exec-8 | cID:65d5469d-c0a844bd-5a6dbab7-111ba54f] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /polarion/checklist/api/api/templates/types
2025-08-01 21:32:14,072 [ajp-nio-127.0.0.1-8889-exec-9 | cID:65d54b76-c0a844bd-5a6dbab7-4f25141f] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /polarion/checklist/api/api/templates
2025-08-01 21:32:17,203 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 24.106201171875
2025-08-01 21:32:27,201 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 20.5642578125
2025-08-01 21:32:37,201 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 17.64697265625
2025-08-01 21:32:47,205 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 15.0033203125
2025-08-01 21:32:57,204 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 12.775390625
2025-08-01 21:33:07,201 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 10.90283203125
2025-08-01 21:33:17,202 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 9.543310546875
2025-08-01 21:33:27,203 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 8.1455078125
2025-08-01 21:33:37,203 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 7.02958984375
2025-08-01 21:33:47,203 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 6.0705078125
2025-08-01 21:33:57,202 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 5.181982421875
2025-08-01 21:34:07,203 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 4.4541015625
2025-08-01 21:34:17,201 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 3.879443359375
2025-08-01 21:34:27,202 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 3.3431640625
2025-08-01 21:34:37,202 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 2.891259765625
2025-08-01 21:34:47,201 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 2.51748046875
2025-08-01 21:34:57,204 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 2.17587890625
2025-08-01 21:35:07,201 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.949560546875
2025-08-01 21:35:17,201 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.7359375
2025-08-01 21:35:27,197 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.56318359375
2025-08-01 21:35:37,201 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.369580078125
2025-08-01 21:35:47,197 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.236474609375
2025-08-01 21:35:57,202 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.10654296875
2025-08-01 21:36:07,201 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.013232421875
2025-08-01 21:36:15,116 [Thread-32] INFO  class com.polarion.alm.server.util.ChartExporterStartup - Chart renderer was started successfully.
2025-08-01 21:36:18,310 [Thread-38] INFO  class com.polarion.alm.server.util.FormulaServerStartup - Formula renderer was started successfully.
2025-08-01 21:37:02,074 [ajp-nio-127.0.0.1-8889-exec-1 | cID:65d9b013-c0a844bd-5a6dbab7-c43e00c5] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?_t=1754055421964': Total: 0.102 s, CPU [user: 0.0132 s, system: 0.0416 s], Allocated memory: 552.5 kB, transactions: 0
2025-08-01 21:41:17,872 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-01 21:41:17,884 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-01 21:41:17,891 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Fri Aug 01 21:41:17 CST 2025]
2025-08-01 21:41:17,905 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-01 21:41:17,905 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,425.36 GB
 [Fri Aug 01 21:41:17 CST 2025]
2025-08-01 21:41:18,084 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-01 21:41:18,084 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Fri Aug 01 21:41:18 CST 2025]
2025-08-01 21:41:18,087 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-01 21:41:18,087 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 18; Time: 1.244 s.
 [Fri Aug 01 21:41:18 CST 2025]
2025-08-01 21:41:18,089 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-01 21:41:18,090 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Fri Aug 01 21:41:18 CST 2025]
2025-08-01 21:41:18,200 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-01 21:41:18,201 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Fri Aug 01 21:41:18 CST 2025]
2025-08-01 21:41:18,326 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-01 21:51:17,872 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-01 21:51:17,875 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-01 21:51:17,876 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Fri Aug 01 21:51:17 CST 2025]
2025-08-01 21:51:17,886 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-01 21:51:17,887 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,420.26 GB
 [Fri Aug 01 21:51:17 CST 2025]
2025-08-01 21:51:18,045 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-01 21:51:18,045 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Fri Aug 01 21:51:18 CST 2025]
2025-08-01 21:51:18,046 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-01 21:51:18,046 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 18; Time: 1.244 s.
 [Fri Aug 01 21:51:18 CST 2025]
2025-08-01 21:51:18,048 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-01 21:51:18,048 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Fri Aug 01 21:51:18 CST 2025]
2025-08-01 21:51:18,147 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-01 21:51:18,147 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Fri Aug 01 21:51:18 CST 2025]
2025-08-01 21:51:18,269 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-01 22:01:17,872 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-01 22:01:17,873 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-01 22:01:17,873 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Fri Aug 01 22:01:17 CST 2025]
2025-08-01 22:01:17,882 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-01 22:01:17,883 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,420.86 GB
 [Fri Aug 01 22:01:17 CST 2025]
2025-08-01 22:01:18,052 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-01 22:01:18,052 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Fri Aug 01 22:01:18 CST 2025]
2025-08-01 22:01:18,054 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-01 22:01:18,054 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 18; Time: 1.244 s.
 [Fri Aug 01 22:01:18 CST 2025]
2025-08-01 22:01:18,056 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-01 22:01:18,056 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Fri Aug 01 22:01:18 CST 2025]
2025-08-01 22:01:18,153 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-01 22:01:18,153 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Fri Aug 01 22:01:18 CST 2025]
2025-08-01 22:01:18,272 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-01 22:11:17,873 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-01 22:11:17,874 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-01 22:11:17,874 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Fri Aug 01 22:11:17 CST 2025]
2025-08-01 22:11:17,883 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-01 22:11:17,884 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,420.84 GB
 [Fri Aug 01 22:11:17 CST 2025]
2025-08-01 22:11:18,062 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-01 22:11:18,062 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Fri Aug 01 22:11:18 CST 2025]
2025-08-01 22:11:18,064 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-01 22:11:18,064 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 18; Time: 1.244 s.
 [Fri Aug 01 22:11:18 CST 2025]
2025-08-01 22:11:18,065 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-01 22:11:18,066 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Fri Aug 01 22:11:18 CST 2025]
2025-08-01 22:11:18,167 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-01 22:11:18,167 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Fri Aug 01 22:11:18 CST 2025]
2025-08-01 22:11:18,287 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-01 22:21:18,098 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-01 22:21:18,099 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-01 22:21:18,099 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Fri Aug 01 22:21:18 CST 2025]
2025-08-01 22:21:18,107 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-01 22:21:18,107 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,420.78 GB
 [Fri Aug 01 22:21:18 CST 2025]
2025-08-01 22:21:18,271 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-01 22:21:18,271 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Fri Aug 01 22:21:18 CST 2025]
2025-08-01 22:21:18,272 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-01 22:21:18,272 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 19; Time: 1.278 s.
 [Fri Aug 01 22:21:18 CST 2025]
2025-08-01 22:21:18,274 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-01 22:21:18,274 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Fri Aug 01 22:21:18 CST 2025]
2025-08-01 22:21:18,370 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-01 22:21:18,370 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Fri Aug 01 22:21:18 CST 2025]
2025-08-01 22:21:18,491 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-01 22:23:16,977 [ajp-nio-127.0.0.1-8889-exec-2 | cID:660407c2-c0a844bd-5a6dbab7-d4c919db] DEBUG com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory - Creating SingletonProxy for service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory
2025-08-01 22:23:16,978 [ajp-nio-127.0.0.1-8889-exec-2 | cID:660407c2-c0a844bd-5a6dbab7-d4c919db] DEBUG com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory - Constructing core service implementation for service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory
2025-08-01 22:23:16,981 [ajp-nio-127.0.0.1-8889-exec-2 | cID:660407c2-c0a844bd-5a6dbab7-d4c919db] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost] - Exception Processing /polarion/
org.apache.hivemind.ApplicationRuntimeException: Unable to construct service com.polarion.platform.authenticatorProviderManager: Unable to construct service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:163) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $IAuthenticatorManager_19865d3e1ea._service($IAuthenticatorManager_19865d3e1ea.java) ~[?:?]
	at $IAuthenticatorManager_19865d3e1ea.getAuthenticators($IAuthenticatorManager_19865d3e1ea.java) ~[?:?]
	at $IAuthenticatorManager_19865d3e1e9.getAuthenticators($IAuthenticatorManager_19865d3e1e9.java) ~[?:?]
	at com.polarion.platform.security.auth.impl.ContributedPolarionAuthenticator.isExpectedCallback(ContributedPolarionAuthenticator.java:33) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.authenticateImpl(PolarionAuthenticator.java:127) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.doAuthenticate(PolarionAuthenticator.java:105) ~[platform.jar:?]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:624) ~[catalina.jar:9.0.53]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invokeInternal(PolarionAuthenticator.java:248) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invoke(PolarionAuthenticator.java:242) ~[platform.jar:?]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: org.apache.hivemind.ApplicationRuntimeException: Unable to construct service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:163) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19865d3e1f8._service($ServiceInterceptorFactory_19865d3e1f8.java) ~[?:?]
	at $ServiceInterceptorFactory_19865d3e1f8.createInterceptor($ServiceInterceptorFactory_19865d3e1f8.java) ~[?:?]
	at $ServiceInterceptorFactory_19865d3e1f7.createInterceptor($ServiceInterceptorFactory_19865d3e1f7.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:66) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19865d3e1f8._service($ServiceInterceptorFactory_19865d3e1f8.java) ~[?:?]
	at $ServiceInterceptorFactory_19865d3e1f8.createInterceptor($ServiceInterceptorFactory_19865d3e1f8.java) ~[?:?]
	at $ServiceInterceptorFactory_19865d3e1f7.createInterceptor($ServiceInterceptorFactory_19865d3e1f7.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.createService(BuilderFactoryLogic.java:87) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactory.createCoreServiceImplementation(BuilderFactory.java:42) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:62) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19865d3e1f8._service($ServiceInterceptorFactory_19865d3e1f8.java) ~[?:?]
	at $ServiceInterceptorFactory_19865d3e1f8.createInterceptor($ServiceInterceptorFactory_19865d3e1f8.java) ~[?:?]
	at $ServiceInterceptorFactory_19865d3e1f7.createInterceptor($ServiceInterceptorFactory_19865d3e1f7.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.ModuleImpl.findTypeInClassResolver(ModuleImpl.java:219) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.ModuleImpl.resolveType(ModuleImpl.java:203) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.instantiateCoreServiceInstance(BuilderFactoryLogic.java:100) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.createService(BuilderFactoryLogic.java:75) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactory.createCoreServiceImplementation(BuilderFactory.java:42) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:62) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19865d3e1f8._service($ServiceInterceptorFactory_19865d3e1f8.java) ~[?:?]
	at $ServiceInterceptorFactory_19865d3e1f8.createInterceptor($ServiceInterceptorFactory_19865d3e1f8.java) ~[?:?]
	at $ServiceInterceptorFactory_19865d3e1f7.createInterceptor($ServiceInterceptorFactory_19865d3e1f7.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
2025-08-01 22:23:18,777 [ajp-nio-127.0.0.1-8889-exec-1 | cID:66040ef7-c0a844bd-5a6dbab7-722db387] DEBUG com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory - Constructing core service implementation for service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory
2025-08-01 22:23:18,778 [ajp-nio-127.0.0.1-8889-exec-1 | cID:66040ef7-c0a844bd-5a6dbab7-722db387] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost] - Exception Processing /polarion/
org.apache.hivemind.ApplicationRuntimeException: Unable to construct service com.polarion.platform.authenticatorProviderManager: Unable to construct service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:163) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $IAuthenticatorManager_19865d3e1ea._service($IAuthenticatorManager_19865d3e1ea.java) ~[?:?]
	at $IAuthenticatorManager_19865d3e1ea.getAuthenticators($IAuthenticatorManager_19865d3e1ea.java) ~[?:?]
	at $IAuthenticatorManager_19865d3e1e9.getAuthenticators($IAuthenticatorManager_19865d3e1e9.java) ~[?:?]
	at com.polarion.platform.security.auth.impl.ContributedPolarionAuthenticator.isExpectedCallback(ContributedPolarionAuthenticator.java:33) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.authenticateImpl(PolarionAuthenticator.java:127) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.doAuthenticate(PolarionAuthenticator.java:105) ~[platform.jar:?]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:624) ~[catalina.jar:9.0.53]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invokeInternal(PolarionAuthenticator.java:248) ~[platform.jar:?]
	at com.polarion.platform.security.auth.PolarionAuthenticator.invoke(PolarionAuthenticator.java:242) ~[platform.jar:?]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
Caused by: org.apache.hivemind.ApplicationRuntimeException: Unable to construct service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:163) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19865d3e1f8._service($ServiceInterceptorFactory_19865d3e1f8.java) ~[?:?]
	at $ServiceInterceptorFactory_19865d3e1f8.createInterceptor($ServiceInterceptorFactory_19865d3e1f8.java) ~[?:?]
	at $ServiceInterceptorFactory_19865d3e1f7.createInterceptor($ServiceInterceptorFactory_19865d3e1f7.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:66) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19865d3e1f8._service($ServiceInterceptorFactory_19865d3e1f8.java) ~[?:?]
	at $ServiceInterceptorFactory_19865d3e1f8.createInterceptor($ServiceInterceptorFactory_19865d3e1f8.java) ~[?:?]
	at $ServiceInterceptorFactory_19865d3e1f7.createInterceptor($ServiceInterceptorFactory_19865d3e1f7.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Error building service com.fasnote.alm.auth.feishu.feishuAuthenticatorManagerInterceptorFactory: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.createService(BuilderFactoryLogic.java:87) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactory.createCoreServiceImplementation(BuilderFactory.java:42) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:62) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19865d3e1f8._service($ServiceInterceptorFactory_19865d3e1f8.java) ~[?:?]
	at $ServiceInterceptorFactory_19865d3e1f8.createInterceptor($ServiceInterceptorFactory_19865d3e1f8.java) ~[?:?]
	at $ServiceInterceptorFactory_19865d3e1f7.createInterceptor($ServiceInterceptorFactory_19865d3e1f7.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
Caused by: org.apache.hivemind.ApplicationRuntimeException: Unable to convert type 'com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory' to a Java class, either as is, or in package com.fasnote.alm.auth.feishu.
	at org.apache.hivemind.impl.ModuleImpl.findTypeInClassResolver(ModuleImpl.java:219) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.ModuleImpl.resolveType(ModuleImpl.java:203) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.instantiateCoreServiceInstance(BuilderFactoryLogic.java:100) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactoryLogic.createService(BuilderFactoryLogic.java:75) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.service.impl.BuilderFactory.createCoreServiceImplementation(BuilderFactory.java:42) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InvokeFactoryServiceConstructor.constructCoreServiceImplementation(InvokeFactoryServiceConstructor.java:62) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructCoreServiceImplementation(AbstractServiceModelImpl.java:108) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:157) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructServiceImplementation(AbstractServiceModelImpl.java:141) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.SingletonServiceModel.getActualServiceImplementation(SingletonServiceModel.java:69) ~[hivemind-1.1.1.jar:?]
	at $ServiceInterceptorFactory_19865d3e1f8._service($ServiceInterceptorFactory_19865d3e1f8.java) ~[?:?]
	at $ServiceInterceptorFactory_19865d3e1f8.createInterceptor($ServiceInterceptorFactory_19865d3e1f8.java) ~[?:?]
	at $ServiceInterceptorFactory_19865d3e1f7.createInterceptor($ServiceInterceptorFactory_19865d3e1f7.java) ~[?:?]
	at org.apache.hivemind.impl.ServiceInterceptorContributionImpl.createInterceptor(ServiceInterceptorContributionImpl.java:95) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.InterceptorStackImpl.process(InterceptorStackImpl.java:116) ~[hivemind-1.1.1.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.addInterceptors(AbstractServiceModelImpl.java:85) ~[polarion-hivemind.jar:?]
	at org.apache.hivemind.impl.servicemodel.AbstractServiceModelImpl.constructNewServiceImplementation(AbstractServiceModelImpl.java:159) ~[polarion-hivemind.jar:?]
	... 26 more
