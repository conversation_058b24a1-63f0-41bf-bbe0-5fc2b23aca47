2025-08-01 21:30:37,206 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-01 21:30:37,207 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-01 21:30:37,208 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-01 21:30:37,211 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-01 21:30:37,211 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-01 21:30:37,212 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 21:30:37,212 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-01 21:30:51,428 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-01 21:30:51,970 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.542 s. ]
2025-08-01 21:30:51,970 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-01 21:30:52,217 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.247 s. ]
2025-08-01 21:30:52,486 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-01 21:30:52,983 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 15 s. ]
2025-08-01 21:30:53,790 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 16.6 s. ]
2025-08-01 21:30:54,294 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 21:30:54,295 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-01 21:30:54,356 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.57 s. ]
2025-08-01 21:30:54,357 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 21:30:54,357 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-01 21:30:54,469 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-08-01 21:30:54,471 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-08-01 21:30:54,470 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-08-01 21:30:54,473 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-08-01 21:30:54,470 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-08-01 21:30:54,485 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-01 21:30:54,610 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-01 21:30:55,271 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-01 21:30:55,685 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-01 21:30:57,090 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 2.73 s. ]
2025-08-01 21:30:57,233 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 21:30:57,235 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-01 21:30:58,665 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-01 21:30:58,720 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 1.63 s. ]
2025-08-01 21:30:58,942 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 21:30:58,943 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-01 21:30:58,973 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-01 21:30:59,153 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-01 21:30:59,298 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-01 21:30:59,418 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-01 21:30:59,538 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-01 21:30:59,631 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-01 21:30:59,770 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-01 21:30:59,921 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-01 21:31:00,064 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-01 21:31:00,065 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 1.34 s. ]
2025-08-01 21:31:00,065 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 21:31:00,065 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-01 21:31:00,128 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.06 s. ]
2025-08-01 21:31:00,197 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 21:31:00,198 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-01 21:31:01,040 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 11 objects in index: BuildArtifact
2025-08-01 21:31:01,171 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 11 from 11 objects were refreshed (100%)
2025-08-01 21:31:01,903 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-01 21:31:01,905 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-01 21:31:02,325 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 2.2 s. ]
2025-08-01 21:31:02,329 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 21:31:02,329 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-01 21:31:02,375 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.05 s. ]
2025-08-01 21:31:02,375 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-01 21:31:02,375 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-01 21:31:18,038 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 15.66 s. ]
2025-08-01 21:31:18,042 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-01 21:31:18,042 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 40.8 s. ]
2025-08-01 21:31:18,042 [main] INFO  com.polarion.platform.startup - ****************************************************************
