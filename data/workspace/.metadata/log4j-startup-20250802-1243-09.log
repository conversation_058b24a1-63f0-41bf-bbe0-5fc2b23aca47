2025-08-02 12:43:09,477 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 12:43:09,477 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-02 12:43:09,477 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 12:43:09,477 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-02 12:43:09,478 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-02 12:43:09,478 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:43:09,478 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-02 12:43:13,601 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-02 12:43:13,734 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.133 s. ]
2025-08-02 12:43:13,734 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-02 12:43:13,791 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0566 s. ]
2025-08-02 12:43:13,867 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-02 12:43:14,021 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 15 s. ]
2025-08-02 12:43:14,250 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.78 s. ]
2025-08-02 12:43:14,332 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:43:14,332 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-02 12:43:14,367 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-08-02 12:43:14,367 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:43:14,367 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-02 12:43:14,376 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-08-02 12:43:14,376 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-08-02 12:43:14,376 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-08-02 12:43:14,376 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-08-02 12:43:14,376 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-08-02 12:43:14,377 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-08-02 12:43:14,388 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-02 12:43:14,518 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-02 12:43:14,633 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-02 12:43:15,151 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.78 s. ]
2025-08-02 12:43:15,162 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:43:15,162 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-02 12:43:15,363 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-02 12:43:15,375 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-08-02 12:43:15,399 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:43:15,399 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-02 12:43:15,403 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-02 12:43:15,460 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-02 12:43:15,511 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-02 12:43:15,553 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-02 12:43:15,577 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-02 12:43:15,601 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-02 12:43:15,639 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-02 12:43:15,680 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-02 12:43:15,714 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-02 12:43:15,714 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.34 s. ]
2025-08-02 12:43:15,714 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:43:15,714 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-02 12:43:15,728 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-02 12:43:15,728 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:43:15,728 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-02 12:43:15,825 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-02 12:43:15,827 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-02 12:43:15,983 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.25 s. ]
2025-08-02 12:43:15,983 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:43:15,983 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-02 12:43:15,990 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-02 12:43:15,990 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 12:43:15,990 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-02 12:43:18,624 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.63 s. ]
2025-08-02 12:43:18,624 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 12:43:18,624 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.15 s. ]
2025-08-02 12:43:18,624 [main] INFO  com.polarion.platform.startup - ****************************************************************
