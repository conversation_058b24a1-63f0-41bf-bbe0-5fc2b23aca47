2025-08-02 17:21:24,928 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using logging context STANDALONE
2025-08-02 17:21:24,929 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Launchers manager started...
2025-08-02 17:21:24,929 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using home directory /opt/polarion/polarion
2025-08-02 17:21:24,929 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using root directory /opt/polarion
2025-08-02 17:21:24,930 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using workspace directory /opt/polarion/data/workspace
2025-08-02 17:21:24,930 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using config directory /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-08-02 17:21:24,930 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading external properties ...
2025-08-02 17:21:24,930 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Using external property file /opt/polarion/etc/polarion.properties
2025-08-02 17:21:24,930 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Loading internal properties ...
2025-08-02 17:21:24,932 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Host: zhangwentiandeMac-mini-2.local (127.0.0.1)
2025-08-02 17:21:24,935 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Product: com.polarion.alm
2025-08-02 17:21:24,935 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Version: 3.22.1
2025-08-02 17:21:24,935 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Build: 20220419-1528-22_R1-be3adceb
2025-08-02 17:21:24,935 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/fasnote
2025-08-02 17:21:24,936 [main] WARN  com.polarion.core.boot.impl.AppLaunchersManager - missing subfolder 'eclipse' under extension directory: /opt/polarion/polarion/extensions/2404
2025-08-02 17:21:24,936 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Extensions: [exts]
2025-08-02 17:21:24,939 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace location: /opt/polarion/data/workspace
2025-08-02 17:21:24,939 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Workspace lock acquired
2025-08-02 17:21:24,940 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Found applications: [polarion.server, polarion.coordinator, polarion.rt]
2025-08-02 17:21:24,940 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Starting application: polarion.server
2025-08-02 17:21:24,946 [main] INFO  com.polarion.core.boot.impl.AppLaunchersManager - Application extension successfully read
2025-08-02 17:21:24,952 [main] INFO  com.polarion.platform.internal.SystemStatistics - Initializing monitoring, isThreadCpuTimeSupported: true, isThreadContentionMonitoringSupported: true, isThreadAllocatedMemorySupported: true
2025-08-02 17:21:24,952 [main] INFO  com.polarion.platform.internal.SystemStatistics - State before enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-08-02 17:21:24,953 [main] INFO  com.polarion.platform.internal.SystemStatistics - State after enabling: isThreadCpuTimeEnabled: true, isThreadContentionMonitoringEnabled: false, isThreadAllocatedMemoryEnabled: true
2025-08-02 17:21:24,956 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 17:21:24,956 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-02 17:21:24,956 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 17:21:24,957 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-02 17:21:24,957 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-02 17:21:24,957 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 17:21:24,957 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-02 17:21:24,958 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** Java system properties listing: 
2025-08-02 17:21:24,981 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminPasswd = admin
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - adminUser = admin
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.auth = false
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.host = 
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.password = **PASSWORD**HIDDEN**
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.port = 25
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - announcer.smtp.user = 
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - awt.toolkit = sun.lwawt.macosx.LWCToolkit
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - base.url = http://localhost
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - bfh.jobs.workdir = /opt/polarion/data/workspace/polarion-data/jobs
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - BIRDir = /opt/polarion/data/BIR
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - calculated.fields.mode = async
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.activationHelpLink = https://polarion.plm.automation.siemens.com/getlicense
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.activation.server = https://license.polarion.com/licenseGenerator/generator/generate
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.enabled = false
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.alm.ui.gravatar.url = http://www.gravatar.com/avatar/$emailHash$?d=identicon&s=50
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.application = polarion.server
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.config = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.data = /opt/polarion/data
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.eclipse = /opt/polarion/polarion
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.home = /opt/polarion/polarion
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.licenseDir = /opt/polarion/polarion/license
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.internalPG = polarion:polarion@localhost:5434
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.disabled = true
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.receivers = 
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.sender = 
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.monitoring.notifications.subject.prefix = 
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.platform.persistence.notifications.disabled = true
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.propertyFile = /opt/polarion/etc/polarion.properties
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.root = /opt/polarion
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.polarion.workspace = /opt/polarion/data/workspace
2025-08-02 17:21:24,982 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.collaborationNotifications.enabled = true
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.document.listStyle = 1ai
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.loggingContext = STANDALONE
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.preview.thumbnailsDataDir = /opt/polarion/data/workspace/previews-data/thumbnails
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.cors.allowedOrigins = *
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.enabled = true
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.rest.swaggerUi.enabled = true
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.accEndpointUrl = https://acc.collab.sws.siemens.com
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.baseDomain = sws.siemens.com
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - com.siemens.polarion.xcelerator.shareEndpointUrl = https://share.sws.siemens.com
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - content.types.user.table = /opt/polarion/polarion/plugins/com.polarion.core.boot_3.22.1/content-types.properties
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlHostname = localhost
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - controlPort = 8887
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.location = Sandbox/
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.default.useUserId = true
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - createproject.limitedAccessMessage = You may create a project in the Sandbox project group (only). Please fill in the required properties below. For example:<br/><table><tr><td>Location:</td><td>Sandbox/MyFirstProject</td></tr><tr><td>ID:</td><td>MyFirstProject</td></tr></table><br/>Or use the suggested defaults.
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug = false
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.license.validation = true
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.machine.code.generation = true
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - debug.security.validation = true
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.ALM = alm_vmodel
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Pro = alm_vmodel
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.QA = qa_vmodel
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.Requirements = req_vmodel
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XBase = alm_vmodel
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XEnterprise = alm_vmodel
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - default.project.template.XPro = alm_vmodel
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - derby.system.home = /opt/polarion/data/logs/derby
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.application = com.polarion.core.boot.app
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.commands = -application
com.polarion.core.boot.app
-data
/opt/polarion/data/workspace
-configuration
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
-dev
file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
-os
linux
-ws
linux
-arch
arm64
-appId
polarion.server

2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.home.location = file:/opt/polarion/polarion/plugins/
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.p2.data.area = @config.dir/.p2
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.pde.launch = true
2025-08-02 17:21:24,983 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.startTime = *************
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - eclipse.stateSaveDelayInterval = 30000
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - enableCreateAccountForm = false
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - equinox.init.uuid = true
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - error.report.email = 
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.encoding = UTF-8
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - file.separator = /
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ftp.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gopherProxySet = false
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - gosh.args = --nointeractive
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - htpasswd.path = htpasswd
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - http.nonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - index.activities = /opt/polarion/data/workspace/polarion-data/index
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.graphicsenv = sun.awt.CGraphicsEnvironment
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.headless = true
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.awt.printerjob = sun.lwawt.macosx.CPrinterJob
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.path = /opt/polarion/polarion/plugins/org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.class.version = 55.0
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.home = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.io.tmpdir = /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.library.path = /Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.name = OpenJDK Runtime Environment
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.runtime.version = 11.0.27+6-LTS
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.security.policy = /opt/polarion/polarion/policy
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.maintenance.version = 3
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.name = Java Platform API Specification
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.vendor = Oracle Corporation
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.specification.version = 11
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor = Microsoft
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url = https://www.microsoft.com
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.url.bug = https://github.com/microsoft/openjdk/issues
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vendor.version = Microsoft-11367290
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version = 11.0.27
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.version.date = 2025-04-15
2025-08-02 17:21:24,984 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.compressedOopsMode = Zero based
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.info = mixed mode
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.name = OpenJDK 64-Bit Server VM
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.name = Java Virtual Machine Specification
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.vendor = Oracle Corporation
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.specification.version = 11
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.vendor = Microsoft
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - java.vm.version = 11.0.27+6-LTS
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - javasvn.timeout = 10000
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - jdk.debug = release
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - ldap.bind.password = **PASSWORD**HIDDEN**
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.audit.enabled = true
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.auto.scan.enabled = true
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.size = 100
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.cache.ttl = 1800
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.check.interval = 0
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.expired = true
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.allow.local.files = true
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.features = all
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.max.users = 10
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.default.plugin.id = com.fasnote.alm.plugin.manage
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.mode = true
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.show.machine.code = true
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.machine.binding = true
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.development.skip.network.validation = true
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.directory = dev-licenses
2025-08-02 17:21:24,985 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.encryption.enabled = false
2025-08-02 17:21:24,986 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.hot.reload.enabled = true
2025-08-02 17:21:24,986 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.log.level = DEBUG
2025-08-02 17:21:24,986 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.machine.binding.enabled = false
2025-08-02 17:21:24,986 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.max.plugins = 1000
2025-08-02 17:21:24,986 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.scan.interval = 60
2025-08-02 17:21:24,986 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.signature.validation.enabled = false
2025-08-02 17:21:24,986 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - license.validation.timeout = 1000
2025-08-02 17:21:24,986 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - licenseForNewUserAccount = 
2025-08-02 17:21:24,986 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - line.separator = 

2025-08-02 17:21:24,986 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.contextSelector = org.apache.logging.log4j.core.selector.BasicContextSelector
2025-08-02 17:21:24,986 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - log4j2.loggerContextFactory = org.apache.logging.log4j.core.impl.Log4jContextFactory
2025-08-02 17:21:24,986 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - logDir = /opt/polarion/data/workspace/.metadata/
2025-08-02 17:21:24,986 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - login = polarion
2025-08-02 17:21:24,986 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - mavenConfigDir = /opt/polarion/polarion/../maven
2025-08-02 17:21:24,986 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - minimalPasswordLength = **PASSWORD**HIDDEN**
2025-08-02 17:21:24,986 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.equinox.simpleconfigurator.configUrl = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/org.eclipse.equinox.simpleconfigurator/bundles.info
2025-08-02 17:21:24,986 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.eclipse.lyo.oslc4j.strictDatatypes = false
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.executionenvironment = OSGi/Minimum-1.0, OSGi/Minimum-1.1, OSGi/Minimum-1.2, JavaSE/compact1-1.8, JavaSE/compact2-1.8, JavaSE/compact3-1.8, JRE-1.1, J2SE-1.2, J2SE-1.3, J2SE-1.4, J2SE-1.5, JavaSE-1.6, JavaSE-1.7, JavaSE-1.8, JavaSE-9, JavaSE-10, JavaSE-11
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.language = zh
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.name = MacOSX
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.os.version = 15.5.0
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.processor = aarch64
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.storage = /Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.capabilities = osgi.ee; osgi.ee="OSGi/Minimum"; version:List<Version>="1.0, 1.1, 1.2", osgi.ee; osgi.ee="JRE"; version:List<Version>="1.0, 1.1", osgi.ee; osgi.ee="JavaSE"; version:List<Version>="1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact1"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact2"; version:List<Version>="1.8, 9.0, 10.0, 11.0",osgi.ee; osgi.ee="JavaSE/compact3"; version:List<Version>="1.8, 9.0, 10.0, 11.0"
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.system.packages = com.sun.jarsigner, com.sun.java.accessibility.util, com.sun.javadoc, com.sun.jdi, com.sun.jdi.connect, com.sun.jdi.connect.spi, com.sun.jdi.event, com.sun.jdi.request, com.sun.jndi.ldap.spi, com.sun.management, com.sun.net.httpserver, com.sun.net.httpserver.spi, com.sun.nio.file, com.sun.nio.sctp, com.sun.security.auth, com.sun.security.auth.callback, com.sun.security.auth.login, com.sun.security.auth.module, com.sun.security.jgss, com.sun.source.doctree, com.sun.source.tree, com.sun.source.util, com.sun.tools.attach, com.sun.tools.attach.spi, com.sun.tools.javac, com.sun.tools.javadoc, com.sun.tools.jconsole, java.applet, java.awt, java.awt.color, java.awt.datatransfer, java.awt.desktop, java.awt.dnd, java.awt.event, java.awt.font, java.awt.geom, java.awt.im, java.awt.im.spi, java.awt.image, java.awt.image.renderable, java.awt.print, java.beans, java.beans.beancontext, java.io, java.lang, java.lang.annotation, java.lang.instrument, java.lang.invoke, java.lang.management, java.lang.module, java.lang.ref, java.lang.reflect, java.math, java.net, java.net.http, java.net.spi, java.nio, java.nio.channels, java.nio.channels.spi, java.nio.charset, java.nio.charset.spi, java.nio.file, java.nio.file.attribute, java.nio.file.spi, java.rmi, java.rmi.activation, java.rmi.dgc, java.rmi.registry, java.rmi.server, java.security, java.security.acl, java.security.cert, java.security.interfaces, java.security.spec, java.sql, java.text, java.text.spi, java.time, java.time.chrono, java.time.format, java.time.temporal, java.time.zone, java.util, java.util.concurrent, java.util.concurrent.atomic, java.util.concurrent.locks, java.util.function, java.util.jar, java.util.logging, java.util.prefs, java.util.regex, java.util.spi, java.util.stream, java.util.zip, javax.accessibility, javax.annotation.processing, javax.crypto, javax.crypto.interfaces, javax.crypto.spec, javax.imageio, javax.imageio.event, javax.imageio.metadata, javax.imageio.plugins.bmp, javax.imageio.plugins.jpeg, javax.imageio.plugins.tiff, javax.imageio.spi, javax.imageio.stream, javax.lang.model, javax.lang.model.element, javax.lang.model.type, javax.lang.model.util, javax.management, javax.management.loading, javax.management.modelmbean, javax.management.monitor, javax.management.openmbean, javax.management.relation, javax.management.remote, javax.management.remote.rmi, javax.management.timer, javax.naming, javax.naming.directory, javax.naming.event, javax.naming.ldap, javax.naming.spi, javax.net, javax.net.ssl, javax.print, javax.print.attribute, javax.print.attribute.standard, javax.print.event, javax.rmi.ssl, javax.script, javax.security.auth, javax.security.auth.callback, javax.security.auth.kerberos, javax.security.auth.login, javax.security.auth.spi, javax.security.auth.x500, javax.security.cert, javax.security.sasl, javax.smartcardio, javax.sound.midi, javax.sound.midi.spi, javax.sound.sampled, javax.sound.sampled.spi, javax.sql, javax.sql.rowset, javax.sql.rowset.serial, javax.sql.rowset.spi, javax.swing, javax.swing.border, javax.swing.colorchooser, javax.swing.event, javax.swing.filechooser, javax.swing.plaf, javax.swing.plaf.basic, javax.swing.plaf.metal, javax.swing.plaf.multi, javax.swing.plaf.nimbus, javax.swing.plaf.synth, javax.swing.table, javax.swing.text, javax.swing.text.html, javax.swing.text.html.parser, javax.swing.text.rtf, javax.swing.tree, javax.swing.undo, javax.tools, javax.transaction.xa, javax.xml, javax.xml.catalog, javax.xml.crypto, javax.xml.crypto.dom, javax.xml.crypto.dsig, javax.xml.crypto.dsig.dom, javax.xml.crypto.dsig.keyinfo, javax.xml.crypto.dsig.spec, javax.xml.datatype, javax.xml.namespace, javax.xml.parsers, javax.xml.stream, javax.xml.stream.events, javax.xml.stream.util, javax.xml.transform, javax.xml.transform.dom, javax.xml.transform.sax, javax.xml.transform.stax, javax.xml.transform.stream, javax.xml.validation, javax.xml.xpath, jdk.dynalink, jdk.dynalink.beans, jdk.dynalink.linker, jdk.dynalink.linker.support, jdk.dynalink.support, jdk.javadoc.doclet, jdk.jfr, jdk.jfr.consumer, jdk.jshell, jdk.jshell.execution, jdk.jshell.spi, jdk.jshell.tool, jdk.management.jfr, jdk.nashorn.api.scripting, jdk.nashorn.api.tree, jdk.net, jdk.nio, jdk.security.jarsigner, jdk.swing.interop, netscape.javascript, org.ietf.jgss, org.w3c.dom, org.w3c.dom.bootstrap, org.w3c.dom.css, org.w3c.dom.events, org.w3c.dom.html, org.w3c.dom.ls, org.w3c.dom.ranges, org.w3c.dom.stylesheets, org.w3c.dom.traversal, org.w3c.dom.views, org.w3c.dom.xpath, org.xml.sax, org.xml.sax.ext, org.xml.sax.helpers, sun.misc, sun.reflect
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.uuid = 2670c275-f579-4560-9a39-789ccaca4924
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.vendor = Eclipse
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.framework.version = 1.9.0
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.extension = true
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.fragment = true
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.osgi.supports.framework.requirebundle = true
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.client.readbuffer.usedirect = true
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - org.xsocket.connection.server.readbuffer.usedirect = true
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.arch = aarch64
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.name = Mac OS X
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - os.version = 15.5
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.arch = arm64
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles = reference:file:/opt/polarion/polarion/plugins/org.eclipse.equinox.simpleconfigurator_1.3.0.v20180502-1828.jar@1:start
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.bundles.defaultStartLevel = 4
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.checkConfiguration = true
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation = true
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.compatibility.bootdelegation.default = true
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.area = file:/Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.configuration.cascaded = false
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.dev = file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework = file:/opt/polarion/polarion/plugins/org.eclipse.osgi_3.13.0.v20180409-1500.jar
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.shape = jar
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.framework.useSystemProperties = true
2025-08-02 17:21:24,987 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.install.area = file:/opt/polarion/polarion/plugins/
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.instance.area = file:/opt/polarion/data/workspace/
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.logfile = /opt/polarion/data/workspace/.metadata/.log
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.nl = zh_CN_#Hans
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.os = linux
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.syspath = /opt/polarion/polarion/plugins
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.tracefile = /opt/polarion/data/workspace/.metadata/trace.log
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - osgi.ws = linux
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - password = **PASSWORD**HIDDEN**
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - path.separator = :
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfbox.fontcache = /opt/polarion/data/workspace/polarion-data
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - pdfexport.config = /opt/polarion/polarion/configuration/pdfexport.xml
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.id = polarion-shared
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.default.deploy.repository.url = file:///opt/polarion/data/shared-maven-repo
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.build.maven.location.maven2 = /opt/polarion/polarion/../maven/distribution
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.size = 100
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.global.doc.cache.with.history = false
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - polarion.tx.doc.cache.size = 100
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.alm = https://polarion.plm.automation.siemens.com/products/polarion-alm
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.qa = https://polarion.plm.automation.siemens.com/products/polarion-qa
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - productLink.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/polarion-requirements
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - repo = http://localhost/repo
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - rolesForNewUserAccount = user
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - RRDir = /opt/polarion/data/RR
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - SDKDir = /opt/polarion/polarion/SDK
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - secure.approvals = false
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - shutdownCatchPhrase = shutdown
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - simple.profiler.enabled = false
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - skip.data.preloading = false
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - socksNonProxyHosts = 127.0.0.1|***********/16|*.***********/16|10.0.0.0/8|*.10.0.0.0/8|**********/12|*.**********/12|**********/16|*.**********/16|localhost|*.localhost|local|*.local|crashlytics.com|*.crashlytics.com|<local>|*.<local>
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stderr.encoding = UTF-8
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - stdout.encoding = UTF-8
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - storeUrl.com.polarion.requirements = https://polarion.plm.automation.siemens.com/products/licensing?product=REQUIREMENTS
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.arch.data.model = 64
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.boot.library.path = /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/lib
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.endian = little
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.cpu.isalist = 
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.io.unicode.encoding = UnicodeBig
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.command = org.eclipse.equinox.launcher.Main -application com.polarion.core.boot.app -data /opt/polarion/data/workspace -configuration file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/ -dev file:///Users/<USER>/eclipse-workspace/.metadata/.plugins/org.eclipse.pde.core/Polarion/dev.properties -os linux -ws linux -arch arm64 -appId polarion.server
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.java.launcher = SUN_STANDARD
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.jnu.encoding = UTF-8
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.management.compiler = HotSpot 64-Bit Tiered Compilers
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - sun.os.patch.level = unknown
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.contact = https://polarion.plm.automation.siemens.com/techsupport/resources
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.license.email = <EMAIL>
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - support.sales.email = <EMAIL>
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.access.file = /opt/polarion/data/svn/access
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svn.passwd.file = /opt/polarion/data/svn/passwd
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.http.encoding = UTF-8
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - svnkit.library.gnome-keyring.enabled = false
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.ajp13-port = 8889
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - TomcatService.request.safeListedHosts = 0.0.0.0
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.country = CN
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.dir = /Applications/Eclipse JEE.app/Contents/MacOS
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.home = /Users/<USER>
2025-08-02 17:21:24,988 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.language = zh
2025-08-02 17:21:24,989 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.name = zhangwentian
2025-08-02 17:21:24,989 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.script = Hans
2025-08-02 17:21:24,989 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - user.timezone = Asia/Shanghai
2025-08-02 17:21:24,989 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - userAccountVault = /opt/polarion/data/workspace/user-account-vault
2025-08-02 17:21:24,989 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - workDir = /opt/polarion/data/workspace/polarion-data
2025-08-02 17:21:24,989 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - **** END of Java system properties
2025-08-02 17:21:24,991 [main] INFO  com.polarion.psvn.launcher.PolarionSVNApplication - XML parsers factory: com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl
2025-08-02 17:21:24,992 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Starting Platform...
2025-08-02 17:21:25,003 [main] INFO  PolarionLicensing - Searching for valid license file in /opt/polarion/polarion/license
2025-08-02 17:21:25,004 [main] INFO  PolarionLicensing - Trying to load license file polarion.lic
2025-08-02 17:21:25,006 [main] INFO  PolarionLicensing - The license file contains the following fields:
2025-08-02 17:21:25,006 [main] INFO  PolarionLicensing - *** License fields ***
2025-08-02 17:21:25,006 [main] INFO  PolarionLicensing - VariantsNamedUsers = 3
2025-08-02 17:21:25,006 [main] INFO  PolarionLicensing - almNamedUsers = 3
2025-08-02 17:21:25,006 [main] INFO  PolarionLicensing - dateCreated = 23.07.2025
2025-08-02 17:21:25,006 [main] INFO  PolarionLicensing - expirationDate = 21.08.2025
2025-08-02 17:21:25,006 [main] INFO  PolarionLicensing - hardwareKey = 8AG9-261C-1962
2025-08-02 17:21:25,006 [main] INFO  PolarionLicensing - licenseFormat = 2022
2025-08-02 17:21:25,006 [main] INFO  PolarionLicensing - licenseType = EVAL
2025-08-02 17:21:25,006 [main] INFO  PolarionLicensing - multiInstanceRunningInstances = 3
2025-08-02 17:21:25,006 [main] INFO  PolarionLicensing - userCompany = Polarion Eval
2025-08-02 17:21:25,006 [main] INFO  PolarionLicensing - *** License fields END ***
2025-08-02 17:21:25,022 [main] INFO  PolarionLicensing - Removing allocations by null
2025-08-02 17:21:25,023 [main] INFO  PolarionLicensing - STATS:concurrentVariantsUser,current:0,peak:0,limit:0
2025-08-02 17:21:25,023 [main] INFO  PolarionLicensing - 0 namedReviewerUser assignments (out of 0) loaded: []
2025-08-02 17:21:25,023 [main] INFO  PolarionLicensing - 0 concurrentReviewerUser assignments (out of 0) loaded: []
2025-08-02 17:21:25,023 [main] INFO  PolarionLicensing - STATS:concurrentReviewerUser,current:0,peak:0,limit:0
2025-08-02 17:21:25,023 [main] INFO  PolarionLicensing - 0 namedXBaseUser assignments (out of 0) loaded: []
2025-08-02 17:21:25,023 [main] INFO  PolarionLicensing - 0 concurrentXBaseUser assignments (out of 0) loaded: []
2025-08-02 17:21:25,023 [main] INFO  PolarionLicensing - STATS:concurrentXBaseUser,current:0,peak:0,limit:0
2025-08-02 17:21:25,023 [main] INFO  PolarionLicensing - 0 namedXProUser assignments (out of 0) loaded: []
2025-08-02 17:21:25,024 [main] INFO  PolarionLicensing - 0 concurrentXProUser assignments (out of 0) loaded: []
2025-08-02 17:21:25,024 [main] INFO  PolarionLicensing - STATS:concurrentXProUser,current:0,peak:0,limit:0
2025-08-02 17:21:25,024 [main] INFO  PolarionLicensing - 0 namedXEnterpriseUser assignments (out of 0) loaded: []
2025-08-02 17:21:25,024 [main] INFO  PolarionLicensing - 0 concurrentXEnterpriseUser assignments (out of 0) loaded: []
2025-08-02 17:21:25,024 [main] INFO  PolarionLicensing - STATS:concurrentXEnterpriseUser,current:0,peak:0,limit:0
2025-08-02 17:21:25,024 [main] INFO  PolarionLicensing - 0 namedProUser assignments (out of 0) loaded: []
2025-08-02 17:21:25,024 [main] INFO  PolarionLicensing - 0 concurrentProUser assignments (out of 0) loaded: []
2025-08-02 17:21:25,024 [main] INFO  PolarionLicensing - STATS:concurrentProUser,current:0,peak:0,limit:0
2025-08-02 17:21:25,024 [main] INFO  PolarionLicensing - 0 namedRequirementsUser assignments (out of 0) loaded: []
2025-08-02 17:21:25,024 [main] INFO  PolarionLicensing - 0 concurrentRequirementsUser assignments (out of 0) loaded: []
2025-08-02 17:21:25,024 [main] INFO  PolarionLicensing - STATS:concurrentRequirementsUser,current:0,peak:0,limit:0
2025-08-02 17:21:25,024 [main] INFO  PolarionLicensing - 0 namedQAUser assignments (out of 0) loaded: []
2025-08-02 17:21:25,024 [main] INFO  PolarionLicensing - 0 concurrentQAUser assignments (out of 0) loaded: []
2025-08-02 17:21:25,024 [main] INFO  PolarionLicensing - STATS:concurrentQAUser,current:0,peak:0,limit:0
2025-08-02 17:21:25,024 [main] INFO  PolarionLicensing - 3 namedALMUser assignments (out of 3) loaded: [admin, ou_d6f3139d36fb2978b33a8f870096b9e3, mTest]
2025-08-02 17:21:25,025 [main] INFO  PolarionLicensing - 0 concurrentALMUser assignments (out of 0) loaded: []
2025-08-02 17:21:25,025 [main] INFO  PolarionLicensing - STATS:concurrentALMUser,current:0,peak:0,limit:0
2025-08-02 17:21:25,025 [main] INFO  PolarionLicensing - 
*******************************************************************
 Polarion successfully activated
*******************************************************************
2025-08-02 17:21:25,083 [main] INFO  com.polarion.platform.internal.i18n.LanguageContributor - Localization file /META-INF/messages_en.properties read successfully (7789 messages)
2025-08-02 17:21:25,108 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Processing bundles:
2025-08-02 17:21:25,108 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [0] - org.eclipse.osgi
2025-08-02 17:21:25,108 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [1] - org.eclipse.equinox.simpleconfigurator
2025-08-02 17:21:25,108 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [2] - antlr
2025-08-02 17:21:25,109 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [3] - antlr4
2025-08-02 17:21:25,109 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [4] - antlr4-runtime
2025-08-02 17:21:25,109 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [5] - bcprov
2025-08-02 17:21:25,109 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [6] - com.auth0.java-jwt
2025-08-02 17:21:25,109 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [7] - com.fasnote.alm.auth.feishu
2025-08-02 17:21:25,111 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.fasnote.alm.auth.feishu to HiveMind
2025-08-02 17:21:25,111 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [8] - com.fasnote.alm.checklist
2025-08-02 17:21:25,111 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [9] - com.fasnote.alm.injection
2025-08-02 17:21:25,111 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [10] - com.fasnote.alm.plugin.manage
2025-08-02 17:21:25,111 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [11] - com.fasnote.alm.test
2025-08-02 17:21:25,112 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [12] - com.fasnote.alm.watermark
2025-08-02 17:21:25,113 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [13] - com.fasterxml.classmate
2025-08-02 17:21:25,113 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [14] - com.fasterxml.jackson
2025-08-02 17:21:25,113 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [15] - com.fasterxml.jackson.dataformat.jackson-dataformat-yaml
2025-08-02 17:21:25,113 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [16] - com.fasterxml.jackson.jaxrs
2025-08-02 17:21:25,113 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [17] - com.fasterxml.woodstox
2025-08-02 17:21:25,113 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [18] - com.finething.hesai.ai
2025-08-02 17:21:25,115 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.finething.hesai.ai to HiveMind
2025-08-02 17:21:25,115 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [19] - com.finething.hesai.defect
2025-08-02 17:21:25,115 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.finething.hesai.defect to HiveMind
2025-08-02 17:21:25,115 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [20] - com.google.gson
2025-08-02 17:21:25,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [21] - com.google.guava
2025-08-02 17:21:25,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [22] - com.google.guava.failureaccess
2025-08-02 17:21:25,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [23] - com.ibm.icu.icu4j
2025-08-02 17:21:25,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [24] - com.icl.saxon
2025-08-02 17:21:25,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [25] - com.jayway.jsonpath.json-path
2025-08-02 17:21:25,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [26] - com.jcraft.jsch
2025-08-02 17:21:25,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [27] - com.networknt.json-schema-validator
2025-08-02 17:21:25,116 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [28] - com.nimbusds.content-type
2025-08-02 17:21:25,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [29] - com.nimbusds.nimbus-jose-jwt
2025-08-02 17:21:25,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [30] - com.opensymphony.quartz
2025-08-02 17:21:25,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [31] - com.polarion.alm.ProjectPlanGantt_new
2025-08-02 17:21:25,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.ProjectPlanGantt_new to HiveMind
2025-08-02 17:21:25,117 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [32] - com.polarion.alm.builder
2025-08-02 17:21:25,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.builder to HiveMind
2025-08-02 17:21:25,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [33] - com.polarion.alm.checker
2025-08-02 17:21:25,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.checker to HiveMind
2025-08-02 17:21:25,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [34] - com.polarion.alm.extension.vcontext
2025-08-02 17:21:25,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.extension.vcontext to HiveMind
2025-08-02 17:21:25,118 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [35] - com.polarion.alm.impex
2025-08-02 17:21:25,119 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.impex to HiveMind
2025-08-02 17:21:25,119 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [36] - com.polarion.alm.install
2025-08-02 17:21:25,119 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [37] - com.polarion.alm.oslc
2025-08-02 17:21:25,121 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.oslc to HiveMind
2025-08-02 17:21:25,121 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [38] - com.polarion.alm.projects
2025-08-02 17:21:25,121 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.projects to HiveMind
2025-08-02 17:21:25,121 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [39] - com.polarion.alm.qcentre
2025-08-02 17:21:25,121 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.qcentre to HiveMind
2025-08-02 17:21:25,121 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [40] - com.polarion.alm.tracker
2025-08-02 17:21:25,122 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.tracker to HiveMind
2025-08-02 17:21:25,122 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [41] - com.polarion.alm.ui
2025-08-02 17:21:25,127 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.ui to HiveMind
2025-08-02 17:21:25,127 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [42] - com.polarion.alm.ui.diagrams.mxgraph
2025-08-02 17:21:25,127 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [43] - com.polarion.alm.wiki
2025-08-02 17:21:25,129 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.alm.wiki to HiveMind
2025-08-02 17:21:25,129 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [44] - com.polarion.alm.ws
2025-08-02 17:21:25,129 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [45] - com.polarion.alm.ws.client
2025-08-02 17:21:25,130 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [46] - com.polarion.cluster
2025-08-02 17:21:25,131 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.cluster to HiveMind
2025-08-02 17:21:25,131 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [47] - com.polarion.core.boot
2025-08-02 17:21:25,131 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [48] - com.polarion.core.util
2025-08-02 17:21:25,131 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [49] - com.polarion.fop
2025-08-02 17:21:25,132 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [50] - com.polarion.platform
2025-08-02 17:21:25,132 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform to HiveMind
2025-08-02 17:21:25,132 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [51] - com.polarion.platform.guice
2025-08-02 17:21:25,132 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [52] - com.polarion.platform.hivemind
2025-08-02 17:21:25,133 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.hivemind to HiveMind
2025-08-02 17:21:25,133 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [53] - com.polarion.platform.jobs
2025-08-02 17:21:25,133 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.jobs to HiveMind
2025-08-02 17:21:25,133 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [54] - com.polarion.platform.monitoring
2025-08-02 17:21:25,134 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.monitoring to HiveMind
2025-08-02 17:21:25,134 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [55] - com.polarion.platform.persistence
2025-08-02 17:21:25,134 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.persistence to HiveMind
2025-08-02 17:21:25,134 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [56] - com.polarion.platform.repository
2025-08-02 17:21:25,134 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository to HiveMind
2025-08-02 17:21:25,134 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [57] - com.polarion.platform.repository.driver.svn
2025-08-02 17:21:25,134 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.driver.svn to HiveMind
2025-08-02 17:21:25,135 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [58] - com.polarion.platform.repository.external
2025-08-02 17:21:25,135 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external to HiveMind
2025-08-02 17:21:25,135 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [59] - com.polarion.platform.repository.external.git
2025-08-02 17:21:25,135 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.git to HiveMind
2025-08-02 17:21:25,135 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [60] - com.polarion.platform.repository.external.svn
2025-08-02 17:21:25,135 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.platform.repository.external.svn to HiveMind
2025-08-02 17:21:25,135 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [61] - com.polarion.platform.sql
2025-08-02 17:21:25,135 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [62] - com.polarion.portal.tomcat
2025-08-02 17:21:25,138 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [63] - com.polarion.psvn.launcher
2025-08-02 17:21:25,138 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.psvn.launcher to HiveMind
2025-08-02 17:21:25,138 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [64] - com.polarion.psvn.translations.en
2025-08-02 17:21:25,138 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [65] - com.polarion.purevariants
2025-08-02 17:21:25,139 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.purevariants to HiveMind
2025-08-02 17:21:25,139 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [66] - com.polarion.qcentre
2025-08-02 17:21:25,139 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [67] - com.polarion.scripting
2025-08-02 17:21:25,147 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.scripting to HiveMind
2025-08-02 17:21:25,147 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [68] - com.polarion.scripting.servlet
2025-08-02 17:21:25,147 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [69] - com.polarion.subterra.base
2025-08-02 17:21:25,147 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [70] - com.polarion.subterra.index
2025-08-02 17:21:25,148 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.index to HiveMind
2025-08-02 17:21:25,148 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [71] - com.polarion.subterra.persistence
2025-08-02 17:21:25,148 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence to HiveMind
2025-08-02 17:21:25,148 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [72] - com.polarion.subterra.persistence.document
2025-08-02 17:21:25,148 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.subterra.persistence.document to HiveMind
2025-08-02 17:21:25,149 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [73] - com.polarion.synchronizer
2025-08-02 17:21:25,149 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.synchronizer to HiveMind
2025-08-02 17:21:25,149 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [74] - com.polarion.synchronizer.proxy.feishu
2025-08-02 17:21:25,150 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [75] - com.polarion.synchronizer.proxy.hpalm
2025-08-02 17:21:25,153 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [76] - com.polarion.synchronizer.proxy.jira
2025-08-02 17:21:25,153 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [77] - com.polarion.synchronizer.proxy.polarion
2025-08-02 17:21:25,154 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [78] - com.polarion.synchronizer.proxy.reqif
2025-08-02 17:21:25,163 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [79] - com.polarion.synchronizer.ui
2025-08-02 17:21:25,163 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [80] - com.polarion.usdp.persistence
2025-08-02 17:21:25,163 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.polarion.usdp.persistence to HiveMind
2025-08-02 17:21:25,163 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [81] - com.polarion.xray.doc.user
2025-08-02 17:21:25,163 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [82] - com.siemens.des.logger.api
2025-08-02 17:21:25,163 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [83] - com.siemens.plm.bitools.analytics
2025-08-02 17:21:25,163 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [84] - com.siemens.polarion.ct.collectors.git
2025-08-02 17:21:25,164 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.ct.collectors.git to HiveMind
2025-08-02 17:21:25,164 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [85] - com.siemens.polarion.eclipse.configurator
2025-08-02 17:21:25,165 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [86] - com.siemens.polarion.integration.ci
2025-08-02 17:21:25,165 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.integration.ci to HiveMind
2025-08-02 17:21:25,165 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [87] - com.siemens.polarion.previewer
2025-08-02 17:21:25,166 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer to HiveMind
2025-08-02 17:21:25,166 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [88] - com.siemens.polarion.previewer.external
2025-08-02 17:21:25,166 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.previewer.external to HiveMind
2025-08-02 17:21:25,166 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [89] - com.siemens.polarion.rest
2025-08-02 17:21:25,166 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [90] - com.siemens.polarion.rt
2025-08-02 17:21:25,167 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [91] - com.siemens.polarion.rt.api
2025-08-02 17:21:25,167 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [92] - com.siemens.polarion.rt.collectors.git
2025-08-02 17:21:25,167 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [93] - com.siemens.polarion.rt.communication.common
2025-08-02 17:21:25,167 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [94] - com.siemens.polarion.rt.communication.polarion
2025-08-02 17:21:25,168 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.siemens.polarion.rt.communication.polarion to HiveMind
2025-08-02 17:21:25,168 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [95] - com.siemens.polarion.rt.communication.rt
2025-08-02 17:21:25,168 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [96] - com.siemens.polarion.rt.parsers.c
2025-08-02 17:21:25,168 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [97] - com.siemens.polarion.rt.ui
2025-08-02 17:21:25,168 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [98] - com.siemens.polarion.synchronizer.proxy.tfs
2025-08-02 17:21:25,169 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [99] - com.sun.activation.javax.activation
2025-08-02 17:21:25,169 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [100] - com.sun.istack.commons-runtime
2025-08-02 17:21:25,169 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [101] - com.sun.jna
2025-08-02 17:21:25,169 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [102] - com.sun.jna.platform
2025-08-02 17:21:25,169 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [103] - com.sun.xml.bind.jaxb-impl
2025-08-02 17:21:25,169 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [104] - com.teamlive.hozon.expcounter
2025-08-02 17:21:25,170 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.teamlive.hozon.expcounter to HiveMind
2025-08-02 17:21:25,170 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [105] - com.teamlive.livechecklist
2025-08-02 17:21:25,170 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle com.teamlive.livechecklist to HiveMind
2025-08-02 17:21:25,170 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [106] - com.trilead.ssh2
2025-08-02 17:21:25,170 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [107] - com.zaxxer.hikariCP
2025-08-02 17:21:25,170 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [108] - des-sdk-core
2025-08-02 17:21:25,170 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [109] - des-sdk-dss
2025-08-02 17:21:25,170 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [110] - io.github.resilience4j.circuitbreaker
2025-08-02 17:21:25,170 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [111] - io.github.resilience4j.core
2025-08-02 17:21:25,170 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [112] - io.github.resilience4j.retry
2025-08-02 17:21:25,170 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [113] - io.swagger
2025-08-02 17:21:25,171 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [114] - io.vavr
2025-08-02 17:21:25,171 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [115] - jakaroma
2025-08-02 17:21:25,171 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [116] - jakarta.validation.validation-api
2025-08-02 17:21:25,171 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [117] - javassist
2025-08-02 17:21:25,171 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [118] - javax.annotation-api
2025-08-02 17:21:25,171 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [119] - javax.cache
2025-08-02 17:21:25,171 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [120] - javax.el
2025-08-02 17:21:25,171 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [121] - javax.inject
2025-08-02 17:21:25,171 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [122] - javax.servlet
2025-08-02 17:21:25,171 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [123] - javax.servlet.jsp
2025-08-02 17:21:25,171 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [124] - javax.transaction
2025-08-02 17:21:25,171 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [125] - jaxb-api
2025-08-02 17:21:25,171 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [126] - jcip-annotations
2025-08-02 17:21:25,172 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [127] - jcl.over.slf4j
2025-08-02 17:21:25,172 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [128] - jul.to.slf4j
2025-08-02 17:21:25,172 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [129] - kuromoji-core
2025-08-02 17:21:25,172 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [130] - kuromoji-ipadic
2025-08-02 17:21:25,172 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [131] - lang-tag
2025-08-02 17:21:25,172 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [132] - net.htmlparser.jericho
2025-08-02 17:21:25,172 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [133] - net.java.dev.jna
2025-08-02 17:21:25,172 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [134] - net.minidev.accessors-smart
2025-08-02 17:21:25,172 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [135] - net.minidev.asm
2025-08-02 17:21:25,172 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [136] - net.minidev.json-smart
2025-08-02 17:21:25,172 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [137] - net.n3.nanoxml
2025-08-02 17:21:25,173 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [138] - net.sourceforge.cssparser
2025-08-02 17:21:25,173 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [139] - nu.xom
2025-08-02 17:21:25,173 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [140] - oauth2-oidc-sdk
2025-08-02 17:21:25,173 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [141] - org.apache.ant
2025-08-02 17:21:25,179 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [142] - org.apache.avro
2025-08-02 17:21:25,180 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [143] - org.apache.axis
2025-08-02 17:21:25,183 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [144] - org.apache.batik
2025-08-02 17:21:25,183 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [145] - org.apache.commons.codec
2025-08-02 17:21:25,183 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [146] - org.apache.commons.collections
2025-08-02 17:21:25,183 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [147] - org.apache.commons.commons-beanutils
2025-08-02 17:21:25,183 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [148] - org.apache.commons.commons-collections4
2025-08-02 17:21:25,184 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [149] - org.apache.commons.commons-compress
2025-08-02 17:21:25,184 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [150] - org.apache.commons.commons-fileupload
2025-08-02 17:21:25,184 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [151] - org.apache.commons.digester
2025-08-02 17:21:25,184 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [152] - org.apache.commons.exec
2025-08-02 17:21:25,184 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [153] - org.apache.commons.io
2025-08-02 17:21:25,185 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [154] - org.apache.commons.lang
2025-08-02 17:21:25,185 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [155] - org.apache.commons.lang3
2025-08-02 17:21:25,185 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [156] - org.apache.commons.logging
2025-08-02 17:21:25,185 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [157] - org.apache.curator
2025-08-02 17:21:25,190 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [158] - org.apache.fop
2025-08-02 17:21:25,191 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [159] - org.apache.hivemind
2025-08-02 17:21:25,192 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform - Add bundle org.apache.hivemind to HiveMind
2025-08-02 17:21:25,192 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [160] - org.apache.httpcomponents.httpclient
2025-08-02 17:21:25,192 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [161] - org.apache.httpcomponents.httpcore
2025-08-02 17:21:25,192 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [162] - org.apache.jasper.glassfish
2025-08-02 17:21:25,192 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [163] - org.apache.kafka.clients
2025-08-02 17:21:25,192 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [164] - org.apache.kafka.streams
2025-08-02 17:21:25,192 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [165] - org.apache.logging.log4j.1.2-api
2025-08-02 17:21:25,192 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [166] - org.apache.logging.log4j.api
2025-08-02 17:21:25,193 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [167] - org.apache.logging.log4j.apiconf
2025-08-02 17:21:25,193 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [168] - org.apache.logging.log4j.core
2025-08-02 17:21:25,193 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [169] - org.apache.logging.log4j.slf4j-impl
2025-08-02 17:21:25,193 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [170] - org.apache.lucene.analyzers-common
2025-08-02 17:21:25,193 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [171] - org.apache.lucene.analyzers-common
2025-08-02 17:21:25,194 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [172] - org.apache.lucene.analyzers-smartcn
2025-08-02 17:21:25,194 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [173] - org.apache.lucene.core
2025-08-02 17:21:25,194 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [174] - org.apache.lucene.core
2025-08-02 17:21:25,196 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [175] - org.apache.lucene.grouping
2025-08-02 17:21:25,196 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [176] - org.apache.lucene.queryparser
2025-08-02 17:21:25,196 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [177] - org.apache.oro
2025-08-02 17:21:25,196 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [178] - org.apache.pdfbox.fontbox
2025-08-02 17:21:25,196 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [179] - org.apache.poi
2025-08-02 17:21:25,205 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [180] - org.apache.tika
2025-08-02 17:21:25,666 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [181] - org.apache.xalan
2025-08-02 17:21:25,667 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [182] - org.apache.xercesImpl
2025-08-02 17:21:25,667 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [183] - org.apache.xml.serializer
2025-08-02 17:21:25,667 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [184] - org.apache.xmlgraphics.commons
2025-08-02 17:21:25,667 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [185] - org.apache.zookeeper
2025-08-02 17:21:25,667 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [186] - org.codehaus.groovy
2025-08-02 17:21:25,668 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [187] - org.codehaus.jettison
2025-08-02 17:21:25,668 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [188] - org.dom4j
2025-08-02 17:21:25,668 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [189] - org.eclipse.core.contenttype
2025-08-02 17:21:25,668 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [190] - org.eclipse.core.expressions
2025-08-02 17:21:25,668 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [191] - org.eclipse.core.filesystem
2025-08-02 17:21:25,668 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [192] - org.eclipse.core.jobs
2025-08-02 17:21:25,668 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [193] - org.eclipse.core.net
2025-08-02 17:21:25,668 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [194] - org.eclipse.core.resources
2025-08-02 17:21:25,668 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [195] - org.eclipse.core.runtime
2025-08-02 17:21:25,668 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [196] - org.eclipse.equinox.app
2025-08-02 17:21:25,668 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [197] - org.eclipse.equinox.common
2025-08-02 17:21:25,668 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [198] - org.eclipse.equinox.event
2025-08-02 17:21:25,669 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [199] - org.eclipse.equinox.http.registry
2025-08-02 17:21:25,669 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [200] - org.eclipse.equinox.http.servlet
2025-08-02 17:21:25,669 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [201] - org.eclipse.equinox.jsp.jasper
2025-08-02 17:21:25,669 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [202] - org.eclipse.equinox.jsp.jasper.registry
2025-08-02 17:21:25,669 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [203] - org.eclipse.equinox.launcher
2025-08-02 17:21:25,669 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [204] - org.eclipse.equinox.preferences
2025-08-02 17:21:25,669 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [205] - org.eclipse.equinox.registry
2025-08-02 17:21:25,669 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [206] - org.eclipse.equinox.security
2025-08-02 17:21:25,669 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [207] - org.eclipse.help
2025-08-02 17:21:25,669 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [208] - org.eclipse.help.base
2025-08-02 17:21:25,669 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [209] - org.eclipse.help.webapp
2025-08-02 17:21:25,669 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [210] - org.eclipse.jgit
2025-08-02 17:21:25,669 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [211] - org.eclipse.osgi.services
2025-08-02 17:21:25,670 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [212] - org.eclipse.osgi.util
2025-08-02 17:21:25,670 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [213] - org.ehcache
2025-08-02 17:21:25,671 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [214] - org.gitlab.java-gitlab-api
2025-08-02 17:21:25,671 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [215] - org.glassfish.jersey
2025-08-02 17:21:25,671 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [216] - org.hibernate.annotations
2025-08-02 17:21:25,671 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [217] - org.hibernate.core
2025-08-02 17:21:25,672 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [218] - org.hibernate.entitymanager
2025-08-02 17:21:25,672 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [219] - org.hibernate.hikaricp
2025-08-02 17:21:25,672 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [220] - org.hibernate.jpa.2.1.api
2025-08-02 17:21:25,672 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [221] - org.jboss.logging
2025-08-02 17:21:25,672 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [222] - org.jvnet.mimepull
2025-08-02 17:21:25,672 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [223] - org.objectweb.asm
2025-08-02 17:21:25,672 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [224] - org.objectweb.jotm
2025-08-02 17:21:25,672 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [225] - org.opensaml
2025-08-02 17:21:25,685 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [226] - org.polarion.svncommons
2025-08-02 17:21:25,685 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [227] - org.polarion.svnwebclient
2025-08-02 17:21:25,685 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [228] - org.postgesql
2025-08-02 17:21:25,685 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [229] - org.projectlombok.lombok
2025-08-02 17:21:25,696 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [230] - org.rocksdb.rocksdbjni
2025-08-02 17:21:25,696 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [231] - org.springframework.data.core
2025-08-02 17:21:25,696 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [232] - org.springframework.data.jpa
2025-08-02 17:21:25,696 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [233] - org.springframework.spring-aop
2025-08-02 17:21:25,696 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [234] - org.springframework.spring-beans
2025-08-02 17:21:25,696 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [235] - org.springframework.spring-context
2025-08-02 17:21:25,697 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [236] - org.springframework.spring-core
2025-08-02 17:21:25,697 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [237] - org.springframework.spring-expression
2025-08-02 17:21:25,697 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [238] - org.springframework.spring-jdbc
2025-08-02 17:21:25,697 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [239] - org.springframework.spring-orm
2025-08-02 17:21:25,697 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [240] - org.springframework.spring-test
2025-08-02 17:21:25,697 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [241] - org.springframework.spring-tx
2025-08-02 17:21:25,697 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [242] - org.springframework.spring-web
2025-08-02 17:21:25,697 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [243] - org.springframework.spring-webmvc
2025-08-02 17:21:25,697 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [244] - org.tmatesoft.sqljet
2025-08-02 17:21:25,697 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [245] - org.tmatesoft.svnkit
2025-08-02 17:21:25,698 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [246] - saaj-api
2025-08-02 17:21:25,698 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [247] - sdk-lifecycle-collab
2025-08-02 17:21:25,698 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [248] - sdk-lifecycle-docmgmt
2025-08-02 17:21:25,698 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [249] - siemens.des.clientsecurity
2025-08-02 17:21:25,698 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [250] - slf4j.api
2025-08-02 17:21:25,698 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [251] - xml-apis
2025-08-02 17:21:25,698 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [252] - xml.apis.ext
2025-08-02 17:21:25,699 [main] INFO  com.polarion.platform.hivemind.EclipseHiveMindPlatform -   [253] - xstream
2025-08-02 17:21:25,887 [main] INFO  com.polarion.core.util.remote.server.SocketRemoteControlServer - Remote control server socket is ready to listen on localhost/127.0.0.1:8887
2025-08-02 17:21:25,887 [xServer:8887] INFO  org.xsocket.connection.Server - server listening on localhost:8887 (xSocket 2.5.3)
2025-08-02 17:21:26,090 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database...
2025-08-02 17:21:26,138 [main] INFO  com.polarion.platform.sql.internal.PgServerInfo - PG server listening on localhost:5435
2025-08-02 17:21:28,263 [main] INFO  com.polarion.platform.internal.cache.CacheConfigurator - EHCache uses internal configuration
2025-08-02 17:21:28,530 [main] WARN  org.ehcache.impl.internal.executor.PooledExecutionService - No default pool configured, services requiring thread pools must be configured explicitly using named thread pools
2025-08-02 17:21:28,604 [main] INFO  org.ehcache.sizeof.filters.AnnotationSizeOfFilter - Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-02 17:21:28,609 [main] INFO  org.ehcache.sizeof.impl.JvmInformation - Detected JVM data model settings of: 64-Bit OpenJDK JVM with Compressed OOPs
2025-08-02 17:21:28,620 [main] INFO  org.ehcache.sizeof.impl.AgentLoader - Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-02 17:21:28,686 [main] INFO  com.polarion.platform.internal.cache.CachingProviderHandler - All the caches have been destroyed because of not clean shutdown. You can ignore this message if Polarion started in reindex mode.
2025-08-02 17:21:28,718 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-08-02 17:21:28,719 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion' is: *****************************************
2025-08-02 17:21:28,720 [main] INFO  com.polarion.platform.sql.internal.PgConnection - JDBC url of database 'polarion_history' is: *************************************************
2025-08-02 17:21:28,765 [main] INFO  com.polarion.platform.sql.internal.SqlModule - Initializing database finished [ TIME 2.67 s. ]
2025-08-02 17:21:28,945 [main] INFO  com.polarion.platform.cluster.ClusterService - Initializing cluster service
2025-08-02 17:21:28,945 [main] INFO  com.polarion.platform.cluster.ClusterService - Cluster service is disabled.
2025-08-02 17:21:29,156 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-02 17:21:29,179 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Starting...
2025-08-02 17:21:29,235 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion_history - Start completed.
2025-08-02 17:21:29,286 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.13 s. ]
2025-08-02 17:21:29,286 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-02 17:21:29,299 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Starting...
2025-08-02 17:21:29,305 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool - pg polarion - Start completed.
2025-08-02 17:21:29,329 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0434 s. ]
2025-08-02 17:21:29,329 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot started
2025-08-02 17:21:29,372 [main] INFO  com.polarion.platform.repository.driver.svn.internal.security.SVNWatcher - SVN auth file watcher started with a period of 3000 milliseconds
2025-08-02 17:21:29,378 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-02 17:21:29,404 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion authenticated from system
2025-08-02 17:21:29,454 [main] INFO  com.polarion.platform.internal.security.auth.LoginFlowUserPassword - User polarion logged in from system
2025-08-02 17:21:29,461 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Created
2025-08-02 17:21:29,462 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - Repository Service Initialized
2025-08-02 17:21:29,467 [main | u:p] INFO  com.polarion.core.util.profiling.SimpleProfiler - Initialization
2025-08-02 17:21:29,477 [main | u:p] INFO  org.objectweb.jotm - JOTM started with a local transaction factory which is not bound.
2025-08-02 17:21:29,477 [main | u:p] INFO  org.objectweb.jotm - CAROL initialization
2025-08-02 17:21:29,483 [main | u:p] INFO  com.polarion.platform.internal.service.repository.listeners.job.PullingJob - lastFullyProcessedRevision [275]
2025-08-02 17:21:29,487 [main | u:p] INFO  com.polarion.platform.internal.service.repository.PlatformRepositoryService - END initializeService
2025-08-02 17:21:29,492 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Polarion startup estimation:  [ TIME 17 s. ]
2025-08-02 17:21:29,492 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 17 s. ]
2025-08-02 17:21:29,497 [main | u:p] INFO  com.polarion.platform.monitoring - Full monitoring results are stored in file /opt/polarion/data/workspace/monitoring/results.txt
2025-08-02 17:21:29,522 [main | u:p] INFO  com.polarion.platform.monitoring - Executing actions from stage PREBOOT
2025-08-02 17:21:29,524 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 17:21:29,525 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,417.65 GB
 [Sat Aug 02 17:21:29 CST 2025]
2025-08-02 17:21:29,708 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.jvm.version'
2025-08-02 17:21:29,708 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.jvm.version (JVM Version) = JVM Version 
Microsoft OpenJDK 64-Bit Server VM 11.0.27+6-LTS
 [Sat Aug 02 17:21:29 CST 2025]
2025-08-02 17:21:29,710 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.os.version'
2025-08-02 17:21:29,710 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.os.version (OS Version) = OS Version 
Mac OS X 15.5 aarch64
 [Sat Aug 02 17:21:29 CST 2025]
2025-08-02 17:21:29,713 [main | u:p] INFO  com.polarion.platform.monitoring - Finished with actions from stage PREBOOT: {OK=3}
2025-08-02 17:21:29,713 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.76 s. ]
2025-08-02 17:21:29,713 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0364 s [59% update (144x), 41% query (12x)] (221x), svn: 0.0116 s [60% getLatestRevision (2x), 30% testConnection (1x)] (4x)
2025-08-02 17:21:29,713 [main | u:p] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - calling ILowLevelPersistence.boot to start persistence
2025-08-02 17:21:29,724 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization started
2025-08-02 17:21:29,756 [main | u:p] INFO  com.polarion.subterra.base.internal.location.LocationCacheContext - Registered invalidationListener: com.polarion.platform.repository.internal.config.RepositoryConfigService$1@2ada96fd
2025-08-02 17:21:29,780 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: BaselineCollection
2025-08-02 17:21:29,780 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: TestRun
2025-08-02 17:21:29,780 [main | u:p] INFO  com.polarion.platform.persistence.internal.CustomFieldsService - Custom fields control field is not set for prototype: Plan
2025-08-02 17:21:29,795 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition started
2025-08-02 17:21:29,795 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 17:21:29,795 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-02 17:21:29,820 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context recognition finished [ TIME 0.0249 s. ]
2025-08-02 17:21:29,820 [main | u:p] INFO  com.polarion.platform.repository.internal.context.RepositoryContextService - Context tree: 
ROOT_CTX_NAME (ContextNature[Root], ContextId[context [global]])
+-default (ContextNature[Repository], ContextId[cluster default, context [global]])
  +-WBS (ContextNature[Project], ContextId[cluster default, context WBS])
  +-WBSdev (ContextNature[Project], ContextId[cluster default, context WBSdev])
  +-Demo Projects (ContextNature[ProjectGroup], ContextId[cluster default, context --Demo Projects])
  | +-elibrary (ContextNature[Project], ContextId[cluster default, context elibrary])
  | +-drivepilot (ContextNature[Project], ContextId[cluster default, context drivepilot])
  +-library (ContextNature[Project], ContextId[cluster default, context library])
  +-hesai (ContextNature[Project], ContextId[cluster default, context hesai])
2025-08-02 17:21:29,820 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-02 17:21:29,820 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0328 s [56% getDir2 content (2x), 36% info (3x)] (6x)
2025-08-02 17:21:29,821 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 17:21:29,821 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-02 17:21:29,821 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Startup workers for phase 3: 6
2025-08-02 17:21:29,826 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-08-02 17:21:29,826 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[context [global]] (4/9) ...
2025-08-02 17:21:29,826 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-02 17:21:29,826 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBSdev] (5/9) ...
2025-08-02 17:21:29,826 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-08-02 17:21:29,826 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-08-02 17:21:29,826 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-08-02 17:21:29,827 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context [global]] (3/9) ...
2025-08-02 17:21:29,827 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context --Demo Projects] (6/9) ...
2025-08-02 17:21:29,827 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context library] (2/9) ...
2025-08-02 17:21:29,826 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-08-02 17:21:29,827 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context WBS] (1/9) ...
2025-08-02 17:21:29,833 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[context [global]] (4/9) TOOK  [ TIME 0.00629 s. ]
2025-08-02 17:21:29,833 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-02 17:21:29,833 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context hesai] (7/9) ...
2025-08-02 17:21:29,941 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/.polarion'
2025-08-02 17:21:29,947 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/modules'
2025-08-02 17:21:29,954 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/documents'
2025-08-02 17:21:29,960 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/_wiki'
2025-08-02 17:21:29,961 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context --Demo Projects contains 0 primary objects (work items+comments).
2025-08-02 17:21:29,962 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context --Demo Projects] (6/9) TOOK  [ TIME 0.135 s. ]
2025-08-02 17:21:29,962 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-02 17:21:29,962 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context elibrary] (8/9) ...
2025-08-02 17:21:30,009 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/library/documents'
2025-08-02 17:21:30,018 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBS/documents'
2025-08-02 17:21:30,061 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/elibrary/documents'
2025-08-02 17:21:30,065 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context library contains 288 primary objects (work items+comments).
2025-08-02 17:21:30,065 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context library] (2/9) TOOK  [ TIME 0.238 s. ]
2025-08-02 17:21:30,065 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-02 17:21:30,065 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Initializing context:ContextId[cluster default, context drivepilot] (9/9) ...
2025-08-02 17:21:30,090 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBS contains 344 primary objects (work items+comments).
2025-08-02 17:21:30,090 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBS] (1/9) TOOK  [ TIME 0.263 s. ]
2025-08-02 17:21:30,119 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context elibrary contains 334 primary objects (work items+comments).
2025-08-02 17:21:30,119 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context elibrary] (8/9) TOOK  [ TIME 0.157 s. ]
2025-08-02 17:21:30,169 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/Demo Projects/drivepilot/documents'
2025-08-02 17:21:30,178 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/hesai/documents'
2025-08-02 17:21:30,212 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context drivepilot contains 461 primary objects (work items+comments).
2025-08-02 17:21:30,213 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context drivepilot] (9/9) TOOK  [ TIME 0.148 s. ]
2025-08-02 17:21:30,253 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context hesai contains 1148 primary objects (work items+comments).
2025-08-02 17:21:30,253 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context hesai] (7/9) TOOK  [ TIME 0.42 s. ]
2025-08-02 17:21:30,345 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context null contains 2214 primary objects (work items+comments).
2025-08-02 17:21:30,345 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context [global]] (3/9) TOOK  [ TIME 0.518 s. ]
2025-08-02 17:21:30,383 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.repository.driver.svn.internal.JavaSvnDriver - notExistingTargetPathsCache: caching not existing path '/WBSdev/documents'
2025-08-02 17:21:30,504 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.subterra.persistence.document.DocumentStorage - [Statistics]: context WBSdev contains 3322 primary objects (work items+comments).
2025-08-02 17:21:30,505 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Intializing context:ContextId[cluster default, context WBSdev] (5/9) TOOK  [ TIME 0.678 s. ]
2025-08-02 17:21:30,506 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.682 s, CPU [user: 0.194 s, system: 0.272 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.104 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-02 17:21:30,506 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.681 s, CPU [user: 0.0905 s, system: 0.167 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.0857 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0606 s [79% log2 (10x), 14% getLatestRevision (2x)] (13x)
2025-08-02 17:21:30,506 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.682 s, CPU [user: 0.109 s, system: 0.211 s], Allocated memory: 24.1 MB, transactions: 0, ObjectMaps: 0.0746 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-02 17:21:30,506 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.681 s, CPU [user: 0.0655 s, system: 0.084 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.078 s [76% log2 (10x), 17% getLatestRevision (2x)] (13x), ObjectMaps: 0.0579 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-02 17:21:30,506 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.682 s, CPU [user: 0.223 s, system: 0.33 s], Allocated memory: 68.4 MB, transactions: 0, ObjectMaps: 0.121 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-02 17:21:30,506 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.682 s, CPU [user: 0.0739 s, system: 0.0964 s], Allocated memory: 9.8 MB, transactions: 0, svn: 0.0836 s [39% log2 (5x), 21% info (5x), 18% log (1x), 10% getLatestRevision (2x)] (18x), ObjectMaps: 0.0606 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-02 17:21:30,506 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.69 s. ]
2025-08-02 17:21:30,507 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.504 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.279 s [62% log2 (36x), 14% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-08-02 17:21:30,518 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [START].
2025-08-02 17:21:30,518 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 17:21:30,518 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-02 17:21:30,614 [main | u:p | u:p] ERROR com.polarion.platform.repository.internal.config.RepositoryConfigService$ConfigProblemCatcher - Failed to work with configuration from location /WBS/.polarion/repositories/repositories.xml:
[/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
com.polarion.platform.repository.config.RepositoryConfigurationException: [/WBS/.polarion/repositories/repositories.xml]: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:87) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocations(AbstractDataHandler.java:61) ~[platform-repository.jar:?]
	at $IDataHandler_1986a160b05.readLocations($IDataHandler_1986a160b05.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readLocations(RepositoryConfigService.java:291) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$3.runImpl(RepositoryConfigService.java:328) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction$1.runWEx(RepositoryConfigService.java:113) ~[platform-repository.jar:?]
	at com.polarion.core.util.RunnableWEx.runWRet(RunnableWEx.java:61) ~[util.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService$SVNAccessAction.run(RepositoryConfigService.java:123) ~[platform-repository.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:361) ~[?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:58) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:422) ~[platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:412) ~[platform.jar:?]
	at $ISecurityService_1986a160920.doAsSystemUser($ISecurityService_1986a160920.java) ~[?:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.readConfiguration(RepositoryConfigService.java:324) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getConfigurationImpl(RepositoryConfigService.java:239) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfigurationImpl(RepositoryConfigService.java:199) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.getReadConfiguration(RepositoryConfigService.java:177) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.internal.config.RepositoryConfigService.addConfigurationListener(RepositoryConfigService.java:263) ~[platform-repository.jar:?]
	at $IRepositoryConfigService_1986a16092e.addConfigurationListener($IRepositoryConfigService_1986a16092e.java) ~[?:?]
	at com.polarion.platform.repository.external.internal.ExternalRepositoryProviderRegistry.initialize(ExternalRepositoryProviderRegistry.java:146) ~[platform-repository.jar:?]
	at $IExternalRepositoryProviderRegistry_1986a1609ea.initialize($IExternalRepositoryProviderRegistry_1986a1609ea.java) ~[?:?]
	at $IExternalRepositoryProviderRegistry_1986a1609e9.initialize($IExternalRepositoryProviderRegistry_1986a1609e9.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule.initModule(RevisionsPersistenceModule.java:353) ~[platform-persistence.jar:?]
	at $IObjectPersistenceModule_1986a160ad9.initModule($IObjectPersistenceModule_1986a160ad9.java) ~[?:?]
	at com.polarion.subterra.persistence.internal.PersistenceEngine.initModule(PersistenceEngine.java:251) ~[subterra-uniform-persistence.jar:?]
	at $IPersistenceEngine_1986a160ac1.initModule($IPersistenceEngine_1986a160ac1.java) ~[?:?]
	at com.polarion.platform.persistence.internal.pe.LowLevelDataService.boot(LowLevelDataService.java:352) ~[platform-persistence.jar:?]
	at $ILowLevelPersistence_1986a1609e6.boot($ILowLevelPersistence_1986a1609e6.java) ~[?:?]
	at $ILowLevelPersistence_1986a1609e5.boot($ILowLevelPersistence_1986a1609e5.java) ~[?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.lambda$0(PlatformService.java:294) ~[launcher.jar:?]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:?]
	at javax.security.auth.Subject.doAs(Subject.java:423) [?:?]
	at com.polarion.platform.internal.security.SubjectNDC.doAs(SubjectNDC.java:69) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsUser(SecurityService.java:417) [platform.jar:?]
	at com.polarion.platform.internal.security.SecurityService.doAsSystemUser(SecurityService.java:407) [platform.jar:?]
	at $ISecurityService_1986a160921.doAsSystemUser($ISecurityService_1986a160921.java) [?:?]
	at $ISecurityService_1986a160920.doAsSystemUser($ISecurityService_1986a160920.java) [?:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.bootPlatform(PlatformService.java:286) [launcher.jar:?]
	at com.polarion.psvn.launcher.internal.platform.PlatformService.start(PlatformService.java:92) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.runImpl(PolarionSVNApplication.java:139) [launcher.jar:?]
	at com.polarion.psvn.launcher.PolarionSVNApplication.run(PolarionSVNApplication.java:94) [launcher.jar:?]
	at com.polarion.core.boot.launchers.BasicAppLauncher.launch(BasicAppLauncher.java:53) [boot.jar:?]
	at com.polarion.core.boot.impl.AppLaunchersManager.start(AppLaunchersManager.java:184) [boot.jar:?]
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196) [org.eclipse.equinox.app_1.3.500.v20171221-2204.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:388) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:243) [org.eclipse.osgi_3.13.0.v20180409-1500.jar:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:656) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:592) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.run(Main.java:1498) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
	at org.eclipse.equinox.launcher.Main.main(Main.java:1471) [org.eclipse.equinox.launcher_1.5.0.v20180512-1130.jar:?]
Caused by: com.thoughtworks.xstream.converters.ConversionException: 
---- Debugging information ----
cause-exception     : com.thoughtworks.xstream.mapper.CannotResolveClassException
cause-message       : AutoBranchGitLab
class               : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
required-type       : [Lcom.polarion.platform.repository.external.IExternalRepositoryConfiguration;
converter-type      : com.thoughtworks.xstream.converters.collections.ArrayConverter
version             : 1.4.17
-------------------------------
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:77) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
Caused by: com.thoughtworks.xstream.mapper.CannotResolveClassException: AutoBranchGitLab
	at com.thoughtworks.xstream.mapper.DefaultMapper.realClass(DefaultMapper.java:81) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.DynamicProxyMapper.realClass(DynamicProxyMapper.java:55) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.PackageAliasingMapper.realClass(PackageAliasingMapper.java:88) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ClassAliasingMapper.realClass(ClassAliasingMapper.java:79) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.ArrayMapper.realClass(ArrayMapper.java:74) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.SecurityMapper.realClass(SecurityMapper.java:71) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.MapperWrapper.realClass(MapperWrapper.java:125) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.mapper.CachingMapper.realClass(CachingMapper.java:47) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.util.HierarchicalStreams.readClassType(HierarchicalStreams.java:29) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readBareItem(AbstractCollectionConverter.java:131) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readItem(AbstractCollectionConverter.java:117) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readCompleteItem(AbstractCollectionConverter.java:147) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.converters.collections.ArrayConverter.unmarshal(ArrayConverter.java:54) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:72) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.TreeUnmarshaller.start(TreeUnmarshaller.java:134) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.core.AbstractTreeMarshallingStrategy.unmarshal(AbstractTreeMarshallingStrategy.java:32) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1431) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.unmarshal(XStream.java:1411) ~[xstream-1.4.17.jar:1.4.17]
	at com.thoughtworks.xstream.XStream.fromXML(XStream.java:1305) ~[xstream-1.4.17.jar:1.4.17]
	at com.polarion.platform.repository.external.internal.RepositoriesDataHandler.processData(RepositoriesDataHandler.java:119) ~[platform-repository.jar:?]
	at com.polarion.platform.repository.spi.config.AbstractDataHandler.readLocation(AbstractDataHandler.java:83) ~[platform-repository.jar:?]
	... 56 more
2025-08-02 17:21:30,708 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-02 17:21:30,718 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions from repository default in context ContextId[context [global]] finished
2025-08-02 17:21:30,719 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.RevisionsPersistenceModule - Processing new revisions [FINISHED].
2025-08-02 17:21:30,719 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.21 s. ]
2025-08-02 17:21:30,719 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.18 s [100% getReadConfiguration (48x)] (48x), svn: 0.0733 s [87% info (18x)] (38x)
2025-08-02 17:21:30,742 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 17:21:30,742 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-02 17:21:30,742 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting repository for build artifacts-related changes
2025-08-02 17:21:30,743 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[context [global]]
2025-08-02 17:21:30,743 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[context [global]]
2025-08-02 17:21:30,744 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[context [global]] has been successfully processed
2025-08-02 17:21:30,746 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[context [global]] finished [ TIME 0.00308 s. ]
2025-08-02 17:21:30,746 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-02 17:21:30,746 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context [global]]
2025-08-02 17:21:30,746 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context [global]]
2025-08-02 17:21:30,763 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context [global]] has been successfully processed
2025-08-02 17:21:30,794 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context [global]] finished [ TIME 0.0485 s. ]
2025-08-02 17:21:30,794 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-02 17:21:30,794 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBS]
2025-08-02 17:21:30,794 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBS]
2025-08-02 17:21:30,814 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBS] has been successfully processed
2025-08-02 17:21:30,843 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBS] finished [ TIME 0.049 s. ]
2025-08-02 17:21:30,843 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-02 17:21:30,843 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context WBSdev]
2025-08-02 17:21:30,843 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context WBSdev]
2025-08-02 17:21:30,865 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context WBSdev] has been successfully processed
2025-08-02 17:21:30,888 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context WBSdev] finished [ TIME 0.0443 s. ]
2025-08-02 17:21:30,888 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-02 17:21:30,888 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context --Demo Projects]
2025-08-02 17:21:30,888 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context --Demo Projects]
2025-08-02 17:21:30,898 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context --Demo Projects] has been successfully processed
2025-08-02 17:21:30,912 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context --Demo Projects] finished [ TIME 0.0244 s. ]
2025-08-02 17:21:30,912 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-02 17:21:30,912 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context library]
2025-08-02 17:21:30,912 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context library]
2025-08-02 17:21:30,921 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context library] has been successfully processed
2025-08-02 17:21:30,934 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context library] finished [ TIME 0.0213 s. ]
2025-08-02 17:21:30,934 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-02 17:21:30,934 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context hesai]
2025-08-02 17:21:30,934 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context hesai]
2025-08-02 17:21:30,951 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context hesai] has been successfully processed
2025-08-02 17:21:30,971 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context hesai] finished [ TIME 0.0369 s. ]
2025-08-02 17:21:30,971 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-02 17:21:30,971 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context elibrary]
2025-08-02 17:21:30,971 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context elibrary]
2025-08-02 17:21:30,990 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context elibrary] has been successfully processed
2025-08-02 17:21:31,009 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context elibrary] finished [ TIME 0.038 s. ]
2025-08-02 17:21:31,009 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-02 17:21:31,009 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts from context ContextId[cluster default, context drivepilot]
2025-08-02 17:21:31,009 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Processing build artifacts configuration from context ContextId[cluster default, context drivepilot]
2025-08-02 17:21:31,019 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Build artifacts configuration from context ContextId[cluster default, context drivepilot] has been successfully processed
2025-08-02 17:21:31,033 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... processing build artifacts from context ContextId[cluster default, context drivepilot] finished [ TIME 0.024 s. ]
2025-08-02 17:21:31,033 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-02 17:21:31,033 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... repository inspection finished [ TIME 0.291 s. ]
2025-08-02 17:21:31,033 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.31 s. ]
2025-08-02 17:21:31,033 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.25 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.192 s [100% getReadConfiguration (54x)] (54x)
2025-08-02 17:21:31,034 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 17:21:31,034 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-02 17:21:31,034 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - Inspecting BIR for new or removed builds
2025-08-02 17:21:31,046 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were removed (including calculations from previous run)
2025-08-02 17:21:31,046 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... 0 build(s) were added or modified
2025-08-02 17:21:31,046 [main | u:p] INFO  com.polarion.alm.builder.internal.persistence.BuilderPersistenceModule - ... BIR inspection finished [ TIME 0.0128 s. ]
2025-08-02 17:21:31,046 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-02 17:21:31,047 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 17:21:31,047 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-02 17:21:31,047 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing startup index events, starting iterations.
2025-08-02 17:21:31,047 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Iteration 1 - processing 5 events
2025-08-02 17:21:31,052 [main | u:p] INFO  com.polarion.alm.tracker.internal.planning.PlanFieldsProvider - livePlanXMLLocation: Location[path /default/.reports/xml/live-plan.xml]
2025-08-02 17:21:31,070 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener -  - reindexing 1 existing objects and 0 deleted objects.
2025-08-02 17:21:31,149 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-02 17:21:31,151 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPage
2025-08-02 17:21:31,151 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Attachment
2025-08-02 17:21:31,151 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-08-02 17:21:31,151 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-02 17:21:31,152 [main | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-08-02 17:21:31,180 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Attachment
2025-08-02 17:21:31,180 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPage
2025-08-02 17:21:31,180 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-08-02 17:21:31,180 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPageAttachment
2025-08-02 17:21:31,181 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-08-02 17:21:31,186 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-08-02 17:21:31,187 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPageAttachment
2025-08-02 17:21:31,190 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem
2025-08-02 17:21:31,191 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: UserGroup
2025-08-02 17:21:31,191 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BaselineCollection
2025-08-02 17:21:31,193 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: UserGroup
2025-08-02 17:21:31,194 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRun
2025-08-02 17:21:31,198 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BaselineCollection
2025-08-02 17:21:31,199 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRun
2025-08-02 17:21:31,199 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Build
2025-08-02 17:21:31,199 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleComment
2025-08-02 17:21:31,200 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Build
2025-08-02 17:21:31,200 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Comment
2025-08-02 17:21:31,202 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleComment
2025-08-02 17:21:31,202 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: BuildArtifact
2025-08-02 17:21:31,202 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: DocumentWorkflowSignature
2025-08-02 17:21:31,203 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Comment
2025-08-02 17:21:31,204 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: BuildArtifact
2025-08-02 17:21:31,205 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WikiPageAttachment
2025-08-02 17:21:31,205 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WikiPageAttachment
2025-08-02 17:21:31,206 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-08-02 17:21:31,206 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: DocumentWorkflowSignature
2025-08-02 17:21:31,206 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Baseline
2025-08-02 17:21:31,207 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-08-02 17:21:31,209 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Baseline
2025-08-02 17:21:31,209 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Plan
2025-08-02 17:21:31,209 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem
2025-08-02 17:21:31,212 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Plan
2025-08-02 17:21:31,212 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunAttachment
2025-08-02 17:21:31,213 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkRecord
2025-08-02 17:21:31,213 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunAttachment
2025-08-02 17:21:31,214 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-08-02 17:21:31,214 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem-OutlineNumbers
2025-08-02 17:21:31,215 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem-OutlineNumbers
2025-08-02 17:21:31,215 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ModuleAttachment
2025-08-02 17:21:31,216 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-08-02 17:21:31,217 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TestRunComment
2025-08-02 17:21:31,217 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkRecord
2025-08-02 17:21:31,218 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TestRunComment
2025-08-02 17:21:31,218 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Revision
2025-08-02 17:21:31,218 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: RichPage
2025-08-02 17:21:31,232 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Module
2025-08-02 17:21:31,236 [PolarionDocIdCreator-2] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: RichPage
2025-08-02 17:21:31,236 [PolarionDocIdCreator-3] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Revision
2025-08-02 17:21:31,236 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}272
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}274
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}273
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}270
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}271
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}224
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}225
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}226
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}228
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}230
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}231
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}232
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}233
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}234
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}235
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}239
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}240
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}241
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}242
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}243
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}245
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}246
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}248
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}249
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}250
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}251
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}253
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}254
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}255
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}256
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}257
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}258
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}259
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}260
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}261
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}262
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}263
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}264
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}265
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}267
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}268
2025-08-02 17:21:31,237 [PolarionDocIdCreator-3] WARN  com.polarion.subterra.index.impl.LuceneWrapper - The polarion document identifier was not found for subterra:data-service:objects:/default/${Revision}269
2025-08-02 17:21:31,238 [PolarionDocIdCreator-4] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ModuleAttachment
2025-08-02 17:21:31,239 [PolarionDocIdCreator-6] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-08-02 17:21:31,255 [PolarionDocIdCreator-5] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Module
2025-08-02 17:21:31,269 [PolarionDocIdCreator-1] INFO  com.polarion.subterra.index.impl.lucene.baseline.PolarionDocIdCreator - Bloom filter loading for 28 indices took  [ TIME 0.131 s. ]
2025-08-02 17:21:31,277 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.DelegatingCalculatedFieldsListener - Calculated fields mode: async
2025-08-02 17:21:31,279 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.PersistenceEngineListener - Flushing took  [ TIME 0.232 s. ]
2025-08-02 17:21:31,279 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-08-02 17:21:31,279 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.232 s [100% doFinishStartup (1x)] (1x), commit: 0.0733 s [100% Revision (1x)] (1x), Lucene: 0.0281 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0135 s [100% objectsToInv (1x)] (1x)
2025-08-02 17:21:31,279 [main | u:p] INFO  com.polarion.platform.internal.service.repository.ListenerManager - Starting the pulling job for repository: default
2025-08-02 17:21:31,279 [main | u:p] INFO  com.polarion.platform.persistence.internal.pe.LowLevelDataService - Persistence initialization finished [ TIME 1.56 s. ]
2025-08-02 17:21:31,279 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 17:21:31,279 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-02 17:21:31,280 [main | u:p] INFO  com.polarion.platform.persistence.internal.calcfields.CalculatedFieldsStorage - Checking integrity of calculated fields storage /opt/polarion/data/workspace/polarion-data/calculated-fields
2025-08-02 17:21:31,286 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-02 17:21:31,286 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 17:21:31,286 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-02 17:21:31,303 [main | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - Updating local scheduler state: start
2025-08-02 17:21:31,310 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-08-02 17:21:31,314 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-02 17:21:31,314 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-08-02 17:21:31,314 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-08-02 17:21:31,314 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutting down.
2025-08-02 17:21:31,314 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED paused.
2025-08-02 17:21:31,314 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-08-02 17:21:31,316 [main | u:p | u:p] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: main | u:p | u:p
2025-08-02 17:21:31,316 [main | u:p | u:p] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-02 17:21:31,316 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-08-02 17:21:31,316 [main | u:p | u:p] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 1.4.2
2025-08-02 17:21:31,316 [main | u:p | u:p] INFO  com.polarion.platform.jobs.internal.service.scheduler.JobSchedulerService - 15 scheduled job(s) configured
2025-08-02 17:21:31,321 [main | u:p | u:p] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED started.
2025-08-02 17:21:31,497 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform boot finished
2025-08-02 17:21:31,497 [main] INFO  com.polarion.psvn.launcher.internal.platform.PlatformService - Platform started
2025-08-02 17:21:31,505 [main] INFO  com.polarion.portal.tomcat.TomcatPlugin - Tomcat home directory was set to /opt/polarion/data/workspace/.metadata/.plugins/com.polarion.portal.tomcat
2025-08-02 17:21:31,511 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Starting Tomcat...
2025-08-02 17:21:31,579 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: webui, contextRoot: webapp/webui, plugin: com.polarion.alm.ui, priority: -10]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,580 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion, contextRoot: webapp/authapp, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,580 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/.well-known, contextRoot: webapp/well-known, plugin: com.polarion.platform, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,580 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ProjectPlanGantt, contextRoot: webapp, plugin: com.polarion.alm.ProjectPlanGantt_new, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,580 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/activate, contextRoot: webapp/activation, plugin: com.polarion.psvn.launcher, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,581 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/announcements, contextRoot: webapp/announcements, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,581 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/bir, contextRoot: webapp/bir, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,581 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/checklist, contextRoot: src/main/webapp, plugin: com.fasnote.alm.checklist, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,581 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/codemirror-modes, contextRoot: webapp/codemirror-modes, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,581 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/defect, contextRoot: webapp, plugin: com.finething.hesai.defect, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,581 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/doorsconnector, contextRoot: webapp, plugin: com.polarion.synchronizer, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,581 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/export, contextRoot: webapp/export, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,581 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/fileupload, contextRoot: webapp/fileupload, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,582 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/gwt, contextRoot: war, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,582 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/hesai-ai, contextRoot: webapp, plugin: com.finething.hesai.ai, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,582 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/icons, contextRoot: webapp/icons, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,582 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/internal-login, contextRoot: webapp/internal-login, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,582 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/livechecklist, contextRoot: webapp, plugin: com.teamlive.livechecklist, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,582 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/module-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,583 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/modulehome, contextRoot: webapp/module-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,583 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/mxgraph, contextRoot: draw.io/war, plugin: com.polarion.alm.ui.diagrams.mxgraph, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,583 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/oauth-feishu, contextRoot: webapp, plugin: com.fasnote.alm.auth.feishu, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,583 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/page-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,583 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/perf-testing, contextRoot: webapp/perf-testing, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,584 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/plugin-manage, contextRoot: webapp, plugin: com.fasnote.alm.plugin.manage, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,584 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/print, contextRoot: webapp/print, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,584 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/register, contextRoot: webapp/register, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,584 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rest, contextRoot: webapp, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,584 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ria, contextRoot: webapp/ria, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,584 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/richpagehome, contextRoot: webapp/richpage-home, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,585 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt, contextRoot: src/main/webapp, plugin: com.siemens.polarion.rt, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,585 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/rt-connect, contextRoot: ws, plugin: com.siemens.polarion.rt.communication.polarion, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,585 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/scripting, contextRoot: webapp/scripting, plugin: com.polarion.scripting.servlet, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,585 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/sdk, contextRoot: webapp/sdk, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,585 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/svnwebclient, contextRoot: webapp, plugin: org.polarion.svnwebclient, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,585 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/swagger, contextRoot: webapp/swagger, plugin: com.siemens.polarion.rest, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,585 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/synchronizer, contextRoot: webapp, plugin: com.polarion.synchronizer.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,585 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/template-download, contextRoot: webapp/project-template, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,585 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/testrun-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,585 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/tour, contextRoot: webapp/tour, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,586 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/watermark, contextRoot: webapp, plugin: com.fasnote.alm.watermark, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,586 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment, contextRoot: webapp/attachments, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,586 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wi-attachment-auth, contextRoot: webapp/wi-attachment-auth, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,586 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/widget-resource, contextRoot: webapp/widget-resource, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,586 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/wiki, contextRoot: src/main/webapp, plugin: com.polarion.alm.wiki, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,587 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/workreport, contextRoot: webapp/workreport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,587 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/ws, contextRoot: ws, plugin: com.polarion.alm.ws, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,587 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/xunitimport, contextRoot: webapp/xunitimport, plugin: com.polarion.alm.ui, priority: 0]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,587 [main] INFO  com.polarion.portal.tomcat.TomcatAppServer - TomcatAppServer.start(): added webapp 'WebappContribution[webappName: polarion/oslc, contextRoot: webapp, plugin: com.polarion.alm.oslc, priority: 1]'; protocol: AJP/1.3, port: 8889
2025-08-02 17:21:31,632 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Initializing ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-08-02 17:21:31,639 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-02 17:21:31,639 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-08-02 17:21:31,658 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6b666548] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,658 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@676d182d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,658 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4e942689] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,658 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2b0e378c] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,658 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@12a0dc5a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,658 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1c586da4] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,672 [Catalina-utility-2] INFO  org.apache.catalina.startup.ContextConfig - No global web.xml found
2025-08-02 17:21:31,680 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,680 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,681 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,681 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [admin] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,681 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,702 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@736c02d2] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,703 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@590ac60e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,703 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@26949d82] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,704 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4186bce2] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,706 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@129e86ec] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,707 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,709 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,709 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,712 [Catalina-utility-2] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@10b4488b] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,712 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@630762b4] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,713 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@6d6f2596] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,713 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@31cddebb] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,714 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,716 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,717 [Catalina-utility-2] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,719 [Catalina-utility-4] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 17:21:31,719 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2b457208] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,721 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@37eb698b] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,721 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@74dc7276] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,723 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1cf01723] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,724 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,724 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,729 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,731 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5d2bd45a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,731 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3a0b0e06] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,734 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@52c3818d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,735 [Catalina-utility-4] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 17:21:31,738 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,741 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1013b36a] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,745 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3709c652] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,746 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,746 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3ba1b2d4] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,746 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@71c92109] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,749 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,751 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,752 [Catalina-utility-5] FATAL org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/polarion/oauth-feishu] - For security constraints with URL pattern [/userinfo] only the HTTP methods [POST GET] are covered. All other methods are uncovered.
2025-08-02 17:21:31,757 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7a3769ed] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,758 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@478ad60d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,759 [Catalina-utility-5] INFO  com.fasnote.alm.injection.osgi.InjectionActivator - 启动ALM依赖注入框架
2025-08-02 17:21:31,768 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@5565fdd2] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,778 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@245d3133] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,782 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@785f4c04] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,785 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,790 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,791 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4906e4cd] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,796 [Catalina-utility-1] INFO  org.apache.tomcat.dbcp.dbcp2.BasicDataSourceFactory - Name = XWikiDS Ignoring unknown property: value of "DB Connection" for "description" property
2025-08-02 17:21:31,799 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,817 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 创建OSGi感知的纯粹依赖注入器
2025-08-02 17:21:31,817 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 初始化包扫描提供者跟踪机制
2025-08-02 17:21:31,817 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 包扫描提供者跟踪机制初始化完成
2025-08-02 17:21:31,818 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 开始注册 OSGi 服务到依赖注入容器
2025-08-02 17:21:31,820 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: org.osgi.framework.BundleContext
2025-08-02 17:21:31,821 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - OSGi 服务注册完成，已注册 1 个 OSGi 服务提供者
2025-08-02 17:21:31,821 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.osgi.InjectionActivator - 启动Bundle跟踪机制
2025-08-02 17:21:31,824 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 包扫描提供者跟踪已启动
2025-08-02 17:21:31,825 [Catalina-utility-5] INFO  com.fasnote.alm.injection.osgi.InjectionActivator - 包扫描提供者跟踪机制启动成功
2025-08-02 17:21:31,825 [Catalina-utility-5] INFO  com.fasnote.alm.injection.osgi.InjectionActivator - ALM依赖注入框架启动成功
2025-08-02 17:21:31,825 [Catalina-utility-5] INFO  com.fasnote.alm.auth.feishu.Activator - Feishu Authentication Plugin starting...
2025-08-02 17:21:31,825 [Catalina-utility-5] INFO  com.fasnote.alm.auth.feishu.Activator - 注册 FeishuPackageScanProvider 为 OSGi 服务...
2025-08-02 17:21:31,826 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.auth.feishu 注册DI扫描包路径: [com.fasnote.alm.auth.feishu]
2025-08-02 17:21:31,827 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.auth.feishu 就绪，开始扫描服务 (状态: 8, 扫描包: [com.fasnote.alm.auth.feishu])
2025-08-02 17:21:31,828 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasnote.alm.auth.feishu
2025-08-02 17:21:31,829 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 开始扫描 Bundle 服务: com.fasnote.alm.auth.feishu (状态: 8, 扫描包: [com.fasnote.alm.auth.feishu])
2025-08-02 17:21:31,860 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.auth.feishu 的包 com.fasnote.alm.auth.feishu 中发现 19 个类文件
2025-08-02 17:21:31,862 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory$FeishuAuthenticatorManagerInvocationHandler (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,863 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuOAuth2LoginClient (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,863 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,863 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.config.FeishuConfigurationAdapter (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,864 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuOAuth2Authenticator (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,866 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuAuthenticatorManagerInterceptorFactory (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,866 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,868 [Catalina-utility-5] INFO  com.fasnote.alm.plugin.manage.Activator - 启动许可证管理插件...
2025-08-02 17:21:31,868 [Catalina-utility-5] DEBUG com.fasnote.alm.plugin.manage.Activator - 初始化许可证配置...
2025-08-02 17:21:31,869 [Catalina-utility-5] DEBUG com.fasnote.alm.plugin.manage.Activator - 从环境变量加载配置完成
2025-08-02 17:21:31,869 [Catalina-utility-5] DEBUG com.fasnote.alm.plugin.manage.Activator - 许可证配置初始化完成
2025-08-02 17:21:31,869 [Catalina-utility-5] INFO  com.fasnote.alm.plugin.manage.Activator - 注册 LicensePackageScanProvider 为 OSGi 服务...
2025-08-02 17:21:31,869 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.plugin.manage 注册DI扫描包路径: [com.fasnote.alm.plugin.manage.injection.module]
2025-08-02 17:21:31,869 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.plugin.manage 就绪，开始扫描服务 (状态: 8, 扫描包: [com.fasnote.alm.plugin.manage.injection.module])
2025-08-02 17:21:31,869 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,869 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 开始扫描 Bundle 服务: com.fasnote.alm.plugin.manage (状态: 8, 扫描包: [com.fasnote.alm.plugin.manage.injection.module])
2025-08-02 17:21:31,870 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - Bundle com.fasnote.alm.plugin.manage 的包 com.fasnote.alm.plugin.manage.injection.module 中发现 5 个类文件
2025-08-02 17:21:31,871 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,871 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 发现 IModule 实现: com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule (Bundle: com.fasnote.alm.plugin.manage)
2025-08-02 17:21:31,872 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功使用无参构造函数创建模块: com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule
2025-08-02 17:21:31,877 [Catalina-utility-5] INFO  com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule - 配置插件管理主模块...
2025-08-02 17:21:31,878 [Catalina-utility-5] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseModule使用无参构造函数创建
2025-08-02 17:21:31,878 [Catalina-utility-5] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 配置统一许可证模块（重构后）...
2025-08-02 17:21:31,878 [Catalina-utility-5] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 注册核心组件...
2025-08-02 17:21:31,878 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: java.util.Map
2025-08-02 17:21:31,880 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,880 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.security.MachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,880 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,880 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.security.MachineCodeProvider, 实现: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,884 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.security.MachineCodeProvider -> com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider  [LAZY_SINGLETON]
2025-08-02 17:21:31,885 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,885 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,885 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,885 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.config.LicenseConfiguration, 实例类: com.fasnote.alm.plugin.manage.config.LicenseConfiguration, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,885 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage.config.LicenseConfiguration, 实例类型: LicenseConfiguration
2025-08-02 17:21:31,885 [Catalina-utility-5] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 核心组件注册完成
2025-08-02 17:21:31,885 [Catalina-utility-5] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 注册管理器组件...
2025-08-02 17:21:31,886 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.BundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,886 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IBundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,886 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.BundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,886 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IBundleManager, 实现: com.fasnote.alm.plugin.manage.core.BundleManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,886 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IBundleManager -> com.fasnote.alm.plugin.manage.core.BundleManager  [LAZY_SINGLETON]
2025-08-02 17:21:31,887 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.ClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,887 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,887 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.ClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,887 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IClassLoaderManager, 实现: com.fasnote.alm.plugin.manage.core.ClassLoaderManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,887 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IClassLoaderManager -> com.fasnote.alm.plugin.manage.core.ClassLoaderManager  [LAZY_SINGLETON]
2025-08-02 17:21:31,888 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,888 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,888 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,888 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseValidator, 实现: com.fasnote.alm.plugin.manage.core.LicenseValidator, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,888 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseValidator -> com.fasnote.alm.plugin.manage.core.LicenseValidator  [LAZY_SINGLETON]
2025-08-02 17:21:31,889 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,889 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,889 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,889 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager, 实现: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,889 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager -> com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager  [LAZY_SINGLETON]
2025-08-02 17:21:31,890 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,890 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,890 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,890 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator, 实现: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,890 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator  [LAZY_SINGLETON]
2025-08-02 17:21:31,891 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,891 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,891 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,891 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager, 实现: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,891 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager  [LAZY_SINGLETON]
2025-08-02 17:21:31,893 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,893 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,894 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,894 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseFileManager, 实现: com.fasnote.alm.plugin.manage.core.LicenseFileManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,894 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseFileManager -> com.fasnote.alm.plugin.manage.core.LicenseFileManager  [LAZY_SINGLETON]
2025-08-02 17:21:31,894 [Catalina-utility-5] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 管理器组件注册完成
2025-08-02 17:21:31,895 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,895 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,895 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,895 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseManager, 实现: com.fasnote.alm.plugin.manage.core.LicenseManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,895 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseManager -> com.fasnote.alm.plugin.manage.core.LicenseManager  [LAZY_SINGLETON]
2025-08-02 17:21:31,895 [Catalina-utility-5] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseManager接口绑定配置完成
2025-08-02 17:21:31,895 [Catalina-utility-5] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseAwareServiceResolver 将通过自动扫描机制注册
2025-08-02 17:21:31,896 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: com.fasnote.alm.plugin.manage.api.LicenseAware
2025-08-02 17:21:31,896 [Catalina-utility-5] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 统一许可证模块配置完成（重构后）
2025-08-02 17:21:31,896 [Catalina-utility-5] INFO  com.fasnote.alm.plugin.manage.web.injection.WebModule - 配置Web层依赖注入模块...
2025-08-02 17:21:31,896 [Catalina-utility-5] DEBUG com.fasnote.alm.plugin.manage.web.injection.WebModule - 注册Web服务...
2025-08-02 17:21:31,897 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,897 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,897 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,897 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl, 实现: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,897 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl  [LAZY_SINGLETON]
2025-08-02 17:21:31,897 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,897 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.web.service.PluginManagementService -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,898 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,898 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.web.service.PluginManagementService, 实现: com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,898 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.web.service.PluginManagementService -> com.fasnote.alm.plugin.manage.web.service.impl.PluginManagementServiceImpl  [LAZY_SINGLETON]
2025-08-02 17:21:31,898 [Catalina-utility-5] DEBUG com.fasnote.alm.plugin.manage.web.injection.WebModule - Web服务注册完成
2025-08-02 17:21:31,898 [Catalina-utility-5] INFO  com.fasnote.alm.plugin.manage.web.injection.WebModule - Web层依赖注入模块配置完成
2025-08-02 17:21:31,898 [Catalina-utility-5] INFO  com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule - 插件管理主模块配置完成
2025-08-02 17:21:31,898 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 已安装模块: PluginManageMainModule (优先级: 0)
2025-08-02 17:21:31,898 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功安装模块: com.fasnote.alm.plugin.manage.injection.module.PluginManageMainModule
2025-08-02 17:21:31,898 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.LicensePackageScanProvider (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,898 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.LicenseModule$LicenseAwareProvider$1 (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,898 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.LicenseModule$LicenseAwareProvider (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,898 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.plugin.manage.injection.module.LicenseModule (Bundle: com.fasnote.alm.plugin.manage, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,898 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 发现 IModule 实现: com.fasnote.alm.plugin.manage.injection.module.LicenseModule (Bundle: com.fasnote.alm.plugin.manage)
2025-08-02 17:21:31,898 [Catalina-utility-5] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseModule使用无参构造函数创建
2025-08-02 17:21:31,898 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功使用无参构造函数创建模块: com.fasnote.alm.plugin.manage.injection.module.LicenseModule
2025-08-02 17:21:31,898 [Catalina-utility-5] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 配置统一许可证模块（重构后）...
2025-08-02 17:21:31,898 [Catalina-utility-5] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 注册核心组件...
2025-08-02 17:21:31,898 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: java.util.Map
2025-08-02 17:21:31,898 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,898 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.security.MachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,898 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,898 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.security.MachineCodeProvider, 实现: com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.security.MachineCodeProvider -> com.fasnote.alm.plugin.manage.security.PolarionMachineCodeProvider  [LAZY_SINGLETON]
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.config.LicenseConfiguration, 实例类: com.fasnote.alm.plugin.manage.config.LicenseConfiguration, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务: com.fasnote.alm.plugin.manage.config.LicenseConfiguration -> com.fasnote.alm.plugin.manage.config.LicenseConfiguration, 实例类型: LicenseConfiguration
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 核心组件注册完成
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 注册管理器组件...
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.BundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IBundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.BundleManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IBundleManager, 实现: com.fasnote.alm.plugin.manage.core.BundleManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IBundleManager -> com.fasnote.alm.plugin.manage.core.BundleManager  [LAZY_SINGLETON]
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.ClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.ClassLoaderManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IClassLoaderManager, 实现: com.fasnote.alm.plugin.manage.core.ClassLoaderManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IClassLoaderManager -> com.fasnote.alm.plugin.manage.core.ClassLoaderManager  [LAZY_SINGLETON]
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseValidator -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseValidator, 实现: com.fasnote.alm.plugin.manage.core.LicenseValidator, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseValidator -> com.fasnote.alm.plugin.manage.core.LicenseValidator  [LAZY_SINGLETON]
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager, 实现: com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IServiceRegistrationManager -> com.fasnote.alm.plugin.manage.core.ServiceRegistrationManager  [LAZY_SINGLETON]
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator, 实现: com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,900 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IPluginRuntimeCoordinator -> com.fasnote.alm.plugin.manage.core.PluginRuntimeCoordinator  [LAZY_SINGLETON]
2025-08-02 17:21:31,901 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,901 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,901 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,901 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager, 实现: com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,901 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.IRuntimeEnvironmentManager -> com.fasnote.alm.plugin.manage.core.RuntimeEnvironmentManager  [LAZY_SINGLETON]
2025-08-02 17:21:31,901 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,901 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,901 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseFileManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,901 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseFileManager, 实现: com.fasnote.alm.plugin.manage.core.LicenseFileManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,901 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseFileManager -> com.fasnote.alm.plugin.manage.core.LicenseFileManager  [LAZY_SINGLETON]
2025-08-02 17:21:31,901 [Catalina-utility-5] DEBUG com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 管理器组件注册完成
2025-08-02 17:21:31,901 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 通过FrameworkUtil检测到Bundle: com.fasnote.alm.plugin.manage.core.LicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,901 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.api.ILicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,901 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.plugin.manage.core.LicenseManager -> com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,901 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录Bundle映射 - 单例服务: com.fasnote.alm.plugin.manage.api.ILicenseManager, 实现: com.fasnote.alm.plugin.manage.core.LicenseManager, 名称: null, Bundle: com.fasnote.alm.plugin.manage
2025-08-02 17:21:31,901 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册单例服务实现: com.fasnote.alm.plugin.manage.api.ILicenseManager -> com.fasnote.alm.plugin.manage.core.LicenseManager  [LAZY_SINGLETON]
2025-08-02 17:21:31,901 [Catalina-utility-5] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseManager接口绑定配置完成
2025-08-02 17:21:31,901 [Catalina-utility-5] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - LicenseAwareServiceResolver 将通过自动扫描机制注册
2025-08-02 17:21:31,901 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 注册提供者: com.fasnote.alm.plugin.manage.api.LicenseAware
2025-08-02 17:21:31,901 [Catalina-utility-5] INFO  com.fasnote.alm.plugin.manage.injection.module.LicenseModule - 统一许可证模块配置完成（重构后）
2025-08-02 17:21:31,902 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 已安装模块: RefactoredLicenseModule (优先级: 5)
2025-08-02 17:21:31,902 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功安装模块: com.fasnote.alm.plugin.manage.injection.module.LicenseModule
2025-08-02 17:21:31,902 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle 服务扫描完成: com.fasnote.alm.plugin.manage - 耗时: 32ms, 模块: 2, 服务: 0, 错误: 0
2025-08-02 17:21:31,902 [Catalina-utility-5] INFO  com.fasnote.alm.plugin.manage.Activator - LicensePackageScanProvider OSGi 服务注册成功
2025-08-02 17:21:31,902 [Catalina-utility-5] INFO  com.fasnote.alm.plugin.manage.Activator - 扫描包路径: [com.fasnote.alm.plugin.manage.injection.module]
2025-08-02 17:21:31,902 [Catalina-utility-5] INFO  com.fasnote.alm.plugin.manage.Activator - 许可证管理插件启动成功
2025-08-02 17:21:31,903 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 在注解 com.fasnote.alm.plugin.manage.annotation.LicenseImplementation 中发现 @Service 元注解
2025-08-02 17:21:31,903 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 发现服务相关注解: LicenseImplementation 在类 com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer
2025-08-02 17:21:31,903 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 发现服务类: com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu)
2025-08-02 17:21:31,904 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 自动注册服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu)
2025-08-02 17:21:31,906 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 设置默认服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer
2025-08-02 17:21:31,906 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 添加服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer (总数: 1)
2025-08-02 17:21:31,906 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.auth.feishu.FeishuLicensedAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu
2025-08-02 17:21:31,906 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu
2025-08-02 17:21:31,906 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuStateUtils (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,907 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuUserInfoMapper (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,907 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuUserInfoMapper$FeishuUserData (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,907 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuApiClient (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,907 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.FeishuUserInfoMapper$FeishuApiResponse (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,908 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.utils.OAuth2UrlHandler (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,909 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuOAuth2LoginClient$1 (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,910 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuOAuth2DefaultLoginHandler (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,910 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuUserInfoServlet (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,910 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,912 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 在注解 com.fasnote.alm.plugin.manage.annotation.FallbackImplementation 中发现 @Service 元注解
2025-08-02 17:21:31,912 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 发现服务相关注解: FallbackImplementation 在类 com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer
2025-08-02 17:21:31,912 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 发现服务类: com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu)
2025-08-02 17:21:31,912 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - 自动注册服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer (Bundle: com.fasnote.alm.auth.feishu)
2025-08-02 17:21:31,912 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 添加服务实现: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer (总数: 2)
2025-08-02 17:21:31,912 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.auth.feishu.FeishuDefaultAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu
2025-08-02 17:21:31,912 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 记录类到Bundle映射: com.fasnote.alm.auth.feishu.IFeishuAuthenticatorEnhancer -> com.fasnote.alm.auth.feishu
2025-08-02 17:21:31,912 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.FeishuPackageScanProvider (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,912 [Catalina-utility-5] DEBUG com.fasnote.alm.injection.impl.DependencyInjector - 成功加载类: com.fasnote.alm.auth.feishu.Activator (Bundle: com.fasnote.alm.auth.feishu, ClassLoader: org.eclipse.osgi.internal.loader.EquinoxClassLoader)
2025-08-02 17:21:31,912 [Catalina-utility-5] INFO  com.fasnote.alm.injection.impl.DependencyInjector - Bundle 服务扫描完成: com.fasnote.alm.auth.feishu - 耗时: 83ms, 模块: 0, 服务: 2, 错误: 0
2025-08-02 17:21:31,913 [Catalina-utility-5] INFO  com.fasnote.alm.auth.feishu.Activator - FeishuPackageScanProvider OSGi 服务注册成功
2025-08-02 17:21:31,913 [Catalina-utility-5] INFO  com.fasnote.alm.auth.feishu.Activator - 扫描包路径: [com.fasnote.alm.auth.feishu]
2025-08-02 17:21:31,913 [Catalina-utility-5] INFO  com.fasnote.alm.auth.feishu.Activator - Feishu Authentication Plugin started successfully
2025-08-02 17:21:31,913 [Catalina-utility-5] INFO  com.fasnote.alm.auth.feishu.Activator - 飞书插件实现类将通过 OSGi 服务自动扫描注册
2025-08-02 17:21:31,917 [Catalina-utility-1] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 17:21:31,917 [Catalina-utility-4] INFO  org.polarion.svncommons.commentscache.CommentsCache - Initializing comments cache. Id: http://localhost/repo, repository: http://localhost/repo/, url: http://localhost/repo/, cache directory: /opt/polarion/data/workspace/polarion-data/log-messages-cache, cache page size: 100
2025-08-02 17:21:31,936 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@30ba6068] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,944 [Catalina-utility-4] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1780140f] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:31,948 [Catalina-utility-4] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:31,949 [Catalina-utility-4] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 17:21:31,997 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@f7e49b] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:32,000 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:32,019 [Catalina-utility-1] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 17:21:32,023 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4c190267] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:32,025 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:32,030 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@2491aa04] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:32,032 [Catalina-utility-1] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:32,035 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3ced033] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:32,061 [Catalina-utility-1] INFO  com.polarion.portal.velocity.VelocityPathManager - VelocityTemplatesPath=/opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/authapp/, /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/, /opt/polarion/polarion/plugins/com.polarion.alm.wiki_3.22.1/src/main/webapp/, ., /opt/polarion/polarion/plugins/com.polarion.alm.ui_3.22.1/webapp/webui/
2025-08-02 17:21:32,215 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3c1d9114] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:32,218 [Catalina-utility-1] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3f853a53] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:32,236 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@7cbeeca7] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:32,240 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:32,249 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@65dec1f6] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:32,252 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:32,254 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3f1f6171] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:32,269 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@18a1de5d] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:32,274 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@507c1e19] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:32,275 [Catalina-utility-6] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:32,277 [Catalina-utility-5] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4e22d25e] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:32,279 [Catalina-utility-5] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:32,281 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@1bbdd833] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:32,281 [Catalina-utility-6] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@da97587] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:32,283 [Catalina-utility-3] WARN  org.apache.catalina.startup.ContextConfig - Security role name [user] used in an <auth-constraint> without being defined in a <security-role>
2025-08-02 17:21:32,286 [Catalina-utility-3] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@3a4db2b5] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:32,291 [Catalina-utility-2] INFO  com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Spring context refreshed, scanning for prompt variables...
2025-08-02 17:21:32,292 [Catalina-utility-5] INFO  org.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-08-02 17:21:32,294 [Catalina-utility-2] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: polarionTool, Class: com.finething.hesai.ai.util.VelocityPolarionTool, Methods found: 29
2025-08-02 17:21:32,294 [Catalina-utility-2] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: enumTool, Class: com.finething.hesai.ai.util.EnumUtil, Methods found: 1
2025-08-02 17:21:32,294 [Catalina-utility-2] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: linkWorkItemUtil, Class: com.finething.hesai.ai.util.LinkWorkItemUtil, Methods found: 6
2025-08-02 17:21:32,295 [Catalina-utility-2] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Cached prompt variable: polarionContext, Class: com.finething.hesai.ai.service.impl.PolarionContextServiceImpl, Methods found: 4
2025-08-02 17:21:32,296 [Catalina-utility-2] DEBUG com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - No methods with @MethodDescription found for variable: polarionHelper, Class: com.finething.hesai.ai.service.impl.DefaultVariableDescriptionProvider$HelperMethods
2025-08-02 17:21:32,297 [Catalina-utility-2] INFO  com.finething.hesai.ai.service.impl.PromptVariableServiceImpl - Finished scanning. Found 4 prompt variables.
2025-08-02 17:21:32,331 [Catalina-utility-1] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-08-02 17:21:32,380 [Catalina-utility-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Started.
2025-08-02 17:21:32,839 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-08-02 17:21:32,841 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[90]')
2025-08-02 17:21:32,842 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[90]')
2025-08-02 17:21:32,844 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[96]')
2025-08-02 17:21:33,026 [Catalina-utility-1] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-08-02 17:21:33,027 [Catalina-utility-1] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[90]')
2025-08-02 17:21:33,027 [Catalina-utility-1] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[92]')
2025-08-02 17:21:33,118 [main] INFO  org.apache.coyote.ajp.AjpNioProtocol - Starting ProtocolHandler ["ajp-nio-127.0.0.1-8889"]
2025-08-02 17:21:33,122 [main] INFO  com.polarion.psvn.launcher.internal.tomcat.TomcatService - Tomcat is listening on port 8889 using AJP/1.3 protocol with 600000 timeout in millis
2025-08-02 17:21:33,122 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Starting Help Service...
2025-08-02 17:21:33,125 [main] INFO  org.apache.catalina.loader.WebappLoader - Unknown class loader [com.polarion.portal.tomcat.WebAppClassLoader@4334b9fd] of class [class com.polarion.portal.tomcat.WebAppClassLoader]
2025-08-02 17:21:33,129 [main] INFO  com.polarion.psvn.launcher.internal.help.HelpService - Help Service started
2025-08-02 17:21:33,174 [main | u:p] INFO  com.xpn.xwiki.XWiki - xwiki.cfg taken from /WEB-INF/xwiki.cfg because the XWikiConfig variable is not set in the context
2025-08-02 17:21:33,547 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-08-02 17:21:33,633 [Thread-33] INFO  class com.polarion.alm.server.util.ChartExporterStartup - Chart renderer says: Server started on 127.0.0.1:34567
2025-08-02 17:21:33,664 [ajp-nio-127.0.0.1-8889-exec-1 | cID:6a1627cf-7f000001-7115e771-b2faa410] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-08-02 17:21:33,683 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-08-02 17:21:33,684 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
2025-08-02 17:21:33,695 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" has id 6a1627f8-7f000001-7115e771-53c55be4
2025-08-02 17:21:33,695 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to UNSCHEDULED
2025-08-02 17:21:33,696 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "Attachment Indexer" is /opt/polarion/data/workspace/polarion-data/jobs/20250802-1721
2025-08-02 17:21:33,697 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "Attachment Indexer" runs as user "polarion"
2025-08-02 17:21:33,698 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to ACTIVATING
2025-08-02 17:21:33,699 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to WAITING
2025-08-02 17:21:33,702 [main | u:p] INFO  com.polarion.platform.monitoring - Next slow periodic actions will be executed on Sun Aug 03 01:00:33 CST 2025
2025-08-02 17:21:33,702 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.tracker.internal.HttpsConfiguratorStartup successfully initialized
2025-08-02 17:21:33,702 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.ChartExporterStartup successfully initialized
2025-08-02 17:21:33,703 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.wiki.WikiPlugin successfully initialized
2025-08-02 17:21:33,703 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.qcentre.internal.QCentreStartup successfully initialized
2025-08-02 17:21:33,703 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.siemens.polarion.rt.communication.connection.RtCommunicationStartup successfully initialized
2025-08-02 17:21:33,703 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.internal.startup.NotificationServerStartup successfully initialized
2025-08-02 17:21:33,703 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.subterra.index.impl.IndexingJobsStartup successfully initialized
2025-08-02 17:21:33,703 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.ui.server.ServerStartup successfully initialized
2025-08-02 17:21:33,703 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.alm.server.util.FormulaServerStartup successfully initialized
2025-08-02 17:21:33,703 [main | u:p] INFO  com.polarion.platform.internal.startup.StartupService - Startup contributor class com.polarion.platform.monitoring.internal.MonitoringServiceStart successfully initialized
2025-08-02 17:21:33,703 [main] INFO  com.polarion.platform.monitoring - Executing actions from stage POSTBOOT
2025-08-02 17:21:33,704 [main] INFO  com.polarion.platform.monitoring - Executing action 'polarion.info.cache.statistics'
2025-08-02 17:21:33,705 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to RUNNING
2025-08-02 17:21:33,705 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" has id 6a162806-7f000001-7115e771-6784002f
2025-08-02 17:21:33,706 [Thread-37] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to UNSCHEDULED
2025-08-02 17:21:33,707 [Thread-37] INFO  com.polarion.platform.jobs.info - Working directory of root job "DB History Creator" is /opt/polarion/data/workspace/polarion-data/jobs/20250802-1721_0
2025-08-02 17:21:33,708 [Thread-37] INFO  com.polarion.platform.jobs.info - Job "DB History Creator" runs as user "polarion"
2025-08-02 17:21:33,709 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to ACTIVATING
2025-08-02 17:21:33,710 [Thread-37 | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to WAITING
2025-08-02 17:21:33,710 [main] INFO  com.polarion.platform.monitoring - polarion.info.cache.statistics (Statistics of caches) = Statistics of caches 
                         CACHE       HITS     MISSES  HIT_RATIO       GETS       PUTS    REMOVAL   EVICTION 
     attachments-content-cache          0          0          0%          0          0          0          0 
                baseline-cache          0          0          0%          0          0          0          0 
                            bq          0          0          0%          0          0          0          0 
                dao-attachment          0          0          0%          0          0          0          0 
                   dao-comment          0          0          0%          0          0          0          0 
                    dao-global          0          4          0%          4          0          0          0 
                    dao-module          0          0          0%          0          0          0          0 
          dao-moduleattachment          0          0          0%          0          0          0          0 
             dao-modulecomment          0          0          0%          0          0          0          0 
                      dao-plan          0          0          0%          0          0          0          0 
                   dao-project          0          0          0%          0          0          0          0 
              dao-projectgroup          0          6          0%          6          3          0          0 
                  dao-richpage          0          0          0%          0          0          0          0 
        dao-richpageattachment          0          0          0%          0          0          0          0 
                   dao-testrun          0          0          0%          0          0          0          0 
         dao-testrunattachment          0          0          0%          0          0          0          0 
                      dao-user          0          0          0%          0          0          0          0 
                  dao-wikipage          0          0          0%          0          0          0          0 
        dao-wikipageattachment          0          0          0%          0          0          0          0 
                  dao-workitem          0          0          0%          0          0          0          0 
      derived-linked-revisions          0          0          0%          0          0          0          0 
                  formulas-svg          0          0          0%          0          0          0          0 
                github-commits          0          0          0%          0          0          0          0 
            historical-queries          0          0          0%          0          0          0          0 
      historical-queries-sizes          0          0          0%          0          0          0          0 
        oslc-linked-item-cache          0          0          0%          0          0          0          0 
               plan-statistics          0          0          0%          0          0          0          0 
                   rmd-default          4          5         44%          9          7          0          0 
                   ss-combined          0          0          0%          0          0          0          0 
                    ss-context          0          0          0%          0          0          0          0 
                     wiki-page          2          8         20%         10          9          1          0 
              wiki-page-exists          0          0          0%          0          9          1          0 

 [Sat Aug 02 17:21:33 CST 2025]
2025-08-02 17:21:33,712 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to RUNNING
2025-08-02 17:21:33,716 [main] INFO  com.polarion.platform.monitoring - Finished with actions from stage POSTBOOT: {OK=1}
2025-08-02 17:21:33,716 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.43 s. ]
2025-08-02 17:21:33,717 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.257 s [89% info (158x)] (168x)
2025-08-02 17:21:33,717 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 17:21:33,717 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.76 s. ]
2025-08-02 17:21:33,717 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 17:21:33,718 [Thread-36] INFO  com.polarion.core.util.process.JavaRunner - Executing /Users/<USER>/Library/Java/JavaVirtualMachines/ms-11.0.27/Contents/Home/bin/java
  -- args [-jar, /opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar, --server.port=40608, --jwksUrl=http://localhost/polarion/.well-known/jwks.json]
  -- env null
  -- dir /var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess11088893250704845353.tmp
2025-08-02 17:21:33,722 [PreLoadDataService] INFO  com.polarion.psvn.launcher.internal.data.PreLoadDataService - Preloading data ...
2025-08-02 17:21:33,722 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - State of job "Attachment Indexer" changed to FINISHED
2025-08-02 17:21:33,724 [Worker-0: Attachment Indexer | u:p] INFO  com.polarion.platform.jobs.info - Status of job "Attachment Indexer" is OK
2025-08-02 17:21:33,793 [Notification-Worker-6 | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: WorkItem
2025-08-02 17:21:33,803 [Notification-Worker-6 | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: WorkItem
2025-08-02 17:21:33,907 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661a858a06844_0_661a858a06844_0_: finished. Total: 0.185 s, CPU [user: 0.112 s, system: 0.0127 s], Allocated memory: 16.1 MB, resolve: 0.0532 s [96% User (2x)] (4x), Lucene: 0.0143 s [100% search (1x)] (1x), svn: 0.0138 s [56% getLatestRevision (2x), 36% testConnection (1x)] (5x)
2025-08-02 17:21:33,925 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: TimePoint
2025-08-02 17:21:33,926 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: TimePoint
2025-08-02 17:21:33,928 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Category
2025-08-02 17:21:33,930 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Category
2025-08-02 17:21:34,020 [Activities-Bulk-Publisher] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Activities
2025-08-02 17:21:34,080 [Thread-43] INFO  class com.polarion.alm.server.util.FormulaServerStartup - Formula renderer says: Server started on 127.0.0.1:34568
2025-08-02 17:21:34,239 [Thread-40] INFO  NotificationService - 
2025-08-02 17:21:34,239 [Thread-40] INFO  NotificationService -   .   ____          _            __ _ _
2025-08-02 17:21:34,239 [Thread-40] INFO  NotificationService -  /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
2025-08-02 17:21:34,239 [Thread-40] INFO  NotificationService - ( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
2025-08-02 17:21:34,239 [Thread-40] INFO  NotificationService -  \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
2025-08-02 17:21:34,240 [Thread-40] INFO  NotificationService -   '  |____| .__|_| |_|_| |_\__, | / / / /
2025-08-02 17:21:34,240 [Thread-40] INFO  NotificationService -  =========|_|==============|___/=/_/_/_/
2025-08-02 17:21:34,241 [Thread-40] INFO  NotificationService -  :: Spring Boot ::                (v2.6.6)
2025-08-02 17:21:34,242 [Thread-40] INFO  NotificationService - 
2025-08-02 17:21:34,312 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Starting Application using Java 11.0.27 on zhangwentiandeMac-mini-2.local with PID 50281 (/opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar started by zhangwentian in /private/var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess11088893250704845353.tmp)
2025-08-02 17:21:34,312 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - No active profile set, falling back to 1 default profile: "default"
2025-08-02 17:21:34,413 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 25, for prototypes: WorkItem; with days range: 180d, took  [ TIME 0.615 s. ] 
2025-08-02 17:21:34,414 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.636 s, CPU [user: 0.00642 s, system: 0.00143 s], Allocated memory: 356.3 kB, transactions: 1
2025-08-02 17:21:34,415 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, notification worker: 0.195 s [97% RevisionActivityCreator (2x)] (6x), resolve: 0.065 s [94% User (3x)] (6x), Lucene: 0.0323 s [44% search (1x), 34% refresh (1x), 21% add (1x)] (3x), Incremental Baseline: 0.026 s [100% WorkItem (24x)] (24x), svn: 0.0196 s [54% getLatestRevision (3x), 41% testConnection (2x)] (7x), ObjectMaps: 0.0122 s [75% getPrimaryObjectLocation (2x), 14% getPrimaryObjectProperty (1x)] (7x), persistence listener: 0.0119 s [85% indexRefreshPersistenceListener (1x)] (7x)
2025-08-02 17:21:34,415 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.703 s, CPU [user: 0.135 s, system: 0.0216 s], Allocated memory: 18.8 MB, transactions: 25, svn: 0.558 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0409 s [81% buildBaselineSnapshots (1x)] (26x)
2025-08-02 17:21:34,417 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - State of job "DB History Creator" changed to FINISHED
2025-08-02 17:21:34,418 [Worker-1: DB History Creator | u:p] INFO  com.polarion.platform.jobs.info - Status of job "DB History Creator" is OK
2025-08-02 17:21:34,696 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.ObjectIndex - Preloading of baselines: 25, for prototypes: WorkItem; with days range: 180d, took  [ TIME 0.283 s. ] 
2025-08-02 17:21:34,714 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a858a07045_0_661a858a07045_0_: finished. Total: 0.99 s, CPU [user: 0.298 s, system: 0.0775 s], Allocated memory: 51.6 MB, svn: 0.584 s [47% getDatedRevision (181x), 32% getDir2 content (25x), 20% getFile content (119x)] (328x), resolve: 0.343 s [100% Category (117x)] (117x), ObjectMaps: 0.121 s [43% getPrimaryObjectProperty (117x), 31% getPrimaryObjectLocation (117x), 26% getLastPromoted (117x)] (473x)
2025-08-02 17:21:34,717 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: ProjectGroup
2025-08-02 17:21:34,719 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: ProjectGroup
2025-08-02 17:21:34,735 [Thread-40] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 40608 (http)
2025-08-02 17:21:34,739 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: Project
2025-08-02 17:21:34,740 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-40608"]
2025-08-02 17:21:34,740 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-02 17:21:34,740 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-02 17:21:34,741 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: Project
2025-08-02 17:21:34,777 [Thread-40] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-02 17:21:34,777 [Thread-40] INFO  NotificationService - [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 441 ms
2025-08-02 17:21:34,788 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - creating index writer for index: User
2025-08-02 17:21:34,790 [PreLoadDataService | u:p] INFO  com.polarion.subterra.index.impl.lucene.LuceneIndexManager - searcher manager created for index: User
2025-08-02 17:21:34,795 [Thread-40] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing AtmosphereFramework
2025-08-02 17:21:34,951 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a858b10c48_0_661a858b10c48_0_: finished. Total: 0.163 s, CPU [user: 0.0681 s, system: 0.012 s], Allocated memory: 8.0 MB, resolve: 0.0617 s [100% User (9x)] (9x), RepositoryConfigService: 0.0596 s [51% getReadConfiguration (180x), 49% getReadUserConfiguration (10x)] (190x), svn: 0.0572 s [57% info (21x), 33% getFile content (16x)] (39x), ObjectMaps: 0.0193 s [59% getPrimaryObjectProperty (8x), 22% getLastPromoted (8x)] (32x), GC: 0.017 s [100% G1 Young Generation (1x)] (1x)
2025-08-02 17:21:35,022 [Thread-40] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-40608"]
2025-08-02 17:21:35,052 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using org.atmosphere.cpr.DefaultAnnotationProcessor for processing annotation
2025-08-02 17:21:35,052 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.DefaultAnnotationProcessor - AnnotationProcessor class org.atmosphere.cpr.DefaultAnnotationProcessor$BytecodeBasedAnnotationProcessor being used
2025-08-02 17:21:35,067 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AnnotationHandler - Found Annotation in class com.siemens.polarion.service.notification.NotificationService being scanned: interface org.atmosphere.config.service.ManagedService
2025-08-02 17:21:35,071 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.AtmosphereResourceLifecycleInterceptor
2025-08-02 17:21:35,071 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.client.TrackMessageSizeInterceptor
2025-08-02 17:21:35,072 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.SuspendTrackerInterceptor
2025-08-02 17:21:35,072 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.config.managed.ManagedServiceInterceptor
2025-08-02 17:21:35,077 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class com.siemens.polarion.service.notification.JwtVerificationInterceptor
2025-08-02 17:21:35,082 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.util.ForkJoinPool - Using ForkJoinPool  java.util.concurrent.ForkJoinPool. Set the org.atmosphere.cpr.broadcaster.maxAsyncWriteThreads to -1 to fully use its power.
2025-08-02 17:21:35,085 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler mapped to context-path /notification and Broadcaster Class org.atmosphere.cpr.DefaultBroadcaster
2025-08-02 17:21:35,085 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor [Atmosphere LifeCycle,  Track Message Size Interceptor using |, UUID Tracking Interceptor, @ManagedService Interceptor, @Service Event Listeners, com.siemens.polarion.service.notification.JwtVerificationInterceptor] mapped to AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler
2025-08-02 17:21:35,094 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Auto detecting WebSocketHandler in /WEB-INF/classes/
2025-08-02 17:21:35,096 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed WebSocketProtocol org.atmosphere.websocket.protocol.SimpleHttpProtocol 
2025-08-02 17:21:35,099 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.container.JSR356AsyncSupport - JSR 356 Mapping path /notification
2025-08-02 17:21:35,105 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installing Default AtmosphereInterceptors
2025-08-02 17:21:35,106 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CorsInterceptor : CORS Interceptor Support
2025-08-02 17:21:35,106 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CacheHeadersInterceptor : Default Response's Headers Interceptor
2025-08-02 17:21:35,106 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.PaddingAtmosphereInterceptor : Browser Padding Interceptor Support
2025-08-02 17:21:35,106 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.AndroidAtmosphereInterceptor : Android Interceptor Support
2025-08-02 17:21:35,107 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.HeartbeatInterceptor : Heartbeat Interceptor Support
2025-08-02 17:21:35,107 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.SSEAtmosphereInterceptor : SSE Interceptor Support
2025-08-02 17:21:35,107 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JSONPAtmosphereInterceptor : JSONP Interceptor Support
2025-08-02 17:21:35,108 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JavaScriptProtocol : Atmosphere JavaScript Protocol
2025-08-02 17:21:35,108 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor : org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor
2025-08-02 17:21:35,108 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.OnDisconnectInterceptor : Browser disconnection detection
2025-08-02 17:21:35,108 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.IdleResourceInterceptor : org.atmosphere.interceptor.IdleResourceInterceptor
2025-08-02 17:21:35,108 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Set org.atmosphere.cpr.AtmosphereInterceptor.disableDefaults to disable them.
2025-08-02 17:21:35,108 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor CORS Interceptor Support with priority FIRST_BEFORE_DEFAULT 
2025-08-02 17:21:35,110 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Default Response's Headers Interceptor with priority AFTER_DEFAULT 
2025-08-02 17:21:35,110 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser Padding Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 17:21:35,110 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Android Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 17:21:35,110 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.interceptor.HeartbeatInterceptor - HeartbeatInterceptor configured with padding value 'X', client frequency 30 seconds and server frequency 120 seconds
2025-08-02 17:21:35,110 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Heartbeat Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 17:21:35,110 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor SSE Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 17:21:35,110 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor JSONP Interceptor Support with priority AFTER_DEFAULT 
2025-08-02 17:21:35,110 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Atmosphere JavaScript Protocol with priority AFTER_DEFAULT 
2025-08-02 17:21:35,110 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor with priority AFTER_DEFAULT 
2025-08-02 17:21:35,110 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser disconnection detection with priority AFTER_DEFAULT 
2025-08-02 17:21:35,110 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.IdleResourceInterceptor with priority BEFORE_DEFAULT 
2025-08-02 17:21:35,110 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using EndpointMapper class org.atmosphere.util.DefaultEndpointMapper
2025-08-02 17:21:35,110 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterCache: org.atmosphere.cache.UUIDBroadcasterCache
2025-08-02 17:21:35,110 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Default Broadcaster Class: org.atmosphere.cpr.DefaultBroadcaster
2025-08-02 17:21:35,110 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Shared List Resources: false
2025-08-02 17:21:35,110 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Polling Wait Time 100
2025-08-02 17:21:35,110 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Shared ExecutorService supported: true
2025-08-02 17:21:35,110 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Messaging ExecutorService Pool Size unavailable - Not instance of ThreadPoolExecutor
2025-08-02 17:21:35,110 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Async I/O Thread Pool Size: 200
2025-08-02 17:21:35,111 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterFactory: org.atmosphere.cpr.DefaultBroadcasterFactory
2025-08-02 17:21:35,111 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using AtmosphereResurceFactory: org.atmosphere.cpr.DefaultAtmosphereResourceFactory
2025-08-02 17:21:35,111 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using WebSocketProcessor: org.atmosphere.websocket.DefaultWebSocketProcessor
2025-08-02 17:21:35,113 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Invoke AtmosphereInterceptor on WebSocket message true
2025-08-02 17:21:35,114 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - HttpSession supported: false
2025-08-02 17:21:35,114 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using Spring Web ObjectFactory for dependency injection and object creation
2025-08-02 17:21:35,114 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using async support: org.atmosphere.container.JSR356AsyncSupport running under container: Apache Tomcat/9.0.60 using javax.servlet/3.0 and jsr356/WebSocket API
2025-08-02 17:21:35,114 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere Framework 2.6.4 started.
2025-08-02 17:21:35,114 [Thread-40] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 
2025-08-02 17:21:35,114 [Thread-40] INFO  NotificationService - 
2025-08-02 17:21:35,114 [Thread-40] INFO  NotificationService - 	For Atmosphere Framework Commercial Support, visit 
2025-08-02 17:21:35,114 [Thread-40] INFO  NotificationService - 	http://www.async-io.org/ or send an <NAME_EMAIL>
2025-08-02 17:21:35,114 [Thread-40] INFO  NotificationService - 
2025-08-02 17:21:35,117 [Thread-40] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 40608 (http) with context path ''
2025-08-02 17:21:35,120 [Thread-40] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Started Application in 1.106 seconds (JVM running for 1.39)
2025-08-02 17:21:35,152 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testtype) created
2025-08-02 17:21:35,157 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (subtype) created
2025-08-02 17:21:35,160 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a858b4684a_0_661a858b4684a_0_: finished. Total: 0.158 s, CPU [user: 0.0472 s, system: 0.00768 s], Allocated memory: 19.8 MB, svn: 0.128 s [76% getDir2 content (17x), 24% getFile content (44x)] (62x), RepositoryConfigService: 0.047 s [98% getReadConfiguration (170x)] (192x)
2025-08-02 17:21:35,357 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (YesNo) created
2025-08-02 17:21:35,363 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (software_VerificationMethod) created
2025-08-02 17:21:35,367 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checklist) created
2025-08-02 17:21:35,371 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonreqproperty) created
2025-08-02 17:21:35,376 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectoriented) created
2025-08-02 17:21:35,376 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submodule) created
2025-08-02 17:21:35,378 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (yesno) created
2025-08-02 17:21:35,378 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (cICategory) created
2025-08-02 17:21:35,379 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (wpFormat) created
2025-08-02 17:21:35,380 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (trigger) created
2025-08-02 17:21:35,384 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ASILLevel) created
2025-08-02 17:21:35,385 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CSRelated) created
2025-08-02 17:21:35,389 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_Module) created
2025-08-02 17:21:35,399 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (规格对象类型) created
2025-08-02 17:21:35,402 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (jenkins_job) created
2025-08-02 17:21:35,402 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (truefalse) created
2025-08-02 17:21:35,403 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (takeOnGroups) created
2025-08-02 17:21:35,409 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasetype) created
2025-08-02 17:21:35,413 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processGroup) created
2025-08-02 17:21:35,416 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeManagement) created
2025-08-02 17:21:35,422 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (seriousness) created
2025-08-02 17:21:35,426 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softReqClass) created
2025-08-02 17:21:35,431 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWDetailDesign) created
2025-08-02 17:21:35,433 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PhaseChecklists) created
2025-08-02 17:21:35,435 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (PassNotpass) created
2025-08-02 17:21:35,437 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineType) created
2025-08-02 17:21:35,439 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (boolYesOrNo) created
2025-08-02 17:21:35,440 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testlevel) created
2025-08-02 17:21:35,443 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (source) created
2025-08-02 17:21:35,446 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectType) created
2025-08-02 17:21:35,447 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atppblversion) created
2025-08-02 17:21:35,449 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (aSIL) created
2025-08-02 17:21:35,452 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (EE) created
2025-08-02 17:21:35,454 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueType) created
2025-08-02 17:21:35,456 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SYS_reqClassification) created
2025-08-02 17:21:35,460 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (oem_2Status) created
2025-08-02 17:21:35,462 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (class) created
2025-08-02 17:21:35,464 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (promotionState) created
2025-08-02 17:21:35,466 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (git_project) created
2025-08-02 17:21:35,470 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (storageType) created
2025-08-02 17:21:35,472 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issueproperty) created
2025-08-02 17:21:35,476 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (commonissueclass) created
2025-08-02 17:21:35,478 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (AgreeDisagree) created
2025-08-02 17:21:35,480 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SA_Category) created
2025-08-02 17:21:35,482 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relevance) created
2025-08-02 17:21:35,492 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (implementationPhase) created
2025-08-02 17:21:35,494 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplier_2Status) created
2025-08-02 17:21:35,500 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Testtype) created
2025-08-02 17:21:35,503 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (conf_baselineTime) created
2025-08-02 17:21:35,509 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (levelneed) created
2025-08-02 17:21:35,510 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (finalresult) created
2025-08-02 17:21:35,512 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testability) created
2025-08-02 17:21:35,515 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solution) created
2025-08-02 17:21:35,517 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Responsible) created
2025-08-02 17:21:35,519 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationstatus) created
2025-08-02 17:21:35,527 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsiassigngroup) created
2025-08-02 17:21:35,529 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqCategory) created
2025-08-02 17:21:35,531 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLineName) created
2025-08-02 17:21:35,533 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskType) created
2025-08-02 17:21:35,539 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeReason) created
2025-08-02 17:21:35,542 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (objectmodule) created
2025-08-02 17:21:35,546 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseOutputMethod) created
2025-08-02 17:21:35,549 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SoftwareFeature) created
2025-08-02 17:21:35,555 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ResponsibleGroup) created
2025-08-02 17:21:35,558 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hsifunctionmodule) created
2025-08-02 17:21:35,560 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (FwReqSource) created
2025-08-02 17:21:35,562 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (occurPhase) created
2025-08-02 17:21:35,564 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (compiletask) created
2025-08-02 17:21:35,566 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBVerificationMethod) created
2025-08-02 17:21:35,568 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (functionmodule) created
2025-08-02 17:21:35,570 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (variant) created
2025-08-02 17:21:35,571 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Fusatype) created
2025-08-02 17:21:35,581 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareversion) created
2025-08-02 17:21:35,584 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (appversion) created
2025-08-02 17:21:35,586 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casefirstmodule) created
2025-08-02 17:21:35,588 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditType) created
2025-08-02 17:21:35,591 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Samplestage) created
2025-08-02 17:21:35,594 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (casesecondmodule) created
2025-08-02 17:21:35,596 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issue_source) created
2025-08-02 17:21:35,598 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ifNeedRegressionTesting) created
2025-08-02 17:21:35,600 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (atpsfsversion) created
2025-08-02 17:21:35,602 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CustomerAllocation) created
2025-08-02 17:21:35,605 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (issuesubclass) created
2025-08-02 17:21:35,607 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_importance) created
2025-08-02 17:21:35,609 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reviewMethod) created
2025-08-02 17:21:35,611 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QANC_findType) created
2025-08-02 17:21:35,613 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (editType) created
2025-08-02 17:21:35,617 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testingobjects) created
2025-08-02 17:21:35,619 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcaselevel) created
2025-08-02 17:21:35,621 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (supplierproblem) created
2025-08-02 17:21:35,623 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqattribute) created
2025-08-02 17:21:35,625 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (fsigroup) created
2025-08-02 17:21:35,628 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (project_reqsource) created
2025-08-02 17:21:35,630 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (preset) created
2025-08-02 17:21:35,633 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Mechverificationmethod) created
2025-08-02 17:21:35,635 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CPMToTPM) created
2025-08-02 17:21:35,637 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBType) created
2025-08-02 17:21:35,639 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testcasesign) created
2025-08-02 17:21:35,641 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationphase) created
2025-08-02 17:21:35,650 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (processArea) created
2025-08-02 17:21:35,653 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (artifactType) created
2025-08-02 17:21:35,658 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Classification) created
2025-08-02 17:21:35,661 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verificationmethod) created
2025-08-02 17:21:35,663 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (changeType) created
2025-08-02 17:21:35,665 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (hardwareAndSoftwareSubType) created
2025-08-02 17:21:35,668 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWIntegrationVerificationMethod) created
2025-08-02 17:21:35,670 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (category) created
2025-08-02 17:21:35,678 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQBCategory) created
2025-08-02 17:21:35,683 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softreqclass) created
2025-08-02 17:21:35,685 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestMethod) created
2025-08-02 17:21:35,689 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reType) created
2025-08-02 17:21:35,693 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (VerificationCriteria) created
2025-08-02 17:21:35,696 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (baseLinechecklist) created
2025-08-02 17:21:35,698 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Category) created
2025-08-02 17:21:35,702 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (SWUnitTestDerivingMethods) created
2025-08-02 17:21:35,705 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (firmware_Category) created
2025-08-02 17:21:35,707 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testMethod) created
2025-08-02 17:21:35,714 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (QAPorcessAreas) created
2025-08-02 17:21:35,722 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (findSource) created
2025-08-02 17:21:35,728 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a858b6e04b_0_661a858b6e04b_0_: finished. Total: 0.567 s, CPU [user: 0.289 s, system: 0.0202 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.421 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.275 s [62% getFile content (412x), 38% getDir2 content (21x)] (434x)
2025-08-02 17:21:35,797 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (tshirt-sizes) created
2025-08-02 17:21:35,799 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (reqtype) created
2025-08-02 17:21:35,952 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Feasibility) created
2025-08-02 17:21:35,954 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (locaMod) created
2025-08-02 17:21:35,955 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseType) created
2025-08-02 17:21:35,957 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ControlLevel) created
2025-08-02 17:21:35,958 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (NCitemSev) created
2025-08-02 17:21:35,959 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (signType) created
2025-08-02 17:21:35,962 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (WBSCategory) created
2025-08-02 17:21:35,963 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseEnv) created
2025-08-02 17:21:35,965 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verifiability) created
2025-08-02 17:21:35,966 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ProjectUser) created
2025-08-02 17:21:35,968 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (standardReq) created
2025-08-02 17:21:35,969 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (statusa) created
2025-08-02 17:21:35,970 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@WorkItems[type:configurationitemversion]) created
2025-08-02 17:21:35,971 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (CIRevisionStatus) created
2025-08-02 17:21:35,973 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (dogTimeout) created
2025-08-02 17:21:35,975 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (REQCategory) created
2025-08-02 17:21:35,978 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proStage) created
2025-08-02 17:21:35,979 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (BaselineType) created
2025-08-02 17:21:35,981 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (custConfStat) created
2025-08-02 17:21:35,982 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sofReqVer) created
2025-08-02 17:21:35,985 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Source) created
2025-08-02 17:21:35,987 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scCategory) created
2025-08-02 17:21:35,988 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseType) created
2025-08-02 17:21:35,991 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solAdv) created
2025-08-02 17:21:35,992 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseMet) created
2025-08-02 17:21:35,993 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMeth) created
2025-08-02 17:21:35,995 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseMe) created
2025-08-02 17:21:35,996 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (softTestCaseEnv) created
2025-08-02 17:21:36,000 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTarget) created
2025-08-02 17:21:36,002 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@ReviewForm) created
2025-08-02 17:21:36,002 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseType) created
2025-08-02 17:21:36,004 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (@Collection) created
2025-08-02 17:21:36,004 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (submissionStage) created
2025-08-02 17:21:36,006 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseMet) created
2025-08-02 17:21:36,007 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandType) created
2025-08-02 17:21:36,008 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (swTestCaseMet) created
2025-08-02 17:21:36,009 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (taskUrgen) created
2025-08-02 17:21:36,011 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (solveMethod) created
2025-08-02 17:21:36,012 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (audMethod) created
2025-08-02 17:21:36,013 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (desStat) created
2025-08-02 17:21:36,016 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (scType) created
2025-08-02 17:21:36,017 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseType) created
2025-08-02 17:21:36,018 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (releaseType) created
2025-08-02 17:21:36,020 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (unitTestCaseEnv) created
2025-08-02 17:21:36,021 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (targetStage) created
2025-08-02 17:21:36,028 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (ClassificationType) created
2025-08-02 17:21:36,030 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testItem) created
2025-08-02 17:21:36,031 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (InfoSecurity) created
2025-08-02 17:21:36,032 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (Verification) created
2025-08-02 17:21:36,034 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMod) created
2025-08-02 17:21:36,035 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (verMethod) created
2025-08-02 17:21:36,036 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (diagramCategory) created
2025-08-02 17:21:36,037 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (assSubsystem) created
2025-08-02 17:21:36,039 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (OccurrenceProbability) created
2025-08-02 17:21:36,040 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (developmentMethod) created
2025-08-02 17:21:36,041 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (portType) created
2025-08-02 17:21:36,043 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (checkType) created
2025-08-02 17:21:36,044 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (demandStatus) created
2025-08-02 17:21:36,045 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (important) created
2025-08-02 17:21:36,046 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (triggerMec) created
2025-08-02 17:21:36,047 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (sysTestCaseTy) created
2025-08-02 17:21:36,048 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (recentPre) created
2025-08-02 17:21:36,050 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestCaseDesignMethod) created
2025-08-02 17:21:36,051 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (testCasePri) created
2025-08-02 17:21:36,054 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (relObj) created
2025-08-02 17:21:36,055 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (proSer) created
2025-08-02 17:21:36,056 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (TestProblemType) created
2025-08-02 17:21:36,057 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (chipName) created
2025-08-02 17:21:36,058 [PreLoadDataService | u:p] INFO  com.polarion.platform.persistence.internal.FactoriesProvider - Factory for unknown enumId (auditTiming) created
2025-08-02 17:21:36,061 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661a858c1544e_0_661a858c1544e_0_: finished. Total: 0.231 s, CPU [user: 0.0947 s, system: 0.00536 s], Allocated memory: 386.8 MB, svn: 0.146 s [50% getDir2 content (21x), 50% getFile content (185x)] (207x), RepositoryConfigService: 0.146 s [97% getReadConfiguration (2787x)] (3025x)
2025-08-02 17:21:36,111 [PreLoadDataService | u:p] ERROR com.polarion.subterra.base.data.model.CustomField - Unknown custom field type 'enum' - using 'string' - for field with id 'taskType' for 'WorkItem task /default/WBS'
2025-08-02 17:21:36,117 [PreLoadDataService] INFO  com.polarion.psvn.launcher.internal.data.PreLoadDataService - Preloading data FINISHED took  [ TIME 2.4 s. ]
2025-08-02 17:21:36,117 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.4 s, CPU [user: 0.895 s, system: 0.137 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.38 s [42% getDir2 content (133x), 33% getFile content (865x), 20% getDatedRevision (181x)] (1224x), RepositoryConfigService: 0.754 s [93% getReadConfiguration (12165x)] (12859x), resolve: 0.448 s [77% Category (117x), 14% User (9x)] (139x), ObjectMaps: 0.155 s [48% getPrimaryObjectProperty (131x), 28% getPrimaryObjectLocation (137x), 24% getLastPromoted (131x)] (536x)
2025-08-02 17:21:36,117 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 61, svn: 1.93 s [43% getDatedRevision (362x), 30% getDir2 content (133x), 24% getFile content (865x)] (1407x), RepositoryConfigService: 0.754 s [93% getReadConfiguration (12165x)] (12859x), resolve: 0.448 s [77% Category (117x), 14% User (10x)] (140x), ObjectMaps: 0.155 s [48% getPrimaryObjectProperty (131x), 28% getPrimaryObjectLocation (137x), 24% getLastPromoted (131x)] (536x)
2025-08-02 17:21:43,728 [Thread-36] INFO  NotificationService - Notification service was started successfully.
2025-08-02 17:22:19,407 [ajp-nio-127.0.0.1-8889-exec-2 | cID:6a16d9a1-7f000001-7115e771-1c3300a6] WARN  com.polarion.psvn.launcher.SystemErrLogger - Something is written to System.err, the original message follows.
com.polarion.core.util.ThrowableUtils$StackTraceException: null
	at com.polarion.core.util.ThrowableUtils.getTrace(ThrowableUtils.java:109) ~[util.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.log(SystemErrLogger.java:264) [launcher.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.println(SystemErrLogger.java:108) [launcher.jar:?]
	at com.checklist.repository.BaseJsonFileRepository.findAll(BaseJsonFileRepository.java:318) [com.fasnote.alm.checklist/:?]
	at com.checklist.repository.BaseJsonFileRepository.findBy(BaseJsonFileRepository.java:428) [com.fasnote.alm.checklist/:?]
	at com.checklist.repository.ChecklistTemplateRepository.findByType(ChecklistTemplateRepository.java:58) [com.fasnote.alm.checklist/:?]
	at com.checklist.service.ChecklistTemplateService.getTemplatesByType(ChecklistTemplateService.java:162) [com.fasnote.alm.checklist/:?]
	at com.checklist.controller.ChecklistTemplateController.getTemplatesByType(ChecklistTemplateController.java:65) [com.fasnote.alm.checklist/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634) [javax.servlet_4.0.0.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.SecurityCheckFilter.doFilter(SecurityCheckFilter.java:46) [portal-tomcat.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-02 17:22:19,408 [ajp-nio-127.0.0.1-8889-exec-2 | cID:6a16d9a1-7f000001-7115e771-1c3300a6] WARN  com.polarion.psvn.launcher.SystemErrLogger - 读取文件失败: /Users/<USER>/eclipse-workspace/com.fasnote.alm.checklist/data/templates/template_demo_design_review.json, 错误: null
2025-08-02 17:22:19,416 [ajp-nio-127.0.0.1-8889-exec-2 | cID:6a16d9a1-7f000001-7115e771-1c3300a6] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?_t=1754126539134': Total: 0.246 s, CPU [user: 0.0567 s, system: 0.0358 s], Allocated memory: 3.2 MB, transactions: 0
2025-08-02 17:22:19,426 [ajp-nio-127.0.0.1-8889-exec-3 | cID:6a16d9a1-7f000001-7115e771-54725119] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?_t=1754126539133': Total: 0.256 s, CPU [user: 0.0591 s, system: 0.0375 s], Allocated memory: 2.4 MB, transactions: 0
2025-08-02 17:22:21,130 [ajp-nio-127.0.0.1-8889-exec-4 | cID:6a16e0de-7f000001-7115e771-a10a8f0c] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/template-config/template_demo_code_review?_t=1754126540996': Total: 0.107 s, CPU [user: 0.0221 s, system: 0.00522 s], Allocated memory: 1.0 MB, transactions: 0
2025-08-02 17:26:33,324 [Thread-32] INFO  class com.polarion.alm.server.util.ChartExporterStartup - Chart renderer was started successfully.
2025-08-02 17:26:33,815 [Thread-38] INFO  class com.polarion.alm.server.util.FormulaServerStartup - Formula renderer was started successfully.
2025-08-02 17:31:33,708 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 17:31:33,716 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 17:31:33,724 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 17:31:33 CST 2025]
2025-08-02 17:31:33,738 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 17:31:33,739 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,417.66 GB
 [Sat Aug 02 17:31:33 CST 2025]
2025-08-02 17:31:33,938 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 17:31:33,938 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 17:31:33 CST 2025]
2025-08-02 17:31:33,941 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 17:31:33,941 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 15; Time: 0.111 s.
 [Sat Aug 02 17:31:33 CST 2025]
2025-08-02 17:31:33,943 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 17:31:33,944 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 17:31:33 CST 2025]
2025-08-02 17:31:34,066 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 17:31:34,067 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 17:31:34 CST 2025]
2025-08-02 17:31:34,201 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 17:41:33,714 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 17:41:33,716 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 17:41:33,717 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 17:41:33 CST 2025]
2025-08-02 17:41:33,731 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 17:41:33,731 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,417.61 GB
 [Sat Aug 02 17:41:33 CST 2025]
2025-08-02 17:41:33,931 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 17:41:33,931 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 17:41:33 CST 2025]
2025-08-02 17:41:33,933 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 17:41:33,933 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 15; Time: 0.111 s.
 [Sat Aug 02 17:41:33 CST 2025]
2025-08-02 17:41:33,935 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 17:41:33,935 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 17:41:33 CST 2025]
2025-08-02 17:41:34,050 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 17:41:34,051 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 17:41:34 CST 2025]
2025-08-02 17:41:34,235 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 17:42:05,837 [ajp-nio-127.0.0.1-8889-exec-1 | cID:6a28f490-7f000001-7115e771-331eb98f] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates': Total: 0.122 s, CPU [user: 0.0319 s, system: 0.051 s], Allocated memory: 1.0 MB, transactions: 0
2025-08-02 17:42:06,028 [ajp-nio-127.0.0.1-8889-exec-2 | cID:6a28f540-7f000001-7115e771-4f319ab4] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/template-config/template_6c4b41a2ccd946f0b769317e89e5c5ca': Total: 0.14 s, CPU [user: 0.0217 s, system: 0.0132 s], Allocated memory: 628.1 kB, transactions: 0
2025-08-02 17:42:21,641 [ajp-nio-127.0.0.1-8889-exec-9 | cID:6a2932c6-7f000001-7115e771-d437f199] WARN  com.polarion.psvn.launcher.SystemErrLogger - Something is written to System.err, the original message follows.
com.polarion.core.util.ThrowableUtils$StackTraceException: null
	at com.polarion.core.util.ThrowableUtils.getTrace(ThrowableUtils.java:109) ~[util.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.log(SystemErrLogger.java:264) [launcher.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.println(SystemErrLogger.java:108) [launcher.jar:?]
	at com.checklist.repository.BaseJsonFileRepository.findAll(BaseJsonFileRepository.java:318) [com.fasnote.alm.checklist/:?]
	at com.checklist.service.ChecklistTemplateService.getAllTemplates(ChecklistTemplateService.java:147) [com.fasnote.alm.checklist/:?]
	at com.checklist.controller.ChecklistTemplateController.getAllTemplates(ChecklistTemplateController.java:37) [com.fasnote.alm.checklist/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634) [javax.servlet_4.0.0.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.SecurityCheckFilter.doFilter(SecurityCheckFilter.java:46) [portal-tomcat.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-02 17:42:21,646 [ajp-nio-127.0.0.1-8889-exec-9 | cID:6a2932c6-7f000001-7115e771-d437f199] WARN  com.polarion.psvn.launcher.SystemErrLogger - 读取文件失败: /Users/<USER>/eclipse-workspace/com.fasnote.alm.checklist/data/templates/template_demo_code_review.json, 错误: null
2025-08-02 17:44:24,964 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.112646484375
2025-08-02 17:44:30,114 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.32 s, CPU [user: 0.00217 s, system: 0.00606 s], Allocated memory: 130.3 kB, transactions: 0, PullingJob: 0.212 s [100% collectChanges (1x)] (1x), svn: 0.212 s [100% getLatestRevision (1x)] (1x)
2025-08-02 17:44:34,974 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.532470703125
2025-08-02 17:44:44,970 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.3515625
2025-08-02 17:44:54,970 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.1978515625
2025-08-02 17:45:04,968 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.021630859375
2025-08-02 17:45:24,056 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.197 s, CPU [user: 0.0019 s, system: 0.0072 s], Allocated memory: 130.1 kB, transactions: 0, PullingJob: 0.174 s [100% collectChanges (1x)] (1x), svn: 0.17 s [100% getLatestRevision (1x)] (1x)
2025-08-02 17:45:25,305 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.48623046875
2025-08-02 17:45:34,971 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.364013671875
2025-08-02 17:45:44,966 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.225048828125
2025-08-02 17:45:54,967 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.0994140625
2025-08-02 17:46:04,969 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.21279296875
2025-08-02 17:46:14,962 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.07177734375
2025-08-02 17:51:24,967 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.07802734375
2025-08-02 17:51:33,721 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 17:51:33,725 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 17:51:33,726 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 17:51:33 CST 2025]
2025-08-02 17:51:33,740 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 17:51:33,740 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,416.61 GB
 [Sat Aug 02 17:51:33 CST 2025]
2025-08-02 17:51:33,947 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 17:51:33,947 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 17:51:33 CST 2025]
2025-08-02 17:51:33,951 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 17:51:33,952 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 16; Time: 0.16 s.
 [Sat Aug 02 17:51:33 CST 2025]
2025-08-02 17:51:33,957 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 17:51:33,957 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 17:51:33 CST 2025]
2025-08-02 17:51:34,113 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 17:51:34,113 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 17:51:34 CST 2025]
2025-08-02 17:51:34,296 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 17:51:34,970 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.033984375
2025-08-02 17:51:44,966 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 2.04755859375
2025-08-02 17:51:54,965 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 2.003369140625
2025-08-02 17:52:04,971 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.726708984375
2025-08-02 17:52:14,967 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.498681640625
2025-08-02 17:52:24,969 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.28359375
2025-08-02 17:52:34,973 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.116943359375
2025-08-02 17:53:24,979 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.32060546875
2025-08-02 17:53:34,969 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.33212890625
2025-08-02 17:53:44,965 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.188134765625
2025-08-02 17:53:54,969 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.050244140625
2025-08-02 17:54:43,246 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.292 s, CPU [user: 0.00232 s, system: 0.00638 s], Allocated memory: 129.8 kB, transactions: 0, PullingJob: 0.251 s [100% collectChanges (1x)] (1x), svn: 0.24 s [100% getLatestRevision (1x)] (1x)
2025-08-02 17:54:54,970 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.25166015625
2025-08-02 17:55:04,966 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.120068359375
2025-08-02 18:01:33,728 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 18:01:33,735 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 18:01:33,736 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 18:01:33 CST 2025]
2025-08-02 18:01:33,745 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 18:01:33,746 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,416.49 GB
 [Sat Aug 02 18:01:33 CST 2025]
2025-08-02 18:01:33,911 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 18:01:33,911 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 18:01:33 CST 2025]
2025-08-02 18:01:33,914 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 18:01:33,914 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 16; Time: 0.16 s.
 [Sat Aug 02 18:01:33 CST 2025]
2025-08-02 18:01:33,916 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 18:01:33,916 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 18:01:33 CST 2025]
2025-08-02 18:01:34,023 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 18:01:34,024 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 18:01:34 CST 2025]
2025-08-02 18:01:34,154 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 18:11:33,736 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 18:11:33,739 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 18:11:33,740 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 18:11:33 CST 2025]
2025-08-02 18:11:33,758 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 18:11:33,758 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,416.38 GB
 [Sat Aug 02 18:11:33 CST 2025]
2025-08-02 18:11:33,947 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 18:11:33,947 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 18:11:33 CST 2025]
2025-08-02 18:11:33,949 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 18:11:33,949 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 16; Time: 0.16 s.
 [Sat Aug 02 18:11:33 CST 2025]
2025-08-02 18:11:33,951 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 18:11:33,951 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 18:11:33 CST 2025]
2025-08-02 18:11:34,057 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 18:11:34,057 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 18:11:34 CST 2025]
2025-08-02 18:11:34,189 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 18:21:33,745 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 18:21:33,747 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 18:21:33,748 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 18:21:33 CST 2025]
2025-08-02 18:21:33,757 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 18:21:33,758 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,416.12 GB
 [Sat Aug 02 18:21:33 CST 2025]
2025-08-02 18:21:33,945 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 18:21:33,945 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 18:21:33 CST 2025]
2025-08-02 18:21:33,947 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 18:21:33,947 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 16; Time: 0.16 s.
 [Sat Aug 02 18:21:33 CST 2025]
2025-08-02 18:21:33,949 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 18:21:33,949 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 18:21:33 CST 2025]
2025-08-02 18:21:34,069 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 18:21:34,069 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 18:21:34 CST 2025]
2025-08-02 18:21:34,200 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 18:31:33,760 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 18:31:33,762 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 18:31:33,763 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 18:31:33 CST 2025]
2025-08-02 18:31:33,778 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 18:31:33,778 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,416.2 GB
 [Sat Aug 02 18:31:33 CST 2025]
2025-08-02 18:31:33,954 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 18:31:33,954 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 18:31:33 CST 2025]
2025-08-02 18:31:33,956 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 18:31:33,956 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 17; Time: 0.217 s.
 [Sat Aug 02 18:31:33 CST 2025]
2025-08-02 18:31:33,958 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 18:31:33,958 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 18:31:33 CST 2025]
2025-08-02 18:31:34,065 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 18:31:34,065 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 18:31:34 CST 2025]
2025-08-02 18:31:34,197 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 18:41:33,773 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 18:41:33,776 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 18:41:33,778 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 18:41:33 CST 2025]
2025-08-02 18:41:33,792 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 18:41:33,793 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,416.04 GB
 [Sat Aug 02 18:41:33 CST 2025]
2025-08-02 18:41:34,002 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 18:41:34,002 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 18:41:34 CST 2025]
2025-08-02 18:41:34,005 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 18:41:34,005 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 17; Time: 0.217 s.
 [Sat Aug 02 18:41:34 CST 2025]
2025-08-02 18:41:34,006 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 18:41:34,006 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 18:41:34 CST 2025]
2025-08-02 18:41:34,125 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 18:41:34,126 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 18:41:34 CST 2025]
2025-08-02 18:41:34,258 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 18:44:24,994 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.326513671875
2025-08-02 18:44:34,987 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.138525390625
2025-08-02 18:44:44,986 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.00283203125
2025-08-02 18:51:33,782 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 18:51:33,785 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 18:51:33,785 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 18:51:33 CST 2025]
2025-08-02 18:51:33,795 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 18:51:33,795 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,415.97 GB
 [Sat Aug 02 18:51:33 CST 2025]
2025-08-02 18:51:33,968 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 18:51:33,969 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 18:51:33 CST 2025]
2025-08-02 18:51:33,971 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 18:51:33,971 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 17; Time: 0.217 s.
 [Sat Aug 02 18:51:33 CST 2025]
2025-08-02 18:51:33,973 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 18:51:33,973 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 18:51:33 CST 2025]
2025-08-02 18:51:34,085 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 18:51:34,086 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 18:51:34 CST 2025]
2025-08-02 18:51:34,219 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 19:01:33,790 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 19:01:33,793 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 19:01:33,794 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 19:01:33 CST 2025]
2025-08-02 19:01:33,823 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 19:01:33,824 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,415.94 GB
 [Sat Aug 02 19:01:33 CST 2025]
2025-08-02 19:01:34,025 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 19:01:34,025 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 19:01:34 CST 2025]
2025-08-02 19:01:34,027 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 19:01:34,027 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 17; Time: 0.217 s.
 [Sat Aug 02 19:01:34 CST 2025]
2025-08-02 19:01:34,029 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 19:01:34,029 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 19:01:34 CST 2025]
2025-08-02 19:01:34,144 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 19:01:34,144 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 19:01:34 CST 2025]
2025-08-02 19:01:34,274 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 19:11:33,805 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 19:11:33,809 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 19:11:33,810 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 19:11:33 CST 2025]
2025-08-02 19:11:33,829 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 19:11:33,829 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,415.66 GB
 [Sat Aug 02 19:11:33 CST 2025]
2025-08-02 19:11:34,048 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 19:11:34,048 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 19:11:34 CST 2025]
2025-08-02 19:11:34,050 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 19:11:34,050 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 17; Time: 0.217 s.
 [Sat Aug 02 19:11:34 CST 2025]
2025-08-02 19:11:34,051 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 19:11:34,051 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 19:11:34 CST 2025]
2025-08-02 19:11:34,157 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 19:11:34,158 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 19:11:34 CST 2025]
2025-08-02 19:11:34,286 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 19:16:05,034 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.23 s, CPU [user: 0.0016 s, system: 0.00568 s], Allocated memory: 130.2 kB, transactions: 0, PullingJob: 0.0777 s [100% collectChanges (1x)] (1x), svn: 0.0316 s [100% getLatestRevision (1x)] (1x)
2025-08-02 19:16:15,004 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.76962890625
2025-08-02 19:16:25,004 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.844091796875
2025-08-02 19:16:35,001 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.742431640625
2025-08-02 19:16:45,006 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.560009765625
2025-08-02 19:16:55,007 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.365576171875
2025-08-02 19:17:04,999 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.202978515625
2025-08-02 19:17:14,999 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.0474609375
2025-08-02 19:18:45,000 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.211474609375
2025-08-02 19:18:55,000 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.078662109375
2025-08-02 19:19:05,002 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.0162109375
2025-08-02 19:21:33,817 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 19:21:33,819 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 19:21:33,820 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 19:21:33 CST 2025]
2025-08-02 19:21:33,829 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 19:21:33,829 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,414.47 GB
 [Sat Aug 02 19:21:33 CST 2025]
2025-08-02 19:21:34,004 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 19:21:34,004 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 19:21:34 CST 2025]
2025-08-02 19:21:34,006 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 19:21:34,006 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 18; Time: 0.293 s.
 [Sat Aug 02 19:21:34 CST 2025]
2025-08-02 19:21:34,008 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 19:21:34,008 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 19:21:34 CST 2025]
2025-08-02 19:21:34,119 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 19:21:34,120 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 19:21:34 CST 2025]
2025-08-02 19:21:34,254 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 19:21:41,499 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.104 s, CPU [user: 0.00226 s, system: 0.0141 s], Allocated memory: 129.7 kB, transactions: 0, PullingJob: 0.0912 s [100% collectChanges (1x)] (1x), svn: 0.0858 s [100% getLatestRevision (1x)] (1x)
2025-08-02 19:22:01,994 [ajp-nio-127.0.0.1-8889-exec-3 | cID:6a84735d-7f000001-7115e771-560a38d6] WARN  com.polarion.psvn.launcher.SystemErrLogger - Something is written to System.err, the original message follows.
com.polarion.core.util.ThrowableUtils$StackTraceException: null
	at com.polarion.core.util.ThrowableUtils.getTrace(ThrowableUtils.java:109) ~[util.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.log(SystemErrLogger.java:264) [launcher.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.println(SystemErrLogger.java:108) [launcher.jar:?]
	at com.checklist.repository.BaseJsonFileRepository.findAll(BaseJsonFileRepository.java:318) [com.fasnote.alm.checklist/:?]
	at com.checklist.repository.BaseJsonFileRepository.findBy(BaseJsonFileRepository.java:428) [com.fasnote.alm.checklist/:?]
	at com.checklist.repository.ChecklistTemplateRepository.findByType(ChecklistTemplateRepository.java:58) [com.fasnote.alm.checklist/:?]
	at com.checklist.service.ChecklistTemplateService.getTemplatesByType(ChecklistTemplateService.java:162) [com.fasnote.alm.checklist/:?]
	at com.checklist.controller.ChecklistTemplateController.getTemplatesByType(ChecklistTemplateController.java:65) [com.fasnote.alm.checklist/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634) [javax.servlet_4.0.0.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.SecurityCheckFilter.doFilter(SecurityCheckFilter.java:46) [portal-tomcat.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-02 19:22:02,007 [ajp-nio-127.0.0.1-8889-exec-3 | cID:6a84735d-7f000001-7115e771-560a38d6] WARN  com.polarion.psvn.launcher.SystemErrLogger - 读取文件失败: /Users/<USER>/eclipse-workspace/com.fasnote.alm.checklist/data/templates/template_6c4b41a2ccd946f0b769317e89e5c5ca.json, 错误: null
2025-08-02 19:22:15,004 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.073583984375
2025-08-02 19:24:25,002 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.5044921875
2025-08-02 19:24:35,003 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.3279296875
2025-08-02 19:24:45,005 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.237451171875
2025-08-02 19:24:55,004 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.13076171875
2025-08-02 19:25:05,002 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.002978515625
2025-08-02 19:29:47,404 [ajp-nio-127.0.0.1-8889-exec-3 | cID:6a8b8d76-7f000001-7115e771-c85ef670] WARN  com.polarion.psvn.launcher.SystemErrLogger - Something is written to System.err, the original message follows.
com.polarion.core.util.ThrowableUtils$StackTraceException: null
	at com.polarion.core.util.ThrowableUtils.getTrace(ThrowableUtils.java:109) ~[util.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.log(SystemErrLogger.java:264) [launcher.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.println(SystemErrLogger.java:108) [launcher.jar:?]
	at com.checklist.repository.BaseJsonFileRepository.findAll(BaseJsonFileRepository.java:318) [com.fasnote.alm.checklist/:?]
	at com.checklist.repository.BaseJsonFileRepository.findBy(BaseJsonFileRepository.java:428) [com.fasnote.alm.checklist/:?]
	at com.checklist.repository.ChecklistTemplateRepository.findByType(ChecklistTemplateRepository.java:58) [com.fasnote.alm.checklist/:?]
	at com.checklist.service.ChecklistTemplateService.getTemplatesByType(ChecklistTemplateService.java:162) [com.fasnote.alm.checklist/:?]
	at com.checklist.controller.ChecklistTemplateController.getTemplatesByType(ChecklistTemplateController.java:65) [com.fasnote.alm.checklist/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634) [javax.servlet_4.0.0.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.SecurityCheckFilter.doFilter(SecurityCheckFilter.java:46) [portal-tomcat.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-02 19:29:47,409 [ajp-nio-127.0.0.1-8889-exec-3 | cID:6a8b8d76-7f000001-7115e771-c85ef670] WARN  com.polarion.psvn.launcher.SystemErrLogger - 读取文件失败: /Users/<USER>/eclipse-workspace/com.fasnote.alm.checklist/data/templates/template_6c4b41a2ccd946f0b769317e89e5c5ca.json, 错误: null
2025-08-02 19:30:21,320 [ajp-nio-127.0.0.1-8889-exec-8 | cID:6a8c1203-7f000001-7115e771-0c6a6b4f] WARN  com.polarion.psvn.launcher.SystemErrLogger - Something is written to System.err, the original message follows.
com.polarion.core.util.ThrowableUtils$StackTraceException: null
	at com.polarion.core.util.ThrowableUtils.getTrace(ThrowableUtils.java:109) ~[util.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.log(SystemErrLogger.java:264) [launcher.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.println(SystemErrLogger.java:108) [launcher.jar:?]
	at com.checklist.repository.BaseJsonFileRepository.findAll(BaseJsonFileRepository.java:318) [com.fasnote.alm.checklist/:?]
	at com.checklist.repository.BaseJsonFileRepository.findBy(BaseJsonFileRepository.java:428) [com.fasnote.alm.checklist/:?]
	at com.checklist.repository.ChecklistTemplateRepository.findByType(ChecklistTemplateRepository.java:58) [com.fasnote.alm.checklist/:?]
	at com.checklist.service.ChecklistTemplateService.getTemplatesByType(ChecklistTemplateService.java:162) [com.fasnote.alm.checklist/:?]
	at com.checklist.controller.ChecklistTemplateController.getTemplatesByType(ChecklistTemplateController.java:65) [com.fasnote.alm.checklist/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634) [javax.servlet_4.0.0.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.SecurityCheckFilter.doFilter(SecurityCheckFilter.java:46) [portal-tomcat.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-02 19:30:21,321 [ajp-nio-127.0.0.1-8889-exec-8 | cID:6a8c1203-7f000001-7115e771-0c6a6b4f] WARN  com.polarion.psvn.launcher.SystemErrLogger - 读取文件失败: /Users/<USER>/eclipse-workspace/com.fasnote.alm.checklist/data/templates/template-1.json, 错误: null
2025-08-02 19:31:33,825 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 19:31:33,830 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 19:31:33,835 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 19:31:33 CST 2025]
2025-08-02 19:31:33,855 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 19:31:33,855 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,416.06 GB
 [Sat Aug 02 19:31:33 CST 2025]
2025-08-02 19:31:34,043 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 19:31:34,043 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 19:31:34 CST 2025]
2025-08-02 19:31:34,047 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 19:31:34,047 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 18; Time: 0.293 s.
 [Sat Aug 02 19:31:34 CST 2025]
2025-08-02 19:31:34,049 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 19:31:34,049 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 19:31:34 CST 2025]
2025-08-02 19:31:34,166 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 19:31:34,167 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 19:31:34 CST 2025]
2025-08-02 19:31:34,345 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 19:33:38,275 [ajp-nio-127.0.0.1-8889-exec-3 | cID:6a8f135e-7f000001-7115e771-3d61a5d7] WARN  com.polarion.psvn.launcher.SystemErrLogger - Something is written to System.err, the original message follows.
com.polarion.core.util.ThrowableUtils$StackTraceException: null
	at com.polarion.core.util.ThrowableUtils.getTrace(ThrowableUtils.java:109) ~[util.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.log(SystemErrLogger.java:264) [launcher.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.println(SystemErrLogger.java:108) [launcher.jar:?]
	at com.checklist.repository.BaseJsonFileRepository.findAll(BaseJsonFileRepository.java:318) [com.fasnote.alm.checklist/:?]
	at com.checklist.repository.BaseJsonFileRepository.findBy(BaseJsonFileRepository.java:428) [com.fasnote.alm.checklist/:?]
	at com.checklist.repository.ChecklistTemplateRepository.findByType(ChecklistTemplateRepository.java:58) [com.fasnote.alm.checklist/:?]
	at com.checklist.service.ChecklistTemplateService.getTemplatesByType(ChecklistTemplateService.java:162) [com.fasnote.alm.checklist/:?]
	at com.checklist.controller.ChecklistTemplateController.getTemplatesByType(ChecklistTemplateController.java:65) [com.fasnote.alm.checklist/:?]
	at jdk.internal.reflect.GeneratedMethodAccessor311.invoke(Unknown Source) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634) [javax.servlet_4.0.0.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.SecurityCheckFilter.doFilter(SecurityCheckFilter.java:46) [portal-tomcat.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-02 19:33:38,337 [ajp-nio-127.0.0.1-8889-exec-3 | cID:6a8f135e-7f000001-7115e771-3d61a5d7] WARN  com.polarion.psvn.launcher.SystemErrLogger - 读取文件失败: /Users/<USER>/eclipse-workspace/com.fasnote.alm.checklist/data/templates/template_6c4b41a2ccd946f0b769317e89e5c5ca.json, 错误: null
2025-08-02 19:35:21,528 [ajp-nio-127.0.0.1-8889-exec-4 | cID:6a90a642-7f000001-7115e771-24426073] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/template-config/template_6c4b41a2ccd946f0b769317e89e5c5ca': Total: 0.112 s, CPU [user: 0.0144 s, system: 0.0475 s], Allocated memory: 647.5 kB, transactions: 0
2025-08-02 19:37:45,893 [ajp-nio-127.0.0.1-8889-exec-6 | cID:6a92da9d-7f000001-7115e771-f9e4454f] WARN  com.polarion.psvn.launcher.SystemErrLogger - Something is written to System.err, the original message follows.
com.polarion.core.util.ThrowableUtils$StackTraceException: null
	at com.polarion.core.util.ThrowableUtils.getTrace(ThrowableUtils.java:109) ~[util.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.log(SystemErrLogger.java:264) [launcher.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.println(SystemErrLogger.java:108) [launcher.jar:?]
	at com.checklist.repository.BaseJsonFileRepository.findAll(BaseJsonFileRepository.java:318) [com.fasnote.alm.checklist/:?]
	at com.checklist.repository.BaseJsonFileRepository.findBy(BaseJsonFileRepository.java:428) [com.fasnote.alm.checklist/:?]
	at com.checklist.repository.ChecklistTemplateRepository.findByType(ChecklistTemplateRepository.java:58) [com.fasnote.alm.checklist/:?]
	at com.checklist.service.ChecklistTemplateService.getTemplatesByType(ChecklistTemplateService.java:162) [com.fasnote.alm.checklist/:?]
	at com.checklist.controller.ChecklistTemplateController.getTemplatesByType(ChecklistTemplateController.java:65) [com.fasnote.alm.checklist/:?]
	at jdk.internal.reflect.GeneratedMethodAccessor311.invoke(Unknown Source) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634) [javax.servlet_4.0.0.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.SecurityCheckFilter.doFilter(SecurityCheckFilter.java:46) [portal-tomcat.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-02 19:37:45,918 [ajp-nio-127.0.0.1-8889-exec-6 | cID:6a92da9d-7f000001-7115e771-f9e4454f] WARN  com.polarion.psvn.launcher.SystemErrLogger - 读取文件失败: /Users/<USER>/eclipse-workspace/com.fasnote.alm.checklist/data/templates/template_6c4b41a2ccd946f0b769317e89e5c5ca.json, 错误: null
2025-08-02 19:39:45,012 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.162646484375
2025-08-02 19:39:55,011 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.022607421875
2025-08-02 19:41:33,832 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 19:41:33,837 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 19:41:33,838 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 19:41:33 CST 2025]
2025-08-02 19:41:33,853 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 19:41:33,854 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,415.9 GB
 [Sat Aug 02 19:41:33 CST 2025]
2025-08-02 19:41:34,033 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 19:41:34,033 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 19:41:34 CST 2025]
2025-08-02 19:41:34,035 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 19:41:34,035 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 18; Time: 0.293 s.
 [Sat Aug 02 19:41:34 CST 2025]
2025-08-02 19:41:34,042 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 19:41:34,042 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 19:41:34 CST 2025]
2025-08-02 19:41:34,151 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 19:41:34,151 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 19:41:34 CST 2025]
2025-08-02 19:41:34,284 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 19:49:36,547 [ajp-nio-127.0.0.1-8889-exec-8 | cID:6a9db296-7f000001-7115e771-382e7d74] WARN  com.polarion.psvn.launcher.SystemErrLogger - Something is written to System.err, the original message follows.
com.polarion.core.util.ThrowableUtils$StackTraceException: null
	at com.polarion.core.util.ThrowableUtils.getTrace(ThrowableUtils.java:109) ~[util.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.log(SystemErrLogger.java:264) [launcher.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.println(SystemErrLogger.java:108) [launcher.jar:?]
	at com.checklist.repository.BaseJsonFileRepository.findAll(BaseJsonFileRepository.java:318) [com.fasnote.alm.checklist/:?]
	at com.checklist.service.ChecklistTemplateService.getAllTemplates(ChecklistTemplateService.java:147) [com.fasnote.alm.checklist/:?]
	at com.checklist.controller.ChecklistTemplateController.getAllTemplates(ChecklistTemplateController.java:37) [com.fasnote.alm.checklist/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634) [javax.servlet_4.0.0.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.SecurityCheckFilter.doFilter(SecurityCheckFilter.java:46) [portal-tomcat.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-02 19:49:36,560 [ajp-nio-127.0.0.1-8889-exec-8 | cID:6a9db296-7f000001-7115e771-382e7d74] WARN  com.polarion.psvn.launcher.SystemErrLogger - 读取文件失败: /Users/<USER>/eclipse-workspace/com.fasnote.alm.checklist/data/templates/template_6c4b41a2ccd946f0b769317e89e5c5ca.json, 错误: null
2025-08-02 19:51:33,841 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 19:51:33,844 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 19:51:33,845 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 19:51:33 CST 2025]
2025-08-02 19:51:33,858 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 19:51:33,858 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,415.07 GB
 [Sat Aug 02 19:51:33 CST 2025]
2025-08-02 19:51:34,034 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 19:51:34,034 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 19:51:34 CST 2025]
2025-08-02 19:51:34,035 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 19:51:34,035 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 18; Time: 0.293 s.
 [Sat Aug 02 19:51:34 CST 2025]
2025-08-02 19:51:34,037 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 19:51:34,037 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 19:51:34 CST 2025]
2025-08-02 19:51:34,149 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 19:51:34,149 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 19:51:34 CST 2025]
2025-08-02 19:51:34,275 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 19:52:29,425 [ajp-nio-127.0.0.1-8889-exec-1 | cID:6aa055b2-7f000001-7115e771-c3d2f3e9] WARN  com.polarion.psvn.launcher.SystemErrLogger - Something is written to System.err, the original message follows.
com.polarion.core.util.ThrowableUtils$StackTraceException: null
	at com.polarion.core.util.ThrowableUtils.getTrace(ThrowableUtils.java:109) ~[util.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.log(SystemErrLogger.java:264) [launcher.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.println(SystemErrLogger.java:108) [launcher.jar:?]
	at com.checklist.repository.BaseJsonFileRepository.findAll(BaseJsonFileRepository.java:318) [com.fasnote.alm.checklist/:?]
	at com.checklist.repository.BaseJsonFileRepository.findBy(BaseJsonFileRepository.java:428) [com.fasnote.alm.checklist/:?]
	at com.checklist.repository.ChecklistTemplateRepository.findByType(ChecklistTemplateRepository.java:58) [com.fasnote.alm.checklist/:?]
	at com.checklist.service.ChecklistTemplateService.getTemplatesByType(ChecklistTemplateService.java:162) [com.fasnote.alm.checklist/:?]
	at com.checklist.controller.ChecklistTemplateController.getTemplatesByType(ChecklistTemplateController.java:65) [com.fasnote.alm.checklist/:?]
	at jdk.internal.reflect.GeneratedMethodAccessor311.invoke(Unknown Source) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634) [javax.servlet_4.0.0.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.SecurityCheckFilter.doFilter(SecurityCheckFilter.java:46) [portal-tomcat.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-02 19:52:29,443 [ajp-nio-127.0.0.1-8889-exec-1 | cID:6aa055b2-7f000001-7115e771-c3d2f3e9] WARN  com.polarion.psvn.launcher.SystemErrLogger - 读取文件失败: /Users/<USER>/eclipse-workspace/com.fasnote.alm.checklist/data/templates/template_6c4b41a2ccd946f0b769317e89e5c5ca.json, 错误: null
2025-08-02 19:55:56,657 [ajp-nio-127.0.0.1-8889-exec-6 | cID:6aa37f58-7f000001-7115e771-3d32fbb6] WARN  com.polarion.psvn.launcher.SystemErrLogger - Something is written to System.err, the original message follows.
com.polarion.core.util.ThrowableUtils$StackTraceException: null
	at com.polarion.core.util.ThrowableUtils.getTrace(ThrowableUtils.java:109) ~[util.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.log(SystemErrLogger.java:264) [launcher.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.println(SystemErrLogger.java:108) [launcher.jar:?]
	at com.checklist.repository.BaseJsonFileRepository.findAll(BaseJsonFileRepository.java:318) [com.fasnote.alm.checklist/:?]
	at com.checklist.repository.BaseJsonFileRepository.findBy(BaseJsonFileRepository.java:428) [com.fasnote.alm.checklist/:?]
	at com.checklist.repository.ChecklistTemplateRepository.findByType(ChecklistTemplateRepository.java:58) [com.fasnote.alm.checklist/:?]
	at com.checklist.service.ChecklistTemplateService.getTemplatesByType(ChecklistTemplateService.java:162) [com.fasnote.alm.checklist/:?]
	at com.checklist.controller.ChecklistTemplateController.getTemplatesByType(ChecklistTemplateController.java:65) [com.fasnote.alm.checklist/:?]
	at jdk.internal.reflect.GeneratedMethodAccessor311.invoke(Unknown Source) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634) [javax.servlet_4.0.0.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.SecurityCheckFilter.doFilter(SecurityCheckFilter.java:46) [portal-tomcat.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-02 19:55:56,675 [ajp-nio-127.0.0.1-8889-exec-6 | cID:6aa37f58-7f000001-7115e771-3d32fbb6] WARN  com.polarion.psvn.launcher.SystemErrLogger - 读取文件失败: /Users/<USER>/eclipse-workspace/com.fasnote.alm.checklist/data/templates/template_6c4b41a2ccd946f0b769317e89e5c5ca.json, 错误: null
2025-08-02 19:56:29,894 [ajp-nio-127.0.0.1-8889-exec-9 | cID:6aa4012a-7f000001-7115e771-7fd7da18] WARN  com.polarion.psvn.launcher.SystemErrLogger - Something is written to System.err, the original message follows.
com.polarion.core.util.ThrowableUtils$StackTraceException: null
	at com.polarion.core.util.ThrowableUtils.getTrace(ThrowableUtils.java:109) ~[util.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.log(SystemErrLogger.java:264) [launcher.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.println(SystemErrLogger.java:108) [launcher.jar:?]
	at com.checklist.repository.BaseJsonFileRepository.findAll(BaseJsonFileRepository.java:318) [com.fasnote.alm.checklist/:?]
	at com.checklist.service.ChecklistTemplateService.getAllTemplates(ChecklistTemplateService.java:147) [com.fasnote.alm.checklist/:?]
	at com.checklist.controller.ChecklistTemplateController.getAllTemplates(ChecklistTemplateController.java:37) [com.fasnote.alm.checklist/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634) [javax.servlet_4.0.0.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.SecurityCheckFilter.doFilter(SecurityCheckFilter.java:46) [portal-tomcat.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-02 19:56:29,900 [ajp-nio-127.0.0.1-8889-exec-9 | cID:6aa4012a-7f000001-7115e771-7fd7da18] WARN  com.polarion.psvn.launcher.SystemErrLogger - 读取文件失败: /Users/<USER>/eclipse-workspace/com.fasnote.alm.checklist/data/templates/template_6c4b41a2ccd946f0b769317e89e5c5ca.json, 错误: null
2025-08-02 20:01:33,850 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 20:01:33,852 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 20:01:33,853 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 20:01:33 CST 2025]
2025-08-02 20:01:33,866 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 20:01:33,867 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,415.05 GB
 [Sat Aug 02 20:01:33 CST 2025]
2025-08-02 20:01:34,057 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 20:01:34,057 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 20:01:34 CST 2025]
2025-08-02 20:01:34,059 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 20:01:34,059 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 18; Time: 0.293 s.
 [Sat Aug 02 20:01:34 CST 2025]
2025-08-02 20:01:34,061 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 20:01:34,061 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 20:01:34 CST 2025]
2025-08-02 20:01:34,187 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 20:01:34,187 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 20:01:34 CST 2025]
2025-08-02 20:01:34,348 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 20:06:32,330 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.104 s, CPU [user: 0.00187 s, system: 0.0115 s], Allocated memory: 130.1 kB, transactions: 0, PullingJob: 0.0905 s [100% collectChanges (1x)] (1x), svn: 0.0781 s [100% getLatestRevision (1x)] (1x)
2025-08-02 20:07:42,195 [ajp-nio-127.0.0.1-8889-exec-5 | cID:6aae436e-7f000001-7115e771-7b9251ff] WARN  com.polarion.psvn.launcher.SystemErrLogger - Something is written to System.err, the original message follows.
com.polarion.core.util.ThrowableUtils$StackTraceException: null
	at com.polarion.core.util.ThrowableUtils.getTrace(ThrowableUtils.java:109) ~[util.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.log(SystemErrLogger.java:264) [launcher.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.println(SystemErrLogger.java:108) [launcher.jar:?]
	at com.checklist.repository.BaseJsonFileRepository.findAll(BaseJsonFileRepository.java:318) [com.fasnote.alm.checklist/:?]
	at com.checklist.service.ChecklistTemplateService.getAllTemplates(ChecklistTemplateService.java:147) [com.fasnote.alm.checklist/:?]
	at com.checklist.controller.ChecklistTemplateController.getAllTemplates(ChecklistTemplateController.java:37) [com.fasnote.alm.checklist/:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634) [javax.servlet_4.0.0.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.SecurityCheckFilter.doFilter(SecurityCheckFilter.java:46) [portal-tomcat.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-02 20:07:42,199 [ajp-nio-127.0.0.1-8889-exec-5 | cID:6aae436e-7f000001-7115e771-7b9251ff] WARN  com.polarion.psvn.launcher.SystemErrLogger - 读取文件失败: /Users/<USER>/eclipse-workspace/com.fasnote.alm.checklist/data/templates/template_demo_code_review.json, 错误: null
2025-08-02 20:11:33,858 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 20:11:33,863 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 20:11:33,863 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 20:11:33 CST 2025]
2025-08-02 20:11:33,889 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 20:11:33,890 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,415.02 GB
 [Sat Aug 02 20:11:33 CST 2025]
2025-08-02 20:11:34,084 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 20:11:34,084 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 20:11:34 CST 2025]
2025-08-02 20:11:34,086 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 20:11:34,086 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 19; Time: 0.316 s.
 [Sat Aug 02 20:11:34 CST 2025]
2025-08-02 20:11:34,089 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 20:11:34,089 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 20:11:34 CST 2025]
2025-08-02 20:11:34,203 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 20:11:34,203 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 20:11:34 CST 2025]
2025-08-02 20:11:34,335 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 20:12:15,026 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.32392578125
2025-08-02 20:12:25,024 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.173828125
2025-08-02 20:12:35,021 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.13359375
2025-08-02 20:12:45,025 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.0201171875
2025-08-02 20:21:33,867 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-02 20:21:33,870 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-02 20:21:33,871 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:0
 [Sat Aug 02 20:21:33 CST 2025]
2025-08-02 20:21:33,883 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-02 20:21:33,883 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,415.01 GB
 [Sat Aug 02 20:21:33 CST 2025]
2025-08-02 20:21:34,071 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-02 20:21:34,071 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Sat Aug 02 20:21:34 CST 2025]
2025-08-02 20:21:34,073 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-02 20:21:34,073 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 19; Time: 0.316 s.
 [Sat Aug 02 20:21:34 CST 2025]
2025-08-02 20:21:34,075 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-02 20:21:34,075 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Sat Aug 02 20:21:34 CST 2025]
2025-08-02 20:21:34,186 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-02 20:21:34,186 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Sat Aug 02 20:21:34 CST 2025]
2025-08-02 20:21:34,327 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-02 20:29:06,891 [ajp-nio-127.0.0.1-8889-exec-6 | cID:6ac1ddb7-7f000001-7115e771-0e4525c1] WARN  com.polarion.psvn.launcher.SystemErrLogger - Something is written to System.err, the original message follows.
com.polarion.core.util.ThrowableUtils$StackTraceException: null
	at com.polarion.core.util.ThrowableUtils.getTrace(ThrowableUtils.java:109) ~[util.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.log(SystemErrLogger.java:264) [launcher.jar:?]
	at com.polarion.psvn.launcher.SystemErrLogger.println(SystemErrLogger.java:108) [launcher.jar:?]
	at com.checklist.repository.BaseJsonFileRepository.findAll(BaseJsonFileRepository.java:318) [com.fasnote.alm.checklist/:?]
	at com.checklist.repository.BaseJsonFileRepository.findBy(BaseJsonFileRepository.java:428) [com.fasnote.alm.checklist/:?]
	at com.checklist.repository.ChecklistTemplateRepository.findByType(ChecklistTemplateRepository.java:58) [com.fasnote.alm.checklist/:?]
	at com.checklist.service.ChecklistTemplateService.getTemplatesByType(ChecklistTemplateService.java:162) [com.fasnote.alm.checklist/:?]
	at com.checklist.controller.ChecklistTemplateController.getTemplatesByType(ChecklistTemplateController.java:65) [com.fasnote.alm.checklist/:?]
	at jdk.internal.reflect.GeneratedMethodAccessor311.invoke(Unknown Source) ~[?:?]
	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634) [javax.servlet_4.0.0.jar:?]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [org.springframework.spring-webmvc_5.2.10.RELEASE.jar:?]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741) [javax.servlet_4.0.0.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [org.springframework.spring-web_5.2.10.RELEASE.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at com.polarion.portal.tomcat.servlets.SecurityCheckFilter.doFilter(SecurityCheckFilter.java:46) [portal-tomcat.jar:?]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [catalina.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [catalina.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [catalina.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [catalina.jar:9.0.53]
	at org.apache.catalina.authenticator.SingleSignOn.invoke(SingleSignOn.java:261) [catalina.jar:9.0.53]
	at com.polarion.platform.session.PolarionLocalSingleSignOn.invoke(PolarionLocalSingleSignOn.java:164) [platform.jar:?]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [catalina.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [catalina.jar:9.0.53]
	at org.apache.coyote.ajp.AjpProcessor.service(AjpProcessor.java:433) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-coyote.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-coyote.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-util.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-util.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:829) [?:?]
2025-08-02 20:29:06,913 [ajp-nio-127.0.0.1-8889-exec-6 | cID:6ac1ddb7-7f000001-7115e771-0e4525c1] WARN  com.polarion.psvn.launcher.SystemErrLogger - 读取文件失败: /Users/<USER>/eclipse-workspace/com.fasnote.alm.checklist/data/templates/template_6c4b41a2ccd946f0b769317e89e5c5ca.json, 错误: null
2025-08-02 20:29:27,836 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.124 s, CPU [user: 0.00194 s, system: 0.00321 s], Allocated memory: 130.1 kB, transactions: 0, PullingJob: 0.0954 s [100% collectChanges (1x)] (1x), svn: 0.0954 s [100% getLatestRevision (1x)] (1x)
