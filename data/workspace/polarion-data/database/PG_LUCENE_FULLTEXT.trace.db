08-01 19:07:16 jdbc[4]: null
org.h2.message.DbException: The connection was not closed by the application and is garbage collected [90018-176]
	at org.h2.message.DbException.get(DbException.java:178)
	at org.h2.message.DbException.get(DbException.java:154)
	at org.h2.message.DbException.get(DbException.java:143)
	at org.h2.jdbc.JdbcConnection.closeOld(JdbcConnection.java:173)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:118)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:91)
	at org.h2.Driver.connect(Driver.java:74)
	at org.h2.jdbcx.JdbcDataSource.getJdbcConnection(JdbcDataSource.java:191)
	at org.h2.jdbcx.JdbcDataSource.getXAConnection(JdbcDataSource.java:354)
	at org.h2.jdbcx.JdbcDataSource.getPooledConnection(JdbcDataSource.java:386)
	at org.h2.jdbcx.JdbcConnectionPool.getConnectionNow(JdbcConnectionPool.java:228)
	at org.h2.jdbcx.JdbcConnectionPool.getConnection(JdbcConnectionPool.java:200)
	at com.polarion.platform.sql.internal.runtime.InternalH2DB.start(InternalH2DB.java:362)
	at com.polarion.platform.sql.internal.runtime.ConfiguredDB.start(ConfiguredDB.java:128)
	at com.polarion.platform.sql.internal.runtime.InternalPGDB.start(InternalPGDB.java:367)
	at com.polarion.platform.sql.internal.runtime.ConfiguredDB.start(ConfiguredDB.java:128)
	at com.polarion.platform.sql.internal.runtime.LoggingDB.start(LoggingDB.java:83)
	at com.polarion.platform.sql.internal.def.DBDef.create(DBDef.java:283)
	at com.polarion.platform.sql.internal.def.DBDef.run(DBDef.java:271)
	at com.polarion.platform.guice.internal.GuicePlatform.runInitializers(GuicePlatform.java:84)
	at com.polarion.platform.guice.internal.GuicePlatform.<init>(GuicePlatform.java:37)
	at com.polarion.psvn.launcher.internal.platform.PlatformService.start(PlatformService.java:90)
	at com.polarion.psvn.launcher.PolarionSVNApplication.runImpl(PolarionSVNApplication.java:139)
	at com.polarion.psvn.launcher.PolarionSVNApplication.run(PolarionSVNApplication.java:94)
	at com.polarion.core.boot.launchers.BasicAppLauncher.launch(BasicAppLauncher.java:53)
	at com.polarion.core.boot.impl.AppLaunchersManager.start(AppLaunchersManager.java:184)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:388)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:243)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:656)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:592)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1498)
	at org.eclipse.equinox.launcher.Main.main(Main.java:1471)
Caused by: org.h2.jdbc.JdbcSQLException: The connection was not closed by the application and is garbage collected [90018-176]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:344)
	... 39 more
08-01 19:21:32 jdbc[4]: null
org.h2.message.DbException: The connection was not closed by the application and is garbage collected [90018-176]
	at org.h2.message.DbException.get(DbException.java:178)
	at org.h2.message.DbException.get(DbException.java:154)
	at org.h2.message.DbException.get(DbException.java:143)
	at org.h2.jdbc.JdbcConnection.closeOld(JdbcConnection.java:173)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:118)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:91)
	at org.h2.Driver.connect(Driver.java:74)
	at org.h2.jdbcx.JdbcDataSource.getJdbcConnection(JdbcDataSource.java:191)
	at org.h2.jdbcx.JdbcDataSource.getXAConnection(JdbcDataSource.java:354)
	at org.h2.jdbcx.JdbcDataSource.getPooledConnection(JdbcDataSource.java:386)
	at org.h2.jdbcx.JdbcConnectionPool.getConnectionNow(JdbcConnectionPool.java:228)
	at org.h2.jdbcx.JdbcConnectionPool.getConnection(JdbcConnectionPool.java:200)
	at com.polarion.platform.sql.internal.runtime.InternalH2DB.start(InternalH2DB.java:362)
	at com.polarion.platform.sql.internal.runtime.ConfiguredDB.start(ConfiguredDB.java:128)
	at com.polarion.platform.sql.internal.runtime.InternalPGDB.start(InternalPGDB.java:367)
	at com.polarion.platform.sql.internal.runtime.ConfiguredDB.start(ConfiguredDB.java:128)
	at com.polarion.platform.sql.internal.runtime.LoggingDB.start(LoggingDB.java:83)
	at com.polarion.platform.sql.internal.def.DBDef.create(DBDef.java:283)
	at com.polarion.platform.sql.internal.def.DBDef.run(DBDef.java:271)
	at com.polarion.platform.guice.internal.GuicePlatform.runInitializers(GuicePlatform.java:84)
	at com.polarion.platform.guice.internal.GuicePlatform.<init>(GuicePlatform.java:37)
	at com.polarion.psvn.launcher.internal.platform.PlatformService.start(PlatformService.java:90)
	at com.polarion.psvn.launcher.PolarionSVNApplication.runImpl(PolarionSVNApplication.java:139)
	at com.polarion.psvn.launcher.PolarionSVNApplication.run(PolarionSVNApplication.java:94)
	at com.polarion.core.boot.launchers.BasicAppLauncher.launch(BasicAppLauncher.java:53)
	at com.polarion.core.boot.impl.AppLaunchersManager.start(AppLaunchersManager.java:184)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:388)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:243)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:656)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:592)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1498)
	at org.eclipse.equinox.launcher.Main.main(Main.java:1471)
Caused by: org.h2.jdbc.JdbcSQLException: The connection was not closed by the application and is garbage collected [90018-176]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:344)
	... 39 more
08-01 21:30:51 jdbc[4]: null
org.h2.message.DbException: The connection was not closed by the application and is garbage collected [90018-176]
	at org.h2.message.DbException.get(DbException.java:178)
	at org.h2.message.DbException.get(DbException.java:154)
	at org.h2.message.DbException.get(DbException.java:143)
	at org.h2.jdbc.JdbcConnection.closeOld(JdbcConnection.java:173)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:118)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:91)
	at org.h2.Driver.connect(Driver.java:74)
	at org.h2.jdbcx.JdbcDataSource.getJdbcConnection(JdbcDataSource.java:191)
	at org.h2.jdbcx.JdbcDataSource.getXAConnection(JdbcDataSource.java:354)
	at org.h2.jdbcx.JdbcDataSource.getPooledConnection(JdbcDataSource.java:386)
	at org.h2.jdbcx.JdbcConnectionPool.getConnectionNow(JdbcConnectionPool.java:228)
	at org.h2.jdbcx.JdbcConnectionPool.getConnection(JdbcConnectionPool.java:200)
	at com.polarion.platform.sql.internal.runtime.InternalH2DB.start(InternalH2DB.java:362)
	at com.polarion.platform.sql.internal.runtime.ConfiguredDB.start(ConfiguredDB.java:128)
	at com.polarion.platform.sql.internal.runtime.InternalPGDB.start(InternalPGDB.java:367)
	at com.polarion.platform.sql.internal.runtime.ConfiguredDB.start(ConfiguredDB.java:128)
	at com.polarion.platform.sql.internal.runtime.LoggingDB.start(LoggingDB.java:83)
	at com.polarion.platform.sql.internal.def.DBDef.create(DBDef.java:283)
	at com.polarion.platform.sql.internal.def.DBDef.run(DBDef.java:271)
	at com.polarion.platform.guice.internal.GuicePlatform.runInitializers(GuicePlatform.java:84)
	at com.polarion.platform.guice.internal.GuicePlatform.<init>(GuicePlatform.java:37)
	at com.polarion.psvn.launcher.internal.platform.PlatformService.start(PlatformService.java:90)
	at com.polarion.psvn.launcher.PolarionSVNApplication.runImpl(PolarionSVNApplication.java:139)
	at com.polarion.psvn.launcher.PolarionSVNApplication.run(PolarionSVNApplication.java:94)
	at com.polarion.core.boot.launchers.BasicAppLauncher.launch(BasicAppLauncher.java:53)
	at com.polarion.core.boot.impl.AppLaunchersManager.start(AppLaunchersManager.java:184)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:388)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:243)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:656)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:592)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1498)
	at org.eclipse.equinox.launcher.Main.main(Main.java:1471)
Caused by: org.h2.jdbc.JdbcSQLException: The connection was not closed by the application and is garbage collected [90018-176]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:344)
	... 39 more
08-02 13:28:39 jdbc[4]: null
org.h2.message.DbException: The connection was not closed by the application and is garbage collected [90018-176]
	at org.h2.message.DbException.get(DbException.java:178)
	at org.h2.message.DbException.get(DbException.java:154)
	at org.h2.message.DbException.get(DbException.java:143)
	at org.h2.jdbc.JdbcConnection.closeOld(JdbcConnection.java:173)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:118)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:91)
	at org.h2.Driver.connect(Driver.java:74)
	at org.h2.jdbcx.JdbcDataSource.getJdbcConnection(JdbcDataSource.java:191)
	at org.h2.jdbcx.JdbcDataSource.getXAConnection(JdbcDataSource.java:354)
	at org.h2.jdbcx.JdbcDataSource.getPooledConnection(JdbcDataSource.java:386)
	at org.h2.jdbcx.JdbcConnectionPool.getConnectionNow(JdbcConnectionPool.java:228)
	at org.h2.jdbcx.JdbcConnectionPool.getConnection(JdbcConnectionPool.java:200)
	at com.polarion.platform.sql.internal.runtime.InternalH2DB.start(InternalH2DB.java:362)
	at com.polarion.platform.sql.internal.runtime.ConfiguredDB.start(ConfiguredDB.java:128)
	at com.polarion.platform.sql.internal.runtime.InternalPGDB.start(InternalPGDB.java:367)
	at com.polarion.platform.sql.internal.runtime.ConfiguredDB.start(ConfiguredDB.java:128)
	at com.polarion.platform.sql.internal.runtime.LoggingDB.start(LoggingDB.java:83)
	at com.polarion.platform.sql.internal.def.DBDef.create(DBDef.java:283)
	at com.polarion.platform.sql.internal.def.DBDef.run(DBDef.java:271)
	at com.polarion.platform.guice.internal.GuicePlatform.runInitializers(GuicePlatform.java:84)
	at com.polarion.platform.guice.internal.GuicePlatform.<init>(GuicePlatform.java:37)
	at com.polarion.psvn.launcher.internal.platform.PlatformService.start(PlatformService.java:90)
	at com.polarion.psvn.launcher.PolarionSVNApplication.runImpl(PolarionSVNApplication.java:139)
	at com.polarion.psvn.launcher.PolarionSVNApplication.run(PolarionSVNApplication.java:94)
	at com.polarion.core.boot.launchers.BasicAppLauncher.launch(BasicAppLauncher.java:53)
	at com.polarion.core.boot.impl.AppLaunchersManager.start(AppLaunchersManager.java:184)
	at org.eclipse.equinox.internal.app.EclipseAppHandle.run(EclipseAppHandle.java:196)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.runApplication(EclipseAppLauncher.java:134)
	at org.eclipse.core.runtime.internal.adaptor.EclipseAppLauncher.start(EclipseAppLauncher.java:104)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:388)
	at org.eclipse.core.runtime.adaptor.EclipseStarter.run(EclipseStarter.java:243)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.eclipse.equinox.launcher.Main.invokeFramework(Main.java:656)
	at org.eclipse.equinox.launcher.Main.basicRun(Main.java:592)
	at org.eclipse.equinox.launcher.Main.run(Main.java:1498)
	at org.eclipse.equinox.launcher.Main.main(Main.java:1471)
Caused by: org.h2.jdbc.JdbcSQLException: The connection was not closed by the application and is garbage collected [90018-176]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:344)
	... 39 more
